import pytest
import numpy as np
from app.models.decoder_Worker import DecoderWorker


# TODO: 需檢查是否與資料庫欄位一致
def _create_test_tool_data():
    """tare=0; Linear=1; Lc=0.1; Hl=0.1; Kl=0.05"""
    return {
        'toolname': 'TestTool',
        'toolip': '*************', 
        'toolmac': '00:11:22:33:44:55',
        'sample_rate': 10000,
        'tare_xv': 0.0,
        'tare_yv': 0.0, 
        'tare_zv': 0.0,
        'tare_tv': 0.0,
        'tare_gx': 1.0, # if set as 0, will be auto set to average of ADXL_X
        'tare_gy': 1.0,
        'tare_gz': 1.0,
        # 電壓到力的轉換係數
        'Linear_x': 1.0,
        'Linear_y': 1.0,
        'Linear_z': 1.0,
        'Linear_t': 1.0,
        # 刀具幾何參數
        'Lc': 0.1,
        'Hl': 0.1,
        'Kl': 0.05,
        'Bx_COMP': 0.0,
        'By_COMP': 0.0,
        'Bz_COMP': 0.0,
        'Bt_COMP': 0.0,
        'CO2_id': None,
        # 自動錄製參數
        'auto_record_enabled': 0,
        'auto_pre_record_seconds': 0,
        'auto_record_seconds': 10,
        'auto_max_record_count': 100,
        'auto_cf_threshold': 0.0,
        'auto_fz_threshold': 0.0,
        'auto_t_threshold': 0.0,
        'auto_cf_enabled': 0,
        'auto_fz_enabled': 0,
        'auto_t_enabled': 0
    }

def _generate_one_low_freq_hex_data():
    """ADXL data=1200(04b0); temperature&wifi=25(19); state=1; MAC=c049ef68aa99"""
    hex_data = "04b004b004b019001901c049ef68aa99"
    return hex_data

def _generate_one_high_freq_hex_data():
    """all data is 16000(3E80)"""
    hex_data = "3e803e803e803e803e803e803e803e80"
    return hex_data

def _generate_n_hex_data(sample_n = 800):
    """
    All high-freqdata is 16000(3E80).
    ADXL data=1200(04b0); temperature&wifi=25(19); state=1; MAC=c049ef68aa99
    """
    hex_data = ""
    for i in range(sample_n):
        if i % 201 == 0:
            hex_data += _generate_one_low_freq_hex_data()
        else:
            hex_data += _generate_one_high_freq_hex_data()
    return hex_data



#TODO: assert不完整，需補充
def test_DecoderWorker_socket_decoder_init():
    test_tool_data = _create_test_tool_data()

    decoder = DecoderWorker(test_tool_data)

    assert isinstance(decoder.SampleRate, int) and decoder.SampleRate == test_tool_data["sample_rate"]
    assert isinstance(decoder.SamplePoint1, int) and decoder.SamplePoint1 > 0
    assert isinstance(decoder.SamplePoint2, int) and decoder.SamplePoint2 > 0
    assert isinstance(decoder.sample_N, int) and decoder.sample_N > 0
    
    assert decoder.Tare_BX == test_tool_data["tare_xv"], \
        f"{__name__} failed: tare_bx init failed: not apply tool setting value"
    assert decoder.Tare_BY == test_tool_data["tare_yv"], \
        f"{__name__} failed: tare_by init failed: not apply tool setting value"
    assert decoder.Tare_BZ == test_tool_data["tare_zv"], \
        f"{__name__} failed: tare_bz init failed: not apply tool setting value"
    assert decoder.Tare_BT == test_tool_data["tare_tv"], \
        f"{__name__} failed: tare_bt init failed: not apply tool setting value"

    assert decoder.Tare_GX == test_tool_data["tare_gx"], \
        f"{__name__} failed: tare_gx init failed: not apply tool setting value"
    assert decoder.Tare_GY == test_tool_data["tare_gy"], \
        f"{__name__} failed: tare_gy init failed: not apply tool setting value"
    assert decoder.Tare_GZ == test_tool_data["tare_gz"], \
        f"{__name__} failed: tare_gz init failed: not apply tool setting value"



def test_DecoderWorker_socket_decoder_with_empty_input_expect_all_zero_return():
    test_tool_data = _create_test_tool_data()
    decoder = DecoderWorker(test_tool_data)
    test_hex_data = ""

    high_freq_sample_n = decoder.SamplePoint1
    expect_x = np.array([0] * high_freq_sample_n, dtype=np.float64)
    expect_y = np.array([0] * high_freq_sample_n, dtype=np.float64)
    expect_xy = np.array([0] * high_freq_sample_n, dtype=np.float64)
    expect_tension = np.array([0] * high_freq_sample_n, dtype=np.float64)
    expect_torsion = np.array([0] * high_freq_sample_n, dtype=np.float64)
    expect_battery = 0.0

    low_freq_sample_n = decoder.SamplePoint2
    expect_ADXL_X = np.array([0] * low_freq_sample_n, dtype=np.float64)
    expect_ADXL_Y = np.array([0] * low_freq_sample_n, dtype=np.float64)
    expect_ADXL_Z = np.array([0] * low_freq_sample_n, dtype=np.float64)
    expect_Temperature = np.array([0] * low_freq_sample_n, dtype=np.float64)
    expect_wifi_RSSI = 0.0
    expect_isCharging = False
    expect_isSleep = False
    expect_co2_g = 0.0

    MS_BendingX, MS_BendingY, MS_BendingXY, MS_Tension, MS_Torsion, \
        MS_Temperature, MS_ADXL_X, MS_ADXL_Y, MS_ADXL_Z, \
        wifi_RSSI, isCharging, isSleep, battery, co2_g = \
        decoder.socket_decoder(test_hex_data)

    assert np.allclose(MS_BendingX, expect_x, atol=1e-10), \
        f"{__name__} failed: x={MS_BendingX[0:3]}..., expected {expect_x[0:3]}..."
    assert np.allclose(MS_BendingY, expect_y, atol=1e-10), \
        f"{__name__} failed: y={MS_BendingY[0:3]}..., expected {expect_y[0:3]}..."
    assert np.allclose(MS_BendingXY, expect_xy, atol=1e-10), \
        f"{__name__} failed: xy={MS_BendingXY[0:3]}..., expected {expect_xy[0:3]}..."
    assert np.allclose(MS_Tension, expect_tension, atol=1e-10), \
        f"{__name__} failed: tension={MS_Tension[0:3]}..., expected {expect_tension[0:3]}..."
    assert np.allclose(MS_Torsion, expect_torsion, atol=1e-10), \
        f"{__name__} failed: torsion={MS_Torsion[0:3]}..., expected {expect_torsion[0:3]}..."
    assert battery == expect_battery, f"{__name__} failed: battery={battery}, expected {expect_battery}"

    assert np.allclose(MS_ADXL_X, expect_ADXL_X, atol=1e-10), \
        f"{__name__} failed: ADXL_x={MS_ADXL_X}, expected {expect_ADXL_X}"
    assert np.allclose(MS_ADXL_Y, expect_ADXL_Y, atol=1e-10), \
        f"{__name__} failed: ADXL_y={MS_ADXL_Y}, expected {expect_ADXL_Y}"
    assert np.allclose(MS_ADXL_Z, expect_ADXL_Z, atol=1e-10), \
        f"{__name__} failed: ADXL_z={MS_ADXL_Z}, expected {expect_ADXL_Z}"
    assert np.allclose(MS_Temperature, expect_Temperature, atol=1e-10), \
        f"{__name__} failed: temperature={MS_Temperature}, expected {expect_Temperature}"
    assert wifi_RSSI == expect_wifi_RSSI, f"{__name__} failed: wifi_RSSI={wifi_RSSI}, expected {expect_wifi_RSSI}"
    assert isCharging == expect_isCharging, f"{__name__} failed: isCharging={isCharging}, expected {expect_isCharging}"
    assert isSleep == expect_isSleep, f"{__name__} failed: isSleep={isSleep}, expected {expect_isSleep}"
    assert co2_g == expect_co2_g, f"{__name__} failed: co2_g={co2_g}, expected {expect_co2_g}"


def test_DecoderWorker_socket_decoder_with_normal_input_no_filter_expect_decoded_data():
    test_tool_data = _create_test_tool_data()
    decoder = DecoderWorker(test_tool_data)
    round_decimal_places = 6
    decoder.display_decimal_places = round_decimal_places
    
    parm_N_X = decoder.parm_N_X
    parm_N_Y = decoder.parm_N_Y
    parm_N_Z = decoder.parm_N_Z
    parm_N_T = decoder.parm_N_T
    high_freq_sample_n = decoder.SamplePoint1
    low_freq_sample_n = decoder.SamplePoint2
    test_hex_data = _generate_n_hex_data(high_freq_sample_n + low_freq_sample_n)

    val = 16000 / 6553.5
    expect_x = np.array([val * parm_N_X] * high_freq_sample_n, dtype=np.float64)
    expect_y = np.array([val * parm_N_Y] * high_freq_sample_n, dtype=np.float64)
    expect_xy = np.array([(expect_x**2 + expect_y**2) ** 0.5] * high_freq_sample_n, dtype=np.float64)
    expect_tension = np.array([val * parm_N_Z] * high_freq_sample_n, dtype=np.float64)
    expect_torsion = np.array([val * parm_N_T] * high_freq_sample_n, dtype=np.float64)
    expect_battery = round(val, round_decimal_places)

    adxl_val = 1200
    tare_gx = decoder.Tare_GX
    tare_gy = decoder.Tare_GY
    tare_gz = decoder.Tare_GZ
    expect_ADXL_X = np.array([adxl_val - tare_gx] * low_freq_sample_n, dtype=np.float64)
    expect_ADXL_Y = np.array([adxl_val - tare_gy] * low_freq_sample_n, dtype=np.float64)
    expect_ADXL_Z = np.array([adxl_val - tare_gz] * low_freq_sample_n, dtype=np.float64)
    expect_Temperature = np.array([25.0 / 128] * low_freq_sample_n, dtype=np.float64)
    expect_wifi_RSSI = 25
    expect_isCharging = True
    expect_isSleep = False
    expect_co2_g = 0.0

    MS_BendingX, MS_BendingY, MS_BendingXY, MS_Tension, MS_Torsion, \
        MS_Temperature, MS_ADXL_X, MS_ADXL_Y, MS_ADXL_Z, \
        wifi_RSSI, isCharging, isSleep, battery, co2_g = \
        decoder.socket_decoder(test_hex_data)

    #- 這裡容差要考慮 round() 的精度和 round_decimal_places 的值
    tolerance = 0.5 * 10**(-round_decimal_places)

    assert np.allclose(MS_BendingX, expect_x, atol=tolerance), \
        f"{__name__} failed: x={MS_BendingX[0:3]}..., expected {expect_x[0:3]}..."
    assert np.allclose(MS_BendingY, expect_y, atol=tolerance), \
        f"{__name__} failed: y={MS_BendingY[0:3]}..., expected {expect_y[0:3]}..."
    assert np.allclose(MS_BendingXY, expect_xy, atol=tolerance), \
        f"{__name__} failed: xy={MS_BendingXY[0:3]}..., expected {expect_xy[0:3]}..."
    assert np.allclose(MS_Tension, expect_tension, atol=tolerance), \
        f"{__name__} failed: tension={MS_Tension[0:3]}..., expected {expect_tension[0:3]}..."
    assert np.allclose(MS_Torsion, expect_torsion, atol=tolerance), \
        f"{__name__} failed: torsion={MS_Torsion[0:3]}..., expected {expect_torsion[0:3]}..."
    assert battery == expect_battery, f"{__name__} failed: battery={battery}, expected {expect_battery}"

    assert np.allclose(MS_ADXL_X, expect_ADXL_X, atol=tolerance), \
        f"{__name__} failed: ADXL_x={MS_ADXL_X}, expected {expect_ADXL_X}"
    assert np.allclose(MS_ADXL_Y, expect_ADXL_Y, atol=tolerance), \
        f"{__name__} failed: ADXL_y={MS_ADXL_Y}, expected {expect_ADXL_Y}"
    assert np.allclose(MS_ADXL_Z, expect_ADXL_Z, atol=tolerance), \
        f"{__name__} failed: ADXL_z={MS_ADXL_Z}, expected {expect_ADXL_Z}"
    assert np.allclose(MS_Temperature, expect_Temperature, atol=tolerance), \
        f"{__name__} failed: temperature={MS_Temperature}, expected {expect_Temperature}"
    assert wifi_RSSI == expect_wifi_RSSI, f"{__name__} failed: wifi_RSSI={wifi_RSSI}, expected {expect_wifi_RSSI}"
    assert isCharging == expect_isCharging, f"{__name__} failed: isCharging={isCharging}, expected {expect_isCharging}"
    assert isSleep == expect_isSleep, f"{__name__} failed: isSleep={isSleep}, expected {expect_isSleep}"
    assert co2_g == expect_co2_g, f"{__name__} failed: co2_g={co2_g}, expected {expect_co2_g}"

