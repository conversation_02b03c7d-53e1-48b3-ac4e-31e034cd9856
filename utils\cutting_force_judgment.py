import math
import numpy as np


def Judge( center_radius, edge_number, points_X, points_Y, points_XY):  
    
    #需要Tare的角度計算
    Tare_Angle=0
    Mean_X = round(np.mean(points_X),2)
    Mean_Y = round(np.mean(points_Y),2)
    Mean_XY = round(np.mean(points_XY),2)
    
    if abs(Mean_X)>1 or abs(Mean_Y)>1:
        Tare_Angle = int((math.degrees(math.atan2(Mean_Y,Mean_X)) +360)%360)  #回傳整體平均值偏向方向
        # print("Tare_Angle= ",Tare_Angle)
             
        
    #計算刃角度    
    SplitAngle = int(360 / edge_number)  #除以刃數，獲得區別角度
    ShiftAngle = 3
    Result_list=[]   #每一個資料分配哪一個顏色區代號


    #間隔50
    StartIndex=0
    EndIndex=50
    

    while len(points_X) > StartIndex:
        Test_NoCenterPoint=0
        TempStartIndex = StartIndex
    
        while len(points_X) > EndIndex:
            if min(points_XY[TempStartIndex:EndIndex])>center_radius:
                temp_test=1  #包刀
            else:
                temp_test=2  #無包刀

            if Test_NoCenterPoint==0:  #第一次
                Test_NoCenterPoint=temp_test

            elif Test_NoCenterPoint!=temp_test:  #不在同類型
                break

            TempStartIndex = EndIndex
            EndIndex += 50
    

        angle_list = [ 0 for i in range(0,360) ]  #角度0~359度，記錄其相關位置資訊
        for i in range(StartIndex,EndIndex):  # ,2 跳著處理減少資料
            if points_XY[i]>center_radius:
                Temp_Angle = int((math.degrees(math.atan2(points_Y[i],points_X[i])) +360)%360)  #角度
                
                # print("BEFORE Temp_Angle= ",Temp_Angle)
                # print("ShiftAngle= ",ShiftAngle)
                Temp_Angle = Temp_Angle  //ShiftAngle *ShiftAngle  #以某數角度值區間統一統計
                
                # print("AFFTER Temp_Angle= ",Temp_Angle)
                angle_list[Temp_Angle] += 1
        # print("angle_list= ",angle_list)
        # print(len(angle_list))
        # print(max(angle_list))

        if Test_NoCenterPoint==1:  #包刀
            FindLocalAngle = SplitAngle//3 #設定範圍多少內來找低點
            MaxTimeAngle = angle_list.index( max(angle_list) )  #最多的角度
            ProcessAngle = MaxTimeAngle
            SplitAngles=[]
            
            while len(SplitAngles) != edge_number:
                ProcessAngle = ProcessAngle%360
                if ProcessAngle - FindLocalAngle < 0:
                    temp_angle = int(FindLocalAngle - ProcessAngle)
                    temp_t_list = angle_list[360-temp_angle:360:ShiftAngle] + angle_list[0:ProcessAngle:ShiftAngle]      
                    temp_a_list = [i for i in range(360-temp_angle,360,ShiftAngle)] + [i for i in range(0,ProcessAngle,ShiftAngle)]
                else:
                    temp_angle = int(ProcessAngle-FindLocalAngle)
                    temp_t_list = angle_list[temp_angle:ProcessAngle:ShiftAngle]     
                    temp_a_list = [i for i in range(temp_angle,ProcessAngle,ShiftAngle)] 
                temp_min = min(temp_t_list)
    
                for i in range(len(temp_t_list)-1,-1,-1):
                    if temp_min==temp_t_list[i]:
                        temp_index = i
                        break
                    
                ProcessAngle += SplitAngle
                SplitAngles.append(temp_a_list[temp_index])
        
            #---找完4個切割角度點後，針對每一個點位分配顏色區域---
            SplitAngles.sort()
    
            
            for i in range(StartIndex,EndIndex):
                if points_XY[i]>center_radius:  #大於中心，是刀刃
                    test_angle = int((math.degrees(math.atan2(points_Y[i],points_X[i])) +360)%360)    
                    
                    for n in range(edge_number):    
                        if n == edge_number-1:
                            Result_list.append(n+1)
                            break
                        elif SplitAngles[n+1] > test_angle and test_angle >= SplitAngles[n]:
                            Result_list.append(n+1)
                            break
                        
                else:  #小於中心區，是中心
                    Result_list.append(0)
            
            StartIndex = EndIndex
            EndIndex += 50  
            
        else:  #無包刀
            FindLocalAngle = SplitAngle//2 #設定範圍多少內來找低點
            MaxTimeAngle = angle_list.index( max(angle_list) )  #最多的角度
            ProcessAngle = MaxTimeAngle
            SplitAngles=[]
            
            while len(SplitAngles) != edge_number:
                
                ProcessAngle = ProcessAngle%360
    
                
                if ProcessAngle + FindLocalAngle > 360:
                    temp_angle = int(FindLocalAngle + ProcessAngle) %360
                    temp_t_list = angle_list[ProcessAngle:360:ShiftAngle] + angle_list[0:temp_angle:ShiftAngle]      
                    temp_a_list = [i for i in range(ProcessAngle,360,ShiftAngle)] + [i for i in range(0,temp_angle,ShiftAngle)]
                else:
                    temp_angle = int(ProcessAngle+FindLocalAngle)
                    temp_t_list = angle_list[ProcessAngle:temp_angle:ShiftAngle]     
                    temp_a_list = [i for i in range(ProcessAngle,temp_angle,ShiftAngle)] 
                temp_min = min(temp_t_list)
                
                for i in range(len(temp_t_list)-1,-1,-1):
                    if temp_min==temp_t_list[i]:
                        temp_index = i
                        break
                    
                ProcessAngle += SplitAngle
                SplitAngles.append(temp_a_list[temp_index])
        
            #---找完4個切割角度點後，針對每一個點位分配顏色區域---
            SplitAngles.sort()
            
            
            for i in range(StartIndex,EndIndex):
                if points_XY[i]>center_radius:  #大於中心，是刀刃
                    test_angle = int((math.degrees(math.atan2(points_Y[i],points_X[i])) +360)%360)    
                
                    # for n in range(edge_number):    
    
                    #     if n == edge_number-1:
                    #         Result_list.append(n+1)
                    #         break
                    #     elif SplitAngles[n+1] > test_angle and test_angle >= SplitAngles[n]:
                    #         Result_list.append(n+1)
                    #         break

                    # #4刃可work
    #                 if SplitAngles[1] > test_angle and test_angle >= SplitAngles[0]:
    #                     Result_list.append(1)
    #                 elif SplitAngles[2] > test_angle and test_angle >= SplitAngles[1]:
    #                     Result_list.append(2)
    #                 # elif SplitAngles[3] > test_angle and test_angle >= SplitAngles[2]:
    #                 #     Result_list.append(3)
    #                 else:
    #                     Result_list.append(4)
                
    
                    if SplitAngles[0]>45:
                        
                        for n in range(edge_number):    
                            if n == edge_number-1:
                                Result_list.append(1)
                                break
                            elif SplitAngles[n+1] > test_angle and test_angle >= SplitAngles[n]:
                                Result_list.append(n+2)
                                break
                        
                        # if SplitAngles[1] > test_angle and test_angle >= SplitAngles[0]:
                        #     Result_list.append(2)
                        # elif SplitAngles[2] > test_angle and test_angle >= SplitAngles[1]:
                        #     Result_list.append(3)
                        # elif SplitAngles[3] > test_angle and test_angle >= SplitAngles[2]:
                        #     Result_list.append(4)
                        # else:
                        #     Result_list.append(1)

                    else:
                        for n in range(edge_number):    
                            if n == edge_number-1:
                                Result_list.append(n+1)
                                break
                            elif SplitAngles[n+1] > test_angle and test_angle >= SplitAngles[n]:
                                Result_list.append(n+1)
                                break

            
                else:  #小於中心區，是中心
                    Result_list.append(0)
            
            StartIndex = EndIndex
            EndIndex += 50  

            # print(Result_list)
    return Result_list, Tare_Angle, 0  #未寫分類種類，先給0





# def Judge( center_radius, edge_number, points_X, points_Y, points_XY):  #, LastMean_X, LastMean_Y, LastMean_Fc 
    
#     #需要Tare的角度計算
#     Tare_Angle=0
#     Mean_X = round(np.mean(points_X),2)
#     Mean_Y = round(np.mean(points_Y),2)
#     Mean_XY = round(np.mean(points_XY),2)
#     # Mean_XY = math.sqrt( (Mean_X*Mean_X) + (Mean_Y*Mean_Y) )
    
#     if abs(Mean_X)>1 or abs(Mean_Y)>1:
#         Tare_Angle = int((math.degrees(math.atan2(Mean_Y,Mean_X)) +360)%360)  #回傳整體平均值偏向方向
             
        
#     #計算刃角度    
#     SplitAngle = int(360 / edge_number)  #除以刃數，獲得區別角度
#     ShiftAngle = 3
#     Result_list=[]   #每一個資料分配哪一個顏色區代號


#     StartIndex=0
#     EndIndex=50
    
    
#     print("YES! I IN")
    
#     while len(points_X) > StartIndex:
#         Test_NoCenterPoint=0
#         TempStartIndex = StartIndex
    
#         print("WHILE",StartIndex)
    
#         while len(points_X) > EndIndex:
#             if min(points_XY[TempStartIndex:EndIndex])>center_radius:
#                 temp_test=1  #包刀
#             else:
#                 temp_test=2  #無包刀

#             if Test_NoCenterPoint==0:  #第一次
#                 Test_NoCenterPoint=temp_test

#             elif Test_NoCenterPoint!=temp_test:  #不在同類型
#                 break

#             TempStartIndex = EndIndex
#             EndIndex += 50
    
#         print("Test_NoCenterPoint",Test_NoCenterPoint)
    
#         angle_list = [ 0 for i in range(0,360) ]  #角度0~359度，記錄其相關位置資訊
#         for i in range(StartIndex,EndIndex):  # ,2 跳著處理減少資料
#             if points_XY[i]>center_radius:
#                 Temp_Angle = int((math.degrees(math.atan2(points_Y[i],points_X[i])) +360)%360)  #角度
#                 Temp_Angle = Temp_Angle  //ShiftAngle *ShiftAngle  #以某數角度值區間統一統計
#                 angle_list[Temp_Angle] += 1

#         # if Test_NoCenterPoint==1:
#         #     FindLocalAngle = 15   #設定範圍多少內來找低點
#         # else:
#         FindLocalAngle = SplitAngle//3 #設定範圍多少內來找低點
#         MaxTimeAngle = angle_list.index( max(angle_list) )  #最多的角度
#         ProcessAngle = MaxTimeAngle
#         # print("CCC最多的角度",MaxTimeAngle)

#         SplitAngles=[]
#         while len(SplitAngles) != edge_number:
            
#             ProcessAngle = ProcessAngle%360
#             # print("\n!!  第" , len(SplitAngles) , "  次!!  角度:" , ProcessAngle)
            
#             if ProcessAngle - FindLocalAngle < 0:
#                 temp_angle = int(FindLocalAngle - ProcessAngle)
#                 temp_t_list = angle_list[360-temp_angle:360:ShiftAngle] + angle_list[0:ProcessAngle:ShiftAngle]      
#                 temp_a_list = [i for i in range(360-temp_angle,360,ShiftAngle)] + [i for i in range(0,ProcessAngle,ShiftAngle)]
            
#                 # print("temp_angle",temp_angle)
#                 # print("temp_t_list",temp_t_list,len(temp_t_list))
#                 # print("temp_a_list",temp_a_list,len(temp_a_list))
                    
#                 #找到最低點  可能在上後半段x~360度，可能在0~現在角度前半段
#                 # temp_index = temp_t_list.index( min(temp_t_list) )      
#                 # print("最少次數",temp_t_list[temp_index],"最少角度",temp_a_list[temp_index])    
        
#             else:
#                 temp_angle = int(ProcessAngle-FindLocalAngle)
#                 temp_t_list = angle_list[temp_angle:ProcessAngle:ShiftAngle]     
#                 temp_a_list = [i for i in range(temp_angle,ProcessAngle,ShiftAngle)] 
            
#                 # print("temp_angle",temp_angle)
#                 # print("temp_t_list",temp_t_list,len(temp_t_list))
#                 # print("temp_a_list",temp_a_list,len(temp_a_list))
                    
#                 #找到最低點  可能在上後半段x~360度，可能在0~現在角度前半段
#                 # temp_index = temp_t_list.index( min(temp_t_list) )      
#                 # print("最少次數",temp_t_list[temp_index],"最少角度",temp_a_list[temp_index])  
                
#             #以最多數量往下後開始找最少點之靠近右邊的為先
#             temp_min = min(temp_t_list)
#             for i in range(len(temp_t_list)-1,-1,-1):
#                 if temp_min==temp_t_list[i]:
#                     temp_index = i
#                     break
                
#             ProcessAngle += SplitAngle
#             SplitAngles.append(temp_a_list[temp_index])
    
#         #---找完4個切割角度點後，針對每一個點位分配顏色區域---
#         SplitAngles.sort()
#         print("切割點角度",SplitAngles)
#         Work_Angles=[ Divide_Angle(SplitAngles[i], SplitAngles[i+1]) if i != len(SplitAngles)-1 else Divide_Angle(SplitAngles[i], SplitAngles[0]) for i in range(len(SplitAngles)) ]
#         Work_Angles.sort()
#         print("工作點角度",Work_Angles)

        
        
#         for i in range(StartIndex,EndIndex):
#             if points_XY[i]>center_radius:  #大於中心，是刀刃
#                 test_angle = int((math.degrees(math.atan2(points_Y[i],points_X[i])) +360)%360)    
            
            
#                 # print("IIIIII",i)
            
#                 #4刃可work
#                 if SplitAngles[1] > test_angle and test_angle >= SplitAngles[0]:
#                     Result_list.append(1)
#                 elif SplitAngles[2] > test_angle and test_angle >= SplitAngles[1]:
#                     Result_list.append(2)
#                 # elif SplitAngles[3] > test_angle and test_angle >= SplitAngles[2]:
#                 #     Result_list.append(3)
#                 else:
#                     Result_list.append(4)
                    
#                 # for n in range(edge_number):    
#                 #     # print("nnnn",n)
#                 #     if n == edge_number-1:
#                 #         Result_list.append(n+1)
#                 #         break
#                 #     elif SplitAngles[n+1] > test_angle and test_angle >= SplitAngles[n]:
#                 #         Result_list.append(n+1)
#                 #         break
                    
                    
                    
            
#                 # if Work_Angles[1] > test_angle and test_angle >= Work_Angles[0]:
#                 #     Result_list.append(1)
#                 # elif Work_Angles[2] > test_angle and test_angle >= Work_Angles[1]:
#                 #     Result_list.append(2)
#                 # elif Work_Angles[3] > test_angle and test_angle >= Work_Angles[2]:
#                 #     Result_list.append(3)
#                 # else:
#                 #     Result_list.append(4)
            
            
            
#             else:  #小於中心區，是中心
#                 Result_list.append(0)
        
#         StartIndex = EndIndex
#         EndIndex += 50




#     # print("Result" , Result_list  , len(Result_list))
#     return Result_list, Tare_Angle, 0  #未寫分類種類，先給0
    


# def Divide_Angle(angle1, angle2):
#     #逆時針算法
#     if angle2 > angle1:
#         average_counterclockwise = (angle1 + angle2) / 2
#     else:
#         average_counterclockwise = (angle1 + angle2 +360) / 2 % 360

#     return average_counterclockwise


    # CorrectionPointX=[0 for i in range(4)] 
    # CorrectionPointY=[0 for i in range(4)] 
    # CorrectionPointSize=[0 for i in range(4)] 
    # Blade_Fc=[0 for i in range(4)]
    # Threshold_XY = 12 #更改閥值
    
    # for i in range(0,len(points_X)):
    #     # if points_XY[i] > Threshold_XY:
    #     if points_XY[i] - Mean_XY > Threshold_XY:
    #         min_value=9999
    #         min_index=0
    #         TestPoint_Angle=( math.degrees(math.atan2(points_Y[i],points_X[i])) +360)%360
            
    #         for x in range(4):
    #             Angle_Diff=abs(Area_Angle[x]-TestPoint_Angle)
                
    #             # #---------------------
    #             # if Angle_Diff <= min_value:
    #             #     min_value=Angle_Diff
    #             #     min_index=x
                    
    #             #小改善，避免4個區域都計算---------------------
    #             if Angle_Diff <= 45:
    #                 min_index=x
    #                 break
    #             elif Angle_Diff <= min_value:
    #                 min_value=Angle_Diff
    #                 min_index=x

    #         #~~~校正~~~
    #         CorrectionPointX[min_index] += points_X[i]
    #         CorrectionPointY[min_index] += points_Y[i]
            
            
            
            
    #         Blade_Fc[min_index] += math.sqrt( (points_X[i]*points_X[i]) + (points_Y[i]*points_Y[i]) )
            
    #         CorrectionPointSize[min_index] += 1
    #         #~~~校正~~~

    
    # #~~~校正~~~
    
    # for i in range(len(CorrectionPointX)):
    #     if(CorrectionPointX[i] !=0) and (CorrectionPointY[i] !=0):
    #         CorrectionPointX[i] = CorrectionPointX[i] / CorrectionPointSize[i]
    #         CorrectionPointY[i] = CorrectionPointY[i] / CorrectionPointSize[i]
            
    #         Blade_Fc[i] = round(Blade_Fc[i] / CorrectionPointSize[i],2)
    #         # print("i:",i)
    #         # print("X,Y",CorrectionPointX[i],CorrectionPointY[i])
    #         # print("Fc",Blade_Fc[i])
    
    
    # # return [Tare_Angle,Mean_X,Mean_Y,Mean_XY,Blade_Fc[0],Blade_Fc[1],Blade_Fc[2],Blade_Fc[3]]  
    
    