from PySide2.QtCore import QObject, Signal
from . import logger

'''
# TODO: implement connection HERE instead of in controller
# TODO: make a tool_data class
# TODO: if similar w/ CNC operation, make a parent class for tool_manager and cnc_manager to inherit

Future Plan:
- Extend to support multiple tools
'''
 
class ToolManager(QObject):

    # signal to controller
    sig_connect_tool = Signal(dict)
    sig_disconnect_tool = Signal(int) # not doing anything yet: new connection takes care of disconnection from previous tool

    def __init__(self, model):
        super().__init__()

        # tool list
        self.model = model
        self.tool_list = {} # dict of dicts, key is tool_id, value is tool_data

        # tool connection
        self.connection_status = False # True if tool is connected
        self.connected_tool = None # tool_id of currently connected tool

        # read tool list from magazine
        self.read_tool_magazine()
    
    def get_tool_list(self):
        """get tool list"""
        return list(self.tool_list.values())
    
    def get_tool_data(self, tool_id):
        """get tool data by tool_id"""
        return dict(self.tool_list.get(tool_id, None))
    
    def add_new_tool(self, tool_mac) -> bool:
        """add new tool to tool_list"""

        logger.info(f"Adding new tool, ip: {tool_mac}")

        # > Check if tool already exists
        # idea: check tool_list, as tool_list should be consistent with database
        # if self.tool_exists(tool_mac):
        #     logger.error(f"Tool {tool_mac} already exists")
        #     return False

        # > look up tool data by mac address (module: network_device_manager)
        # idea: view -> controller -> tool_manager -> network_device_manager
        # success, ip = self.network_device_manager.check_mac_in_network(mac_address=tool_mac) # change this func to not write to db & not init data & return ip
        # if not success:
            # logger.error("Failed to find tool by mac address in network")
            # return false
        
        # > init default tool data (module: config.py)
        # id = len(self.tool_list) + 1
        # tool_data = dict(DEFAULT_TOOL_DATA)
        # tool_data['toolmac'] = tool_mac
        # tool_data['toolip'] = ip
        # tool_data['toolname'] = f'default_{ip}'
        # tool_data['id'] = id  

        # > Add tool to database
        # self.model.connect_db()
        # self.model.insert_tool(tool_data) #insert_tool: to be implemented
        # self.model.close_db()
        
        # > Add to internal tool list
        # self.tool_list[id] = tool_data

        # > Finish
        # return True

        # Temporary implementation 
        # read db magazine (tool is already added to db by network_device_manager)
        self.read_tool_magazine()
    
    def update_tool_data(self, tool_data): 
        """update tool data of existing tool in tool_list"""

        # validate tool data
        if not self.validate_tool_data(tool_data):
            logger.error(f"Invalid tool data")
            return False
        
        # check if tool exists
        tool_id = tool_data.get('id', None)
        if not self.tool_exists(tool_id):
            logger.error(f"Tool {tool_id} not found in tool list")
            return False
        
        logger.info(f"Updating tool data: {tool_id} {tool_data}")
        
        # update internal list    
        self.tool_list[tool_id] = tool_data
        
        # update database 
        self.model.connect_db()
        self.model.update_tool_data(tool_data)
        self.model.close_db()

        return True
    
    def tool_exists(self, tool_x: any) -> bool:
        """check if tool exists"""
        if isinstance(tool_x, int):
            return tool_x in self.tool_list
        elif isinstance(tool_x, str):
            for tool in self.tool_list:
                if tool['toolmac'] == tool_x:
                    return True
            return False
        else:
            return False

    def validate_tool_data(self, tool_data) -> bool:
        """Validate tool data structure"""
        if not isinstance(tool_data, dict):
            return False
            
        # Check for required fields
        required_fields = ['id', 'toolip', 'toolname']
        for field in required_fields:
            if field not in tool_data:
                logger.error(f"Missing required field: {field}")
                return False
                
        return True
    
    def read_tool_magazine(self) -> bool:
        """read tools from database"""

        logger.info("Reading tool list from magazine")
        try:
            # read from database
            self.model.connect_db()
            tool_list = self.model.get_tool_data()
            self.model.close_db()

            # convert to dict
            tool_ids = [tool['id'] for tool in tool_list]
            tool_datas = [dict(tool) for tool in tool_list]
            self.tool_list = dict(zip(tool_ids, tool_datas))
            logger.info(f"Tool list: {self.tool_list}")

            return True
        except Exception as e:
            error_msg = f"Failed to read tool magazine: {e}"
            logger.error(error_msg)
            return False

    def connect_tool(self, tool_id) -> bool:
        """connect to tool"""

        logger.info(f"Connecting to tool: {tool_id}")
        
        # Validate tool exists
        if not self.tool_exists(tool_id):
            error_msg = f"Tool {tool_id} not found in tool list"
            logger.error(error_msg)
            return False
            
        self.sig_connect_tool.emit(self.get_tool_data(tool_id))
        return True

    def disconnect_tool(self, tool_id) -> bool:
        """disconnect from tool"""
        
        logger.info(f"Disconnecting from tool: {tool_id}")

        # Validate tool exists
        if not self.tool_exists(tool_id):
            error_msg = f"Tool {tool_id} not found in tool list"
            logger.error(error_msg)
            return False

        self.sig_disconnect_tool.emit(tool_id)
        return True
    
    def on_tool_connected(self, tool_id):
        """tool connected"""
        
        logger.info(f"Tool {tool_id} connected")
        self.connection_status = True
        self.connected_tool = tool_id
        self.current_tool_data = self.tool_list.get(tool_id, {})

    def on_tool_disconnected(self, tool_id):
        """tool disconnected"""
        
        logger.info(f"Tool {tool_id} disconnected")
        self.connection_status = False
        self.connected_tool = None
        self.current_tool_data = None
    
    def is_connected(self) -> bool:
        """Check if any tool is currently connected"""
        return self.connection_status
    
    def get_connected_tool_data(self) -> dict:
        """get currently connected tool"""
        connected_tool_data = self.tool_list.get(self.connected_tool, None)
        logger.info(f"Currently connected tool data: {connected_tool_data}")
        return connected_tool_data
    
    