import numpy as np
from . import logger  # 導入 logger

class CO2Data:
    def __init__(self):
        self.data = []
        self.ema_value = None
        self.CO2_accumulation = 0 # CO2累積值
        self.CO2_Pc_flag = False
        self.CO2_Pc_interval_count = 0  # 這行解決問題

    def clear_accumulation(self):
        self.CO2_accumulation = 0

    def caculate_co2_g(self, txt_ten, SampleRate, co2_data): 
        MS_CO2_Pc = co2_data["MS_CO2_Pc"]
        MS_CO2_Pb = co2_data["MS_CO2_Pb"]
        MS_CO2_EF = co2_data["MS_CO2_EF"]

        if abs(txt_ten)>3:  #Tension若大於3則開始觸發，0.05是手用力測試
            self.CO2_Pc_flag=True
            CO2_Pc_interval_trigger = 0.02 * SampleRate  #0.02秒
            self.CO2_Pc_interval_count = CO2_Pc_interval_trigger
        else:
            self.CO2_Pc_interval_count-=1
            if 0 >= self.CO2_Pc_interval_count:  
                self.CO2_Pc_flag=False  #在0.02秒內沒有Tension大於3，判定沒在切削了
            
        if self.CO2_Pc_flag:  #Pc+Pb
            self.CO2_accumulation += (MS_CO2_Pc + MS_CO2_Pb) * MS_CO2_EF /3.6 / SampleRate  #*1000 /3600 => /3.6
        else:  #Pb
            self.CO2_accumulation += MS_CO2_Pb * MS_CO2_EF /3.6/ SampleRate

        return self.CO2_accumulation 