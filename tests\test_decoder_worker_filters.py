"""
Comprehensive DecoderWorker filter tests
----------------------------------------

Each test feeds two artificial 800-packet frames to DecoderWorker:

frame-1 supplies four *history* points (last queued hi-freq packets)
frame-2 provides a single new point in its first usable packet.

The test then:
1.  extracts the true history from dw.decoded_high_freq_data_queue
2.  recreates the same filter object used in apply_filter
3.  runs filter.applyTo on a local copy of the new frame’s bending-X data
4.  compares DecoderWorker’s first output sample to that expected value.

Helpers are copied from the Gaussian test, so they can live together.
"""

import numpy as np
from scipy import signal

from app.models.decoder_Worker import DecoderWorker
from app.models.filters import (
    MovingAverage,
    MedianFilter,
    ButterworthFilter,
    <PERSON><PERSON>tzkyGolayFilter,
    G<PERSON>sianFilter,
)

# ------------------------------------------------------------------- constants
DIV     = 6553.5
SR      = 10_000
NPKT    = 800
HIST_V  = [0.2, 0.3, 0.4, 0.5]    # volts in frame-1 history
NEW_V   = 1.0                     # volt in first usable pkt of frame-2

WIN3    = 3
WIN5    = 5
CUTOFF  = 100                     # Hz for Butterworth low-pass
POLY    = 3                       # polyorder for Savitzky–Golay

# ------------------------------------------------------------------- utilities
def w_from_v(v: float) -> str:
    return f"{int(round(v*DIV)) & 0xFFFF:04x}"

def low_pkt() -> str:            # 32 chars
    return "00"*16

def hi_pkt(v: float) -> str:
    return w_from_v(v)*4 + "00"*8

def frame_tail(tail_values):
    last_queued = NPKT - 4 - 1               # WIN5 -> skip last 4
    inject = list(range(last_queued, last_queued-len(tail_values), -1))
    vals = tail_values[::-1]; vi = 0
    pkts = []
    for i in range(NPKT):
        if i % 201 == 0:
            pkts.append(low_pkt())
        elif i in inject:
            pkts.append(hi_pkt(vals[vi])); vi += 1
        else:
            pkts.append(hi_pkt(0.0))
    return "".join(pkts)

def frame_head(head_values):
    vi = 0; pkts = []
    for i in range(NPKT):
        if i % 201 == 0:
            pkts.append(low_pkt())
        elif vi < len(head_values):
            pkts.append(hi_pkt(head_values[vi])); vi += 1
        else:
            pkts.append(hi_pkt(0.0))
    return "".join(pkts)

def tooldata():
    return dict(
        toolname="UT", toolip="0", toolmac="00", sample_rate=SR,
        tare_xv=0, tare_yv=0, tare_zv=0, tare_tv=0,
        Linear_x=1, Linear_y=1, Linear_z=1, Linear_t=1,
        tare_gx=0, tare_gy=0, tare_gz=0,
        Lc=0.1, Hl=0.1, Kl=0.05, CO2_id=None,
        auto_record_enabled=0, auto_pre_record_seconds=0,
        auto_record_seconds=0, auto_max_record_count=0,
        auto_cf_enabled=0, auto_fz_enabled=0, auto_t_enabled=0,
        auto_cf_threshold=0, auto_fz_threshold=0, auto_t_threshold=0,
    )

# ---------------------------------------------------------------- helper
def run_two_frames(filter_type, filter_kwargs, build_filter):
    dw = DecoderWorker(tooldata())
    # assemble filter_data dict expected by DecoderWorker
    fd = {
        "filter_type": filter_type,
        "sampleRate": SR,
        "decimal_places": 3,
        **filter_kwargs,
    }
    dw.filter_data = fd

    # feed frames
    dw.socket_decoder(frame_tail(HIST_V))
    msB2, *_ = dw.socket_decoder(frame_head([NEW_V]))

    # build expected via same Filter subclass
    hist = [row[0] for row in dw.decoded_high_freq_data_queue.get_all()]
    scale = dw.parm_N_X  # DecoderWorker converts volts → force with this factor
    new_force = round(NEW_V * scale, 3)
    data = np.zeros(NPKT, dtype=float)
    data[0] = new_force
    filt = build_filter()
    filt.applyTo(data, np.asarray(hist))
    return msB2[0], data[0]                 # (DW, expected)


# ------------------------------------------------------------------- tests
def test_mean_filter_history():
    from app.models.filters import MeanFilter
    got, exp = run_two_frames(
        "Meanfilter_radio",
        {"filter_values": WIN3},
        lambda: MeanFilter(WIN3),
    )
    assert np.isclose(got, exp, rtol=1e-6)


def test_median_filter_history():
    got, exp = run_two_frames(
        "Medianfilter_radio",
        {"filter_values": WIN5},
        lambda: MedianFilter(WIN5),
    )
    assert np.isclose(got, exp, rtol=1e-6)


def test_butterworth_lowpass_history():
    cutoff = CUTOFF
    got, exp = run_two_frames(
        "Lowfilter_radio",
        {"Lowfilter_edit": str(cutoff)},
        lambda: ButterworthFilter(cutoff, fs=SR, btype="low", order=2),
    )
    # Butterworth is continuous, allow 0.5% tolerance
    assert np.isclose(got, exp, rtol=5e-3)


def test_savgol_history():
    got, exp = run_two_frames(
        "SGfilter_radio",
        {"SGfilter_edit": WIN5},
        lambda: SavitzkyGolayFilter(WIN5, POLY),
    )
    assert np.isclose(got, exp, rtol=1e-6)


# --------------------------- NEW TESTS -------------------------------------


def test_moving_average_history():
    got, exp = run_two_frames(
        "MAfilter_radio",
        {"MAfilter_edit": WIN5},
        lambda: MovingAverage(WIN5),
    )
    assert np.isclose(got, exp, rtol=1e-6)


def test_gaussian_filter_history():
    got, exp = run_two_frames(
        "Gaussianfilter_radio",
        {"filter_values": WIN5},
        lambda: GaussianFilter(WIN5, 1.0),
    )
    assert np.isclose(got, exp, rtol=1e-6)