<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ModeSwitch_Window</class>
 <widget class="QWidget" name="ModeSwitch_Window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>210</width>
    <height>360</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>210</width>
    <height>360</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>210</width>
    <height>360</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#ModeSwitch_Window{
background-color: #0C0C44;
border: 2px solid #5448B6;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="0,0,0,0,0,0,0">
   <property name="spacing">
    <number>25</number>
   </property>
   <property name="leftMargin">
    <number>10</number>
   </property>
   <property name="topMargin">
    <number>15</number>
   </property>
   <property name="rightMargin">
    <number>10</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <item>
    <widget class="QWidget" name="modeswitch_title" native="true">
     <layout class="QHBoxLayout" name="mode_title">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <spacer name="horizontalSpacer_7">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="mode_title_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>20</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">color:#5448B6;</string>
        </property>
        <property name="text">
         <string>模式切換</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_8">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="realtime_widget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>5</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="realtime_btnicon">
        <property name="styleSheet">
         <string notr="true">border:none;
</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="realtime_btn">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    image: none;
border:none;
color: rgb(255, 255, 255);
}

</string>
        </property>
        <property name="text">
         <string>Real-time</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="readfile_widget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>5</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="readfile_btnicon">
        <property name="styleSheet">
         <string notr="true">border:none;</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="readfile_btn">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">border:none;
color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>Read-file</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="realtime_pro_widget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <property name="spacing">
       <number>5</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="realtimepro_btnicon">
        <property name="styleSheet">
         <string notr="true">QPushButton {
    border:none;
	image: url(:/other/mode_dot_0.png);
}

</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="realtimepro_btn">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">border:none;
color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>Real-time Pro</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="readfile_pro_widget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_4">
      <property name="spacing">
       <number>5</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="readfilepro_btnicon">
        <property name="styleSheet">
         <string notr="true">border:none;</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="readfilepro_btn">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">border:none;
color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>Read-file Pro</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="homepage_widget" native="true">
     <property name="styleSheet">
      <string notr="true">border:none;</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_5">
      <property name="spacing">
       <number>5</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="homepage_btnicon">
        <property name="styleSheet">
         <string notr="true">border:none;</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="homepage_btn">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">border:none;
color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>首頁</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_5">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="analyzer_widget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_6">
      <property name="spacing">
       <number>5</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="analyzer_btnicon">
        <property name="styleSheet">
         <string notr="true">border:none;</string>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="analyzer_btn">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">border:none;
color: rgb(255, 255, 255);</string>
        </property>
        <property name="locale">
         <locale language="Chinese" country="China"/>
        </property>
        <property name="text">
         <string>Analyzer</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_6">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../system_image/system_image.qrc"/>
 </resources>
 <connections/>
</ui>
