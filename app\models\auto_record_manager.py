# -*- coding: utf-8 -*-
"""
Auto Recording Manager
Handles automatic recording based on sensor thresholds
"""

import dataclasses
import math
import time
from collections import deque
import itertools
from PySide2.QtCore import QObject, QTimer, Signal
from . import logger

class AutoRecordManager(QObject):
    """Manages automatic recording based on sensor thresholds"""
    
    # Signals
    sig_auto_record_triggered = Signal(str)  # Emits trigger reason
    sig_auto_record_stop = Signal()
    
    def __init__(self):
        super().__init__()
        # Auto recording settings
        self.auto_pre_record_seconds = 0
        self.auto_record_seconds = 10
        self.auto_max_record_count = 100
        self.sample_rate = 10000  # Default sample rate, will be updated
        
        # Trigger thresholds
        self.auto_cf_enabled = False
        self.auto_fz_enabled = False
        self.auto_t_enabled = False
        self.auto_cf_threshold = 0.0
        self.auto_fz_threshold = 0.0
        self.auto_t_threshold = 0.0
        
        # Recording state
        self.is_recording = False
        self.record_count = 0
        self.trigger_reason = ""
        
        # pre record buffer
        self.write_buffer_size = 804 # will be set by record_manager
        self.pre_record_chunk_number = 0
        self.pre_record_buffer = None

        # timer
        self.timer = QTimer(self, singleShot=True)
        self.timer.timeout.connect(self.stop_auto_record)
        
    def configure(self, settings):
        """Configure auto recording settings"""
        # save settings
        self.auto_record_enabled = bool(settings.get('auto_record_enabled', False))
        self.auto_record_seconds = settings.get('auto_record_seconds', 10)
        self.auto_pre_record_seconds = settings.get('auto_pre_record_seconds', 0)
        self.auto_max_record_count = settings.get('auto_max_record_count', 100)
        self.auto_cf_enabled = bool(settings.get('auto_cf_enabled', False))
        self.auto_fz_enabled = bool(settings.get('auto_fz_enabled', False))
        self.auto_t_enabled = bool(settings.get('auto_t_enabled', False))
        self.auto_cf_threshold = settings.get('auto_cf_threshold', 0.0)
        self.auto_fz_threshold = settings.get('auto_fz_threshold', 0.0)
        self.auto_t_threshold = settings.get('auto_t_threshold', 0.0)
        self.sample_rate = settings.get('sample_rate', 10000)
        
        # Calculate pre record data chunks needed
        pre_record_data_points = int(self.auto_pre_record_seconds * self.sample_rate * 1.005) # calculate total number of data points, including 0.5% extra adxl data
        self.pre_record_chunk_number = int(math.ceil(pre_record_data_points / self.write_buffer_size))
        # Initialize pre-record buffer if needed
        if self.auto_pre_record_seconds > 0:
            # a deque containing chunks collected from record_manager
            # total amount of data collected is equivalent to that in specified pre_record_seconds
            self.pre_record_buffer = deque(maxlen=self.pre_record_chunk_number)

        # set timer interval
        self.timer.setInterval(self.auto_record_seconds * 1000)
        
        logger.info(f"Auto recording configured: "
                   f"auto_pre_record={self.auto_pre_record_seconds}s ({self.pre_record_chunk_number} chunks), "
                   f"auto_record={self.auto_record_seconds}s, "
                   f"sample_rate={self.sample_rate}")
        
    def check_triggers(self, sensor_data):
        """Check if any sensor values exceed thresholds"""
        # get sensor data
        x = sensor_data[0]
        y = sensor_data[1]
        cutting_force = math.sqrt(x*x + y*y)
        tension = sensor_data[2]
        torque = sensor_data[3]
        
        # Check each threshold
        trigger_reasons = []
        # cutting force
        if self.auto_cf_enabled and cutting_force >= self.auto_cf_threshold:
            trigger_reasons.append(f"CF({cutting_force:.2f} >= {self.auto_cf_threshold})")
        # tension
        if self.auto_fz_enabled and tension >= self.auto_fz_threshold:
            trigger_reasons.append(f"Fz({tension:.2f} >= {self.auto_fz_threshold})")
        # torque
        if self.auto_t_enabled and torque >= self.auto_t_threshold:
            trigger_reasons.append(f"T({torque:.2f} >= {self.auto_t_threshold})")
        
        # auto recording triggered
        if trigger_reasons:
            # generate string of trigger reasons
            self.trigger_reason = " + ".join(trigger_reasons)
            logger.info(f"Auto recording triggered: {self.trigger_reason}")
            # check if maximum record count is reached
            if self.record_count > self.auto_max_record_count:
                logger.warning(f"Maximum record count ({self.auto_max_record_count}) reached")
                return False
            # start recording, regardless if pre-record buffer is full
            self.start_auto_record()
            return True
            
        return False

    def start_auto_record(self):
        """start auto recording"""
        # emit signal
        self.sig_auto_record_triggered.emit(self.trigger_reason)
        # set recording state
        self.is_recording = True
        # start timer
        self.timer.start()

    def stop_auto_record(self):
        """stop auto recording"""
        # emit signal
        self.sig_auto_record_stop.emit()
        # set recording state
        self.is_recording = False

    def keep_pre_record_data(self, data_chunk):
        """save data in pre-record buffer"""
        # append chunk to pre-record buffer
        if self.auto_pre_record_seconds > 0:
            if self.pre_record_buffer is not None:
                self.pre_record_buffer.append(data_chunk)
                # logger.debug(f"pre-record buffer size: {len(self.pre_record_buffer)}")
            else:
                logger.error("Pre-record buffer is not initialized")
    
    def get_pre_record_data(self):
        """get data in pre-record buffer"""
        return list(itertools.chain.from_iterable(self.pre_record_buffer))
    
    def reset(self):
        """Reset auto recording state"""
        self.is_recording = False

        self.trigger_reason = ""
        self.current_data_point_count = 0
        if self.pre_record_buffer:
            self.pre_record_buffer.clear()
            self.pre_record_buffer_size = 0

        logger.info("Auto recording reset")
        
    def get_status(self):
        """Get current status"""
        return {
            'auto_record_enabled': self.auto_record_enabled,
            'is_recording': self.is_recording,
            'auto_pre_recording_enabled': self.auto_pre_record_seconds > 0,
            'record_count': self.record_count,
            'auto_max_record_count': self.auto_max_record_count,
            'current_data_points': self.current_data_point_count,
            'target_data_points': self.auto_record_data_points,
            'trigger_reason': self.trigger_reason,
            'pre_record_buffer_size': len(self.pre_record_buffer)
        } 