from app.models.database_model import DatabaseModel
from . import logger  # 從同一個包導入 logger
from config import NETWORK_DEVICE_URL, NETWORK_DEVICE_USERNAME, NETWORK_DEVICE_PASSWORD

import requests
import base64
import json
import pandas as pd
import os
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def login_and_get_arp_data(url=NETWORK_DEVICE_URL, username=NETWORK_DEVICE_USERNAME, password=NETWORK_DEVICE_PASSWORD):
    """
    Login to the device and fetch ARP table data

    Args:
        url: Base URL of the device (e.g., 'https://192.168.0.1')
        username: Login username
        password: Login password

    Returns:
        ARP table data as a list of dictionaries
    """
    # Create a session to maintain cookies
    session = requests.Session()

    # Login URL
    login_url = f"{url}/cgi-bin/luci/"

    try:
        # Get login page to establish cookies
        session.get(login_url, verify=False, timeout=10)

        # Encode credentials in base64
        encoded_username = base64.b64encode(username.encode()).decode()
        encoded_password = base64.b64encode(password.encode()).decode()

        # Login data
        login_data = {
            'luci_username': encoded_username,
            'luci_password': encoded_password
        }

        # Perform login
        logger.info(f"Logging in to {login_url}...")
        response = session.post(
            login_url, 
            data=login_data, 
            verify=False,
            timeout=10
        )

        # Check if login was successful
        if "Login" in response.text and "Username" in response.text and "Password" in response.text:
            logger.error("Login failed. Check your credentials.")
            return None

        logger.info("Login successful!")

        # Now, fetch the ARP table data using the exact endpoint from the JavaScript code
        arp_url = f"{url}/cgi-bin/luci/admin/statistics?id=0"

        # Add the status=1 parameter exactly as shown in the JavaScript
        params = {
            'status': '1'
        }

        logger.info(f"Fetching ARP data from {arp_url}...")
        response = session.get(
            arp_url,
            params=params,
            verify=False,
            timeout=10,
            headers={
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        )

        # Check response
        if response.status_code == 200:
            try:
                data = response.json()
                logger.info("Successfully retrieved ARP data")
                return data
            except json.JSONDecodeError:
                logger.error("Error: Received non-JSON response")
                logger.error(f"Response content: {response.text[:200]}...")  # Log first 200 chars for debugging
                return None
        else:
            logger.error(f"Error: Received status code {response.status_code}")
            return None

    except requests.exceptions.RequestException as e:
        logger.error(f"Error during request: {e}")
        return None

def check_mac_in_database(mac_address, table_name="tool_magazine_temp"):
    """
    檢查 MAC 地址是否已存在於資料庫中
    
    參數:
        mac_address (str): 要搜尋的 MAC 地址
        table_name (str): 資料表名稱
    
    回傳:
        tuple: (是否找到裝置, 裝置資訊)
    """
    # 正規化 MAC 地址格式
    mac_address = mac_address.upper()
    
    # 檢查資料庫中是否已有此 MAC
    db = DatabaseModel()
    db_result = db.get_query(
        table_name=table_name,
        conditions={"toolmac": mac_address}
    )
    
    if db_result:
        logger.info(f"✅ 資料庫中已存在此 MAC: {mac_address}")
        db.close_db()
        return True, db_result[0]
    
    db.close_db()
    return False, None

def check_mac_in_network(mac_address, table_name="tool_magazine_temp"):
    """
    在網路中搜尋特定 MAC 地址，檢查是否存在並更新資料庫
    
    參數:
        mac_address (str): 要搜尋的 MAC 地址
        table_name (str): 資料表名稱
    
    回傳:
        tuple: (是否找到裝置, 裝置資訊)
    """
    # 正規化 MAC 地址格式
    mac_address = mac_address.upper()
    
    # 進行爬蟲檢查
    arp_data = login_and_get_arp_data()
    
    if not arp_data or "client" not in arp_data:
        logger.error("⚠️ 無法取得 ARP 資料")
        return False, None
    
    # 在 ARP 資料中搜尋特定 MAC
    device_info = None
    for device in arp_data["client"]:
        if device.get("bssid", "").upper() == mac_address:
            device_info = {
                "toolmac": mac_address,
                "toolip": device.get("ip", ""),
                "toolname": f"default_{device.get('ip', '')}",  # 使用 MAC 後六位作為預設名稱
                "sample_rate": 10000,  # 預設值
                "tare_xv": 2.5,  # 預設值
                "tare_yv": 2.5,  # 預設值
                "tare_zv": 2.5,  # 預設值
                "tare_tv": 2.5,  # 預設值
                "Linear_x": 1446.0,  # 預設值
                "Linear_y": 1465.0,  # 預設值
                "Linear_z": 31094.0,  # 預設值
                "Linear_t": 1.0,  # 預設值
                "Lc": 0.136,  # 預設值
                "Hl": 1.1,  # 預設值
                "Kl": 0.04,  # 預設值
                "Bx_COMP": 1446.0,  # 預設值
                "By_COMP": 1465.0,  # 預設值
                "Bz_COMP": 31094.0,  # 預設值
                "Bt_COMP": 1.0  # 預設值
            }
            break
    
    if device_info:
        logger.info(f"✅ 找到新裝置: MAC={mac_address}, IP={device_info['toolip']}")
        # 新增到資料庫
        db = DatabaseModel()
        db.add_item(table_name=table_name, **device_info)
        logger.info(f"✅ 已新增到資料庫")
        db.close_db()
    else:
        logger.info(f"❌ 未找到 MAC: {mac_address}")
    
    return device_info is not None, device_info

def search_all_network_devices():
    """
    搜尋網路中所有的設備
    
    回傳:
        dict: 以 MAC 地址為鍵的設備資訊字典
    """
    # 進行爬蟲檢查
    arp_data = login_and_get_arp_data()
    
    if not arp_data or "client" not in arp_data:
        logger.error("⚠️ 無法取得 ARP 資料")
        return {}
    
    # 整理所有設備資訊，使用 MAC 地址作為鍵
    devices = {}
    for device in arp_data["client"]:
        mac_address = device.get("bssid", "").upper()
        if mac_address:  # 只處理有效的 MAC 地址
            devices[mac_address] = {
                "ip_address": device.get("ip", ""),
            }
            logger.info(f"✅ 發現設備: MAC={mac_address}, IP={devices[mac_address]['ip_address']}")
    
    logger.info(f"✅ 總共發現 {len(devices)} 個設備")
    return devices




