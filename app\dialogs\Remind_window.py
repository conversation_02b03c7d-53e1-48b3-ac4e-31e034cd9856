from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
from .message_box import Ui_Dialog  

class Ui_Remind_Window(QDialog, Ui_Dialog):
    def __init__(self, message, parent=None):
        super().__init__(parent)
        self.setupUi(self)

        # 設定無邊框
        self.setWindowFlags(self.windowFlags() | Qt.FramelessWindowHint)


        # 設定訊息內容
        self.set_message(message)

        # 刪除 Yes 按鈕
        self.remove_yes_button()

        # 修改 No 按鈕為 OK 按鈕
        self.btv_yes_btn.setText("OK")  # 修改按鈕文字

        # 綁定 OK 按鈕的 accept 事件
        self.btv_yes_btn.clicked.connect(self.accept)

    def set_message(self, message):
        """更新對話框的訊息"""
        self.message.setText(message)

    def remove_yes_button(self):
        """刪除 Yes 按鈕"""
        try:
            self.btn_no_btn.deleteLater()
            self.btn_no_btn.setParent(None)
        except AttributeError:
            pass  # 避免 UI 沒有該按鈕時發生錯誤

    def get_result(self):
        """顯示對話框並回傳使用者的選擇"""
        self.exec_()
        return True  # 只有 OK 按鈕，固定回傳 True
