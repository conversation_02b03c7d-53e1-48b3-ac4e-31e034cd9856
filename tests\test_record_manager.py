import pytest
import os
import shutil
import random
from app.models.record_manager import RecordManager

TEST_DIR = "test_dir"
TEST_FILENAME = "test_file"
TEST_TOOL_DATA = {
    "toolname": "test_tool",
    "toolip": "***********",
    "sample_rate": 10000,
    "tare_xv": 1,
    "tare_yv": 2,
    "tare_zv": 3,
    "tare_tv": 4,
    "Linear_x": 5,
    "Linear_y": 6,
    "Linear_z": 7,
    "Lc": 8,
    "Hl": 9,
    "Kl": 10,
    "Bx_COMP": 11,
    "By_COMP": 12,
    "Bz_COMP": 13,
    "Bt_COMP": 14,
}
TEST_SENSOR_DATA = [1.123456789, 2.123456789, 3.123456789, 4.123456789, 5.123456789]

@pytest.fixture
def record_manager():
    rm =  RecordManager()
    yield rm
    # clean up
    for manager in rm.record_file_manager_list.values():
        manager.close_file()
    for manager in rm.raw_data_file_manager_list.values():
        manager.close_file()
    for test_dir in ["create", "release", "start_manual", "stop_manual", "process_data"]:
        if os.path.exists(f"{TEST_DIR}_{test_dir}"):
            shutil.rmtree(f"{TEST_DIR}_{test_dir}")

def test_configure_tool_info(record_manager):
    record_manager.configure_tool_info(TEST_TOOL_DATA)
    assert record_manager.tool_name == TEST_TOOL_DATA["toolname"]
    assert record_manager.tool_info_text == f"!,{TEST_TOOL_DATA['sample_rate']},{round(TEST_TOOL_DATA['tare_xv'],2)},{round(TEST_TOOL_DATA['tare_yv'],2)},{round(TEST_TOOL_DATA['tare_zv'],2)},{round(TEST_TOOL_DATA['tare_tv'],2)},{TEST_TOOL_DATA['Linear_x']},{TEST_TOOL_DATA['Linear_y']},{TEST_TOOL_DATA['Linear_z']},-1,0,0,0,0,0,0,0,0,0,0,0,-1,{TEST_TOOL_DATA['Lc']},{TEST_TOOL_DATA['Hl']},{TEST_TOOL_DATA['Kl']},{TEST_TOOL_DATA['Bx_COMP']},{TEST_TOOL_DATA['By_COMP']},{TEST_TOOL_DATA['Bz_COMP']},{TEST_TOOL_DATA['Bt_COMP']}\n"

def test_configure_record_path(record_manager):
    record_manager.configure_record_path("directory", TEST_DIR)
    record_manager.configure_record_path("manual", TEST_FILENAME)
    assert record_manager.record_directory == TEST_DIR
    assert record_manager.manual_record_filename == TEST_FILENAME

def test_create_file(record_manager):
    unique_dir = f"{TEST_DIR}_create"
    record_manager.save_raw_data = True
    record_manager.create_file(unique_dir, TEST_FILENAME)
    assert os.path.exists(f"{unique_dir}/{TEST_FILENAME}") and os.path.exists(f"{unique_dir}/raw_data_{TEST_FILENAME}")

def test_release_file(record_manager):
    unique_dir = f"{TEST_DIR}_release"
    record_manager.save_raw_data = True
    record_manager.create_file(unique_dir, TEST_FILENAME)
    assert os.path.exists(f"{unique_dir}/{TEST_FILENAME}") and os.path.exists(f"{unique_dir}/raw_data_{TEST_FILENAME}")
    record_file_handle = record_manager.record_file_manager_list[TEST_FILENAME].file_handle
    raw_data_file_handle = record_manager.raw_data_file_manager_list[f"raw_data_{TEST_FILENAME}"].file_handle
    record_manager.release_file(TEST_FILENAME)
    assert record_file_handle.closed and raw_data_file_handle.closed

def test_start_manual_record(record_manager):
    unique_dir = f"{TEST_DIR}_start_manual"
    record_manager.save_raw_data = True
    record_manager.configure_tool_info(TEST_TOOL_DATA)
    record_manager.configure_record_path("directory", unique_dir)
    record_manager.configure_record_path("manual", TEST_FILENAME)
    manual_filename = record_manager.start_manual_record()
    assert record_manager.manual_is_recording
    assert os.path.exists(f"{unique_dir}/{manual_filename}")
    assert os.path.exists(f"{unique_dir}/raw_data_{manual_filename}")

def test_stop_manual_record(record_manager):
    unique_dir = f"{TEST_DIR}_stop_manual"
    record_manager.save_raw_data = True
    record_manager.configure_tool_info(TEST_TOOL_DATA)
    record_manager.configure_record_path("directory", unique_dir)
    record_manager.configure_record_path("manual", TEST_FILENAME)
    manual_filename = record_manager.start_manual_record()
    assert record_manager.manual_is_recording
    record_manager.stop_manual_record()
    assert not record_manager.manual_is_recording
    assert os.path.exists(f"{unique_dir}/{manual_filename}")
    assert os.path.exists(f"{unique_dir}/raw_data_{manual_filename}")

def test_process_data_no_raw_data(record_manager):
    unique_dir = f"{TEST_DIR}_process_data"
    record_manager.configure_tool_info(TEST_TOOL_DATA)
    record_manager.configure_record_path("directory", unique_dir)
    record_manager.configure_record_path("manual", TEST_FILENAME)
    manual_filename = record_manager.start_manual_record()
    assert record_manager.manual_is_recording
    for i in range(3):
        record_manager.process_data(TEST_SENSOR_DATA)
    assert len(record_manager.record_data_lines) == 3
    record_manager.write_record_data()
    assert len(record_manager.record_data_lines) == 0
    record_manager.stop_manual_record()
    assert not record_manager.manual_is_recording
    assert os.path.exists(f"{unique_dir}/{manual_filename}")
    with open(f"{unique_dir}/{manual_filename}", "r") as f:
        lines = f.readlines()
        assert len(lines) == 4
        assert lines[0] == f"!,{TEST_TOOL_DATA['sample_rate']},{round(TEST_TOOL_DATA['tare_xv'],2)},{round(TEST_TOOL_DATA['tare_yv'],2)},{round(TEST_TOOL_DATA['tare_zv'],2)},{round(TEST_TOOL_DATA['tare_tv'],2)},{TEST_TOOL_DATA['Linear_x']},{TEST_TOOL_DATA['Linear_y']},{TEST_TOOL_DATA['Linear_z']},-1,0,0,0,0,0,0,0,0,0,0,0,-1,{TEST_TOOL_DATA['Lc']},{TEST_TOOL_DATA['Hl']},{TEST_TOOL_DATA['Kl']},{TEST_TOOL_DATA['Bx_COMP']},{TEST_TOOL_DATA['By_COMP']},{TEST_TOOL_DATA['Bz_COMP']},{TEST_TOOL_DATA['Bt_COMP']}\n"
        for i in range(3):
            assert lines[i+1] == f"1.123,2.123,3.123,4.123,5.123\n"