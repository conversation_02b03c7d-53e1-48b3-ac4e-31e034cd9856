# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'Filter_window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

from app.models.filters import Filter
import system_image.system_image_rc

class Ui_Filter_Window(object):
    def setupUi(self, Filter_Window):
        if not Filter_Window.objectName():
            Filter_Window.setObjectName(u"Filter_Window")
        Filter_Window.resize(330, 450)
        Filter_Window.setMinimumSize(QSize(330, 450))
        Filter_Window.setMaximumSize(QSize(330, 450))
        Filter_Window.setStyleSheet(u"QWidget#Filter_Window{background-color: #0C0C44;\n"
            "    border: 2px solid #5448B6;}")
        self.verticalLayout_3 = QVBoxLayout(Filter_Window)
        self.verticalLayout_3.setSpacing(15)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.filter_title = QWidget(Filter_Window)
        self.filter_title.setObjectName(u"filter_title")
        self.filter_title.setStyleSheet(u"QWidget#filter_title{\n"
            "background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
            "border-radius:5px;\n"
            "}\n"
            "")
        self.horizontalLayout = QHBoxLayout(self.filter_title)
        self.horizontalLayout.setSpacing(3)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(5, 0, 0, 0)
        self.filter_title_label = QLabel(self.filter_title)
        self.filter_title_label.setObjectName(u"filter_title_label")
        font = QFont()
        font.setFamily(u"Arial Black")
        font.setPointSize(12)
        self.filter_title_label.setFont(font)
        self.filter_title_label.setLayoutDirection(Qt.LeftToRight)
        self.filter_title_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.filter_title_label.setAlignment(Qt.AlignCenter)
        self.filter_title_label.setMargin(2)

        self.horizontalLayout.addWidget(self.filter_title_label)

        self.horizontalSpacer_1 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_1)


        self.verticalLayout_3.addWidget(self.filter_title)

        self.Filter_Layout = QVBoxLayout()
        self.Filter_Layout.setSpacing(15)
        self.Filter_Layout.setObjectName(u"Filter_Layout")
        self.Filter_Layout.setContentsMargins(5, -1, 5, -1)
        self.Nofilter_radio = QRadioButton(Filter_Window)
        self.Nofilter_radio.setObjectName(u"Nofilter_radio")
        # self.Nofilter_radio.setChecked(True)
        font1 = QFont()
        font1.setFamily(u"Arial Black")
        font1.setPointSize(10)
        self.Nofilter_radio.setFont(font1)
        self.Nofilter_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
            "\n"
            "\n"
            "QRadioButton::indicator:unchecked{\n"
            "image: url(:/btn_radio/btn_radio_s0_0.png);\n"
            "}\n"
            "QRadioButton::indicator:unchecked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s0_1.png);\n"
            "}\n"
            "QRadioButton::indicator:checked{\n"
            "image: url(:/btn_radio/btn_radio_s1_0.png);\n"
            "}\n"
            "QRadioButton::indicator:checked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s1_1.png);\n"
            "}\n"
            "	")

        self.Filter_Layout.addWidget(self.Nofilter_radio)

        self.Meanfilter_radio = QRadioButton(Filter_Window)
        self.Meanfilter_radio.setObjectName(u"Meanfilter_radio")
        self.Meanfilter_radio.setFont(font1)
        self.Meanfilter_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
            "\n"
            "\n"
            "QRadioButton::indicator:unchecked{\n"
            "image: url(:/btn_radio/btn_radio_s0_0.png);\n"
            "}\n"
            "QRadioButton::indicator:unchecked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s0_1.png);\n"
            "}\n"
            "QRadioButton::indicator:checked{\n"
            "image: url(:/btn_radio/btn_radio_s1_0.png);\n"
            "}\n"
            "QRadioButton::indicator:checked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s1_1.png);\n"
            "}\n"
            "	")

        self.Filter_Layout.addWidget(self.Meanfilter_radio)

        self.Medianfilter_radio = QRadioButton(Filter_Window)
        self.Medianfilter_radio.setObjectName(u"Medianfilter_radio")
        self.Medianfilter_radio.setFont(font1)
        self.Medianfilter_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
            "\n"
            "\n"
            "QRadioButton::indicator:unchecked{\n"
            "image: url(:/btn_radio/btn_radio_s0_0.png);\n"
            "}\n"
            "QRadioButton::indicator:unchecked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s0_1.png);\n"
            "}\n"
            "QRadioButton::indicator:checked{\n"
            "image: url(:/btn_radio/btn_radio_s1_0.png);\n"
            "}\n"
            "QRadioButton::indicator:checked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s1_1.png);\n"
            "}\n"
            "	")

        self.Filter_Layout.addWidget(self.Medianfilter_radio)

        self.Gaussianfilter_radio = QRadioButton(Filter_Window)
        self.Gaussianfilter_radio.setObjectName(u"Gaussianfilter_radio")
        self.Gaussianfilter_radio.setFont(font1)
        self.Gaussianfilter_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
            "\n"
            "\n"
            "QRadioButton::indicator:unchecked{\n"
            "image: url(:/btn_radio/btn_radio_s0_0.png);\n"
            "}\n"
            "QRadioButton::indicator:unchecked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s0_1.png);\n"
            "}\n"
            "QRadioButton::indicator:checked{\n"
            "image: url(:/btn_radio/btn_radio_s1_0.png);\n"
            "}\n"
            "QRadioButton::indicator:checked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s1_1.png);\n"
            "}\n"
            "	")

        self.Filter_Layout.addWidget(self.Gaussianfilter_radio)

        self.MSfilter_radio = QRadioButton(Filter_Window)
        self.MSfilter_radio.setObjectName(u"MSfilter_radio")
        self.MSfilter_radio.setFont(font1)
        self.MSfilter_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
            "\n"
            "\n"
            "QRadioButton::indicator:unchecked{\n"
            "image: url(:/btn_radio/btn_radio_s0_0.png);\n"
            "}\n"
            "QRadioButton::indicator:unchecked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s0_1.png);\n"
            "}\n"
            "QRadioButton::indicator:checked{\n"
            "image: url(:/btn_radio/btn_radio_s1_0.png);\n"
            "}\n"
            "QRadioButton::indicator:checked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s1_1.png);\n"
            "}\n"
            "	")

        self.Filter_Layout.addWidget(self.MSfilter_radio)

        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.filter_label = QLabel(Filter_Window)
        self.filter_label.setObjectName(u"filter_label")
        self.filter_label.setFont(font1)
        self.filter_label.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.horizontalLayout_2.addWidget(self.filter_label)

        self.filter_Slider = QSlider(Filter_Window)
        self.filter_Slider.setObjectName(u"filter_Slider")
        self.filter_Slider.setStyleSheet(u"QSlider::groove:horizontal { \n"
            "border: 1px solid #bbb; \n"
            "background: white; \n"
            "height: 10px; \n"
            "border-radius: 4px; \n"
            "} \n"
            "   \n"
            "QSlider::sub-page:horizontal { \n"
            "background: qlineargradient(x1: 0, y1: 0.2, x2: 1, y2: 1, \n"
            "    stop: 0 #bbf, stop: 1 #55f); \n"
            "border: 1px solid #777; \n"
            "height: 10px; \n"
            "border-radius: 4px; \n"
            "} \n"
            "   \n"
            "QSlider::add-page:horizontal { \n"
            "background: #fff; \n"
            "border: 1px solid #777; \n"
            "height: 10px; \n"
            "border-radius: 4px; \n"
            "} \n"
            "   \n"
            "QSlider::handle:horizontal { \n"
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, \n"
            "    stop:0 #eee, stop:1 #ccc); \n"
            "border: 1px solid #777; \n"
            "width: 13px; \n"
            "margin-top: -2px; \n"
            "margin-bottom: -2px; \n"
            "border-radius: 4px; \n"
            "} \n"
            "   \n"
            "QSlider::handle:horizontal:hover { \n"
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, \n"
            "    stop:0 #fff, stop:1 #ddd); \n"
            "border: 1px solid #444; \n"
            "border-radius: 4px; \n"
            "} \n"
            "   \n"
            "QSlider::sub-page:horizontal:disabled { \n"
            ""
                                    "background: #bbb; \n"
            "border-color: #999; \n"
            "} \n"
            "   \n"
            "QSlider::add-page:horizontal:disabled { \n"
            "background: #eee; \n"
            "border-color: #999; \n"
            "} \n"
            "   \n"
            "QSlider::handle:horizontal:disabled { \n"
            "background: #eee; \n"
            "border: 1px solid #aaa; \n"
            "border-radius: 4px; \n"
            "} ")
        self.filter_Slider.setOrientation(Qt.Horizontal)
        self.filter_Slider.setMaximum(Filter.MAX_WINDOW_SIZE)
        self.filter_Slider.setMinimum(Filter.MIN_WINDOW_SIZE)
        self.horizontalLayout_2.addWidget(self.filter_Slider)


        self.Filter_Layout.addLayout(self.horizontalLayout_2)


        self.verticalLayout_3.addLayout(self.Filter_Layout)

        self.Lowfilter_Layout = QVBoxLayout()
        self.Lowfilter_Layout.setSpacing(15)
        self.Lowfilter_Layout.setObjectName(u"Lowfilter_Layout")
        self.Lowfilter_Layout.setContentsMargins(5, -1, 5, -1)
        self.LowfilterLayout = QHBoxLayout()
        self.LowfilterLayout.setObjectName(u"LowfilterLayout")
        self.Lowfilter_radio = QRadioButton(Filter_Window)
        self.Lowfilter_radio.setObjectName(u"Lowfilter_radio")
        self.Lowfilter_radio.setFont(font1)
        self.Lowfilter_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
            "\n"
            "\n"
            "QRadioButton::indicator:unchecked{\n"
            "image: url(:/btn_radio/btn_radio_s0_0.png);\n"
            "}\n"
            "QRadioButton::indicator:unchecked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s0_1.png);\n"
            "}\n"
            "QRadioButton::indicator:checked{\n"
            "image: url(:/btn_radio/btn_radio_s1_0.png);\n"
            "}\n"
            "QRadioButton::indicator:checked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s1_1.png);\n"
            "}\n"
            "	")

        self.LowfilterLayout.addWidget(self.Lowfilter_radio)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.LowfilterLayout.addItem(self.horizontalSpacer)

        self.Lowfilter_lineEdit = QLineEdit(Filter_Window)
        self.Lowfilter_lineEdit.setObjectName(u"Lowfilter_lineEdit")
        self.Lowfilter_lineEdit.setText(u"999")
        self.LowfilterLayout.addWidget(self.Lowfilter_lineEdit)

        self.LowfilterLayout.setStretch(0, 3)
        self.LowfilterLayout.setStretch(1, 1)
        self.LowfilterLayout.setStretch(2, 2)

        self.Lowfilter_Layout.addLayout(self.LowfilterLayout)

        self.Highfilter_Layout = QHBoxLayout()
        self.Highfilter_Layout.setObjectName(u"Highfilter_Layout")
        self.Highfilter_radio = QRadioButton(Filter_Window)
        self.Highfilter_radio.setObjectName(u"Highfilter_radio")
        self.Highfilter_radio.setFont(font1)
        self.Highfilter_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
            "\n"
            "\n"
            "QRadioButton::indicator:unchecked{\n"
            "image: url(:/btn_radio/btn_radio_s0_0.png);\n"
            "}\n"
            "QRadioButton::indicator:unchecked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s0_1.png);\n"
            "}\n"
            "QRadioButton::indicator:checked{\n"
            "image: url(:/btn_radio/btn_radio_s1_0.png);\n"
            "}\n"
            "QRadioButton::indicator:checked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s1_1.png);\n"
            "}\n"
            "	")

        self.Highfilter_Layout.addWidget(self.Highfilter_radio)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.Highfilter_Layout.addItem(self.horizontalSpacer_2)

        self.Highfilter_lineEdit = QLineEdit(Filter_Window)
        self.Highfilter_lineEdit.setObjectName(u"Highfilter_lineEdit")
        self.Highfilter_lineEdit.setText(u"1")

        self.Highfilter_Layout.addWidget(self.Highfilter_lineEdit)

        self.Highfilter_Layout.setStretch(0, 3)
        self.Highfilter_Layout.setStretch(1, 1)
        self.Highfilter_Layout.setStretch(2, 2)

        self.Lowfilter_Layout.addLayout(self.Highfilter_Layout)

        self.SGfilter_Layout = QHBoxLayout()
        self.SGfilter_Layout.setObjectName(u"SGfilter_Layout")
        self.SGfilter_radio = QRadioButton(Filter_Window)
        self.SGfilter_radio.setObjectName(u"SGfilter_radio")
        self.SGfilter_radio.setFont(font1)
        self.SGfilter_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
            "\n"
            "\n"
            "QRadioButton::indicator:unchecked{\n"
            "image: url(:/btn_radio/btn_radio_s0_0.png);\n"
            "}\n"
            "QRadioButton::indicator:unchecked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s0_1.png);\n"
            "}\n"
            "QRadioButton::indicator:checked{\n"
            "image: url(:/btn_radio/btn_radio_s1_0.png);\n"
            "}\n"
            "QRadioButton::indicator:checked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s1_1.png);\n"
            "}\n"
            "	")

        self.SGfilter_Layout.addWidget(self.SGfilter_radio)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.SGfilter_Layout.addItem(self.horizontalSpacer_4)

        self.SGfilter_lineEdit = QLineEdit(Filter_Window)
        self.SGfilter_lineEdit.setObjectName(u"SGfilter_lineEdit")
        self.SGfilter_lineEdit.setText(u"4")

        self.SGfilter_Layout.addWidget(self.SGfilter_lineEdit)

        self.SGfilter_Layout.setStretch(0, 2)
        self.SGfilter_Layout.setStretch(1, 1)
        self.SGfilter_Layout.setStretch(2, 4)

        self.Lowfilter_Layout.addLayout(self.SGfilter_Layout)

        self.MAfilter_Layout = QHBoxLayout()
        self.MAfilter_Layout.setObjectName(u"MAfilter_Layout")
        self.MAfilter_radio = QRadioButton(Filter_Window)
        self.MAfilter_radio.setObjectName(u"MAfilter_radio")
        self.MAfilter_radio.setFont(font1)
        self.MAfilter_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
            "\n"
            "\n"
            "QRadioButton::indicator:unchecked{\n"
            "image: url(:/btn_radio/btn_radio_s0_0.png);\n"
            "}\n"
            "QRadioButton::indicator:unchecked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s0_1.png);\n"
            "}\n"
            "QRadioButton::indicator:checked{\n"
            "image: url(:/btn_radio/btn_radio_s1_0.png);\n"
            "}\n"
            "QRadioButton::indicator:checked:hover {\n"
            "image: url(:/btn_radio/btn_radio_s1_1.png);\n"
            "}\n"
            "	")

        self.MAfilter_Layout.addWidget(self.MAfilter_radio)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.MAfilter_Layout.addItem(self.horizontalSpacer_3)

        self.MAfilter_lineEdit = QLineEdit(Filter_Window)
        self.MAfilter_lineEdit.setObjectName(u"MAfilter_lineEdit")
        self.MAfilter_lineEdit.setText(u"1")

        self.MAfilter_Layout.addWidget(self.MAfilter_lineEdit)

        self.MAfilter_Layout.setStretch(0, 3)
        self.MAfilter_Layout.setStretch(1, 1)
        self.MAfilter_Layout.setStretch(2, 2)

        self.Lowfilter_Layout.addLayout(self.MAfilter_Layout)


        self.verticalLayout_3.addLayout(self.Lowfilter_Layout)

        self.filter_save_close = QWidget(Filter_Window)
        self.filter_save_close.setObjectName(u"filter_save_close")
        self.horizontalLayout_9 = QHBoxLayout(self.filter_save_close)
        self.horizontalLayout_9.setSpacing(0)
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_10 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_10)

        self.filter_save_btn = QPushButton(self.filter_save_close)
        self.filter_save_btn.setObjectName(u"filter_save_btn")
        self.filter_save_btn.setMinimumSize(QSize(10, 10))
        self.filter_save_btn.setFont(font1)
        self.filter_save_btn.setStyleSheet(u"QPushButton {\n"
            "    color: rgb(255, 255, 255);\n"
            "    background-color:#5448B6;\n"
            "    border: 2px solid #5448B6;\n"
            "	border-radius:5px;\n"
            "	padding:2px;\n"
            "}\n"
            "\n"
            "QPushButton:hover {\n"
            "    background-color:#7AFEC6;\n"
            "     color: rgb(255, 255, 255);\n"
            "    border:none\n"
            "}\n"
            "\n"
            "QPushButton:pressed {\n"
            "    background-color: gray;\n"
            "	border:none\n"
            "}")
        self.filter_save_btn.setIconSize(QSize(20, 20))
        self.filter_save_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.filter_save_btn)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_5)

        self.filter_close_btn = QPushButton(self.filter_save_close)
        self.filter_close_btn.setObjectName(u"filter_close_btn")
        self.filter_close_btn.setMinimumSize(QSize(10, 10))
        self.filter_close_btn.setFont(font1)
        self.filter_close_btn.setStyleSheet(u"QPushButton {\n"
            "    color: rgb(255, 255, 255);\n"
            "    background-color: #0C0C44;\n"
            "    border: 2px solid #5448B6;\n"
            "	border-radius:5px;\n"
            "	padding:2px;\n"
            "}\n"
            "\n"
            "QPushButton:hover {\n"
            "    background-color: #0C0C44;\n"
            "	border: 2px solid #7AFEC6;\n"
            "    color: rgb(255, 255, 255);\n"
            "	border-radius:5px;\n"
            "	padding:2px;\n"
            "}\n"
            "\n"
            "QPushButton:pressed {\n"
            "    background-color: gray;\n"
            "	border: none;\n"
            "}")
        self.filter_close_btn.setIconSize(QSize(20, 20))
        self.filter_close_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.filter_close_btn)

        self.horizontalLayout_9.setStretch(0, 8)
        self.horizontalLayout_9.setStretch(1, 3)
        self.horizontalLayout_9.setStretch(2, 1)
        self.horizontalLayout_9.setStretch(3, 3)

        self.verticalLayout_3.addWidget(self.filter_save_close)


        self.retranslateUi(Filter_Window)

        QMetaObject.connectSlotsByName(Filter_Window)
    # setupUi

    def retranslateUi(self, Filter_Window):
        Filter_Window.setWindowTitle(QCoreApplication.translate("Filter_Window", u"Form", None))
        self.filter_title_label.setText(QCoreApplication.translate("Filter_Window", u"\u6ffe\u6ce2\u5668", None))
        self.Nofilter_radio.setText(QCoreApplication.translate("Filter_Window", u"\u7121\u6ffe\u6ce2\u5668", None))
        self.Meanfilter_radio.setText(QCoreApplication.translate("Filter_Window", u"\u5747\u503c\u6ffe\u6ce2\u5668", None))
        self.Medianfilter_radio.setText(QCoreApplication.translate("Filter_Window", u"\u4e2d\u503c\u6ffe\u6ce2\u5668", None))
        self.Gaussianfilter_radio.setText(QCoreApplication.translate("Filter_Window", u"\u9ad8\u65af\u6ffe\u6ce2\u5668", None))
        self.MSfilter_radio.setText(QCoreApplication.translate("Filter_Window", u"Machsync\u6ffe\u6ce2\u5668", None))
        self.filter_label.setText(QCoreApplication.translate("Filter_Window", u"\u6ffe\u6ce2\u503c:1", None))
        self.Lowfilter_radio.setText(QCoreApplication.translate("Filter_Window", u"\u4f4e\u901a\u6ffe\u6ce2\u5668", None))
        self.Highfilter_radio.setText(QCoreApplication.translate("Filter_Window", u"\u9ad8\u901a\u6ffe\u6ce2\u5668", None))
        self.SGfilter_radio.setText(QCoreApplication.translate("Filter_Window", u"Savitzky-Golay\u6ffe\u6ce2\u5668", None))
        self.MAfilter_radio.setText(QCoreApplication.translate("Filter_Window", u"\u7c21\u55ae\u79fb\u52d5\u5e73\u5747", None))
        self.filter_save_btn.setText(QCoreApplication.translate("Filter_Window", u"\u5132\u5b58", None))
        self.filter_close_btn.setText(QCoreApplication.translate("Filter_Window", u"\u95dc\u9589", None))
    # retranslateUi

