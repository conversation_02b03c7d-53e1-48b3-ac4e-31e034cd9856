# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'Toolinfo_window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

import system_image.system_image_rc

class Ui_ToolInfo_Window(object):
    def setupUi(self, ToolInfo_Window):
        if not ToolInfo_Window.objectName():
            ToolInfo_Window.setObjectName(u"ToolInfo_Window")
        ToolInfo_Window.resize(450, 690)
        sizePolicy = QSizePolicy(QSizePolicy.Maximum, QSizePolicy.MinimumExpanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(ToolInfo_Window.sizePolicy().hasHeightForWidth())
        ToolInfo_Window.setSizePolicy(sizePolicy)
        ToolInfo_Window.setMinimumSize(QSize(450, 640))
        ToolInfo_Window.setMaximumSize(QSize(450, 690))
        ToolInfo_Window.setStyleSheet(u"QWidget#ToolInfo_Window{background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;}")
        self.verticalLayout_3 = QVBoxLayout(ToolInfo_Window)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.toolinfo_title = QWidget(ToolInfo_Window)
        self.toolinfo_title.setObjectName(u"toolinfo_title")
        self.toolinfo_title.setStyleSheet(u"QWidget#toolinfo_title{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout = QHBoxLayout(self.toolinfo_title)
        self.horizontalLayout.setSpacing(3)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(2, 2, 2, 2)
        self.toolinfo_title_label = QLabel(self.toolinfo_title)
        self.toolinfo_title_label.setObjectName(u"toolinfo_title_label")
        font = QFont()
        font.setFamily(u"Arial Black")
        font.setPointSize(12)
        self.toolinfo_title_label.setFont(font)
        self.toolinfo_title_label.setLayoutDirection(Qt.LeftToRight)
        self.toolinfo_title_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.toolinfo_title_label.setAlignment(Qt.AlignCenter)
        self.toolinfo_title_label.setMargin(2)

        self.horizontalLayout.addWidget(self.toolinfo_title_label)

        self.horizontalSpacer_1 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_1)


        self.verticalLayout_3.addWidget(self.toolinfo_title)

        self.Toolinfo_group = QWidget(ToolInfo_Window)
        self.Toolinfo_group.setObjectName(u"Toolinfo_group")
        self.toolinfo_area1 = QHBoxLayout(self.Toolinfo_group)
        self.toolinfo_area1.setSpacing(0)
        self.toolinfo_area1.setObjectName(u"toolinfo_area1")
        self.toolinfo_area1.setContentsMargins(-1, 1, 1, 5)
        self.toolinfo_edit_group = QWidget(self.Toolinfo_group)
        self.toolinfo_edit_group.setObjectName(u"toolinfo_edit_group")
        sizePolicy1 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.toolinfo_edit_group.sizePolicy().hasHeightForWidth())
        self.toolinfo_edit_group.setSizePolicy(sizePolicy1)
        self.grid_toolinfo_area1 = QGridLayout(self.toolinfo_edit_group)
        self.grid_toolinfo_area1.setObjectName(u"grid_toolinfo_area1")
        self.grid_toolinfo_area1.setHorizontalSpacing(8)
        self.grid_toolinfo_area1.setContentsMargins(2, -1, -1, -1)
        self.toolinfo_label_name = QLabel(self.toolinfo_edit_group)
        self.toolinfo_label_name.setObjectName(u"toolinfo_label_name")
        font1 = QFont()
        font1.setFamily(u"Arial Black")
        font1.setPointSize(10)
        self.toolinfo_label_name.setFont(font1)
        self.toolinfo_label_name.setLayoutDirection(Qt.LeftToRight)
        self.toolinfo_label_name.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.toolinfo_label_name.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.toolinfo_label_name.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.toolinfo_label_name, 0, 0, 1, 1)

        self.toolinfoName_lineEdit = QLineEdit(self.toolinfo_edit_group)
        self.toolinfoName_lineEdit.setObjectName(u"toolinfoName_lineEdit")

        self.grid_toolinfo_area1.addWidget(self.toolinfoName_lineEdit, 0, 1, 1, 1)

        self.toolinfo_label_IP = QLabel(self.toolinfo_edit_group)
        self.toolinfo_label_IP.setObjectName(u"toolinfo_label_IP")
        self.toolinfo_label_IP.setFont(font1)
        self.toolinfo_label_IP.setLayoutDirection(Qt.LeftToRight)
        self.toolinfo_label_IP.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.toolinfo_label_IP.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.toolinfo_label_IP.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.toolinfo_label_IP, 1, 0, 1, 1)

        self.toolinfoIP_lineEdit = QLineEdit(self.toolinfo_edit_group)
        self.toolinfoIP_lineEdit.setObjectName(u"toolinfoIP_lineEdit")
        self.toolinfoIP_lineEdit.setReadOnly(True)

        self.grid_toolinfo_area1.addWidget(self.toolinfoIP_lineEdit, 1, 1, 1, 1)

        self.toolinfo_label_Frequency = QLabel(self.toolinfo_edit_group)
        self.toolinfo_label_Frequency.setObjectName(u"toolinfo_label_Frequency")
        self.toolinfo_label_Frequency.setFont(font1)
        self.toolinfo_label_Frequency.setLayoutDirection(Qt.LeftToRight)
        self.toolinfo_label_Frequency.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.toolinfo_label_Frequency.setAlignment(Qt.AlignCenter)
        self.toolinfo_label_Frequency.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.toolinfo_label_Frequency, 2, 0, 1, 1)

        self.toolinfoFrequency_lineEdit = QLineEdit(self.toolinfo_edit_group)
        self.toolinfoFrequency_lineEdit.setObjectName(u"toolinfoFrequency_lineEdit")
        self.toolinfoFrequency_lineEdit.setReadOnly(True)

        self.grid_toolinfo_area1.addWidget(self.toolinfoFrequency_lineEdit, 2, 1, 1, 1)


        self.toolinfo_area1.addWidget(self.toolinfo_edit_group)

        self.Sys_check_btn_group = QWidget(self.Toolinfo_group)
        self.Sys_check_btn_group.setObjectName(u"Sys_check_btn_group")
        sizePolicy2 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy2.setHorizontalStretch(3)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.Sys_check_btn_group.sizePolicy().hasHeightForWidth())
        self.Sys_check_btn_group.setSizePolicy(sizePolicy2)
        self.horizontalLayout_7 = QHBoxLayout(self.Sys_check_btn_group)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.horizontalSpacer_7 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_7.addItem(self.horizontalSpacer_7)

        self.Tool_system_check = QPushButton(self.Sys_check_btn_group)
        self.Tool_system_check.setObjectName(u"Tool_system_check")
        sizePolicy3 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        sizePolicy3.setHorizontalStretch(10)
        sizePolicy3.setVerticalStretch(0)
        sizePolicy3.setHeightForWidth(self.Tool_system_check.sizePolicy().hasHeightForWidth())
        self.Tool_system_check.setSizePolicy(sizePolicy3)
        self.Tool_system_check.setFont(font1)
        self.Tool_system_check.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: 2px solid #7AFEC6;\n"
"    color: rgb(255, 255, 255);\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")
        self.Tool_system_check.setIconSize(QSize(20, 20))
        self.Tool_system_check.setCheckable(True)

        self.horizontalLayout_7.addWidget(self.Tool_system_check)


        self.toolinfo_area1.addWidget(self.Sys_check_btn_group)

        self.toolinfo_area1.setStretch(0, 5)

        self.verticalLayout_3.addWidget(self.Toolinfo_group)

        self.toolinfo_title_tare = QWidget(ToolInfo_Window)
        self.toolinfo_title_tare.setObjectName(u"toolinfo_title_tare")
        self.toolinfo_title_tare.setStyleSheet(u"QWidget#toolinfo_title_tare{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout_2 = QHBoxLayout(self.toolinfo_title_tare)
        self.horizontalLayout_2.setSpacing(3)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalLayout_2.setContentsMargins(2, 2, 2, 2)
        self.toolinfo_label_tare = QLabel(self.toolinfo_title_tare)
        self.toolinfo_label_tare.setObjectName(u"toolinfo_label_tare")
        self.toolinfo_label_tare.setFont(font)
        self.toolinfo_label_tare.setLayoutDirection(Qt.LeftToRight)
        self.toolinfo_label_tare.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.toolinfo_label_tare.setAlignment(Qt.AlignCenter)
        self.toolinfo_label_tare.setMargin(2)

        self.horizontalLayout_2.addWidget(self.toolinfo_label_tare)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_2)


        self.verticalLayout_3.addWidget(self.toolinfo_title_tare)

        self.tare_lable = QLabel(ToolInfo_Window)
        self.tare_lable.setObjectName(u"tare_lable")
        self.tare_lable.setFont(font1)
        self.tare_lable.setLayoutDirection(Qt.LeftToRight)
        self.tare_lable.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.tare_lable.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.tare_lable.setMargin(2)

        self.verticalLayout_3.addWidget(self.tare_lable)

        self.Tareinfo_group = QWidget(ToolInfo_Window)
        self.Tareinfo_group.setObjectName(u"Tareinfo_group")
        self.horizontalLayout_5 = QHBoxLayout(self.Tareinfo_group)
        self.horizontalLayout_5.setSpacing(5)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.horizontalLayout_5.setContentsMargins(0, 0, 5, 0)
        self.Tareinfo_edit_group = QWidget(self.Tareinfo_group)
        self.Tareinfo_edit_group.setObjectName(u"Tareinfo_edit_group")
        self.grid_toolinfo_area1_2 = QGridLayout(self.Tareinfo_edit_group)
        self.grid_toolinfo_area1_2.setObjectName(u"grid_toolinfo_area1_2")
        self.grid_toolinfo_area1_2.setHorizontalSpacing(8)
        self.grid_toolinfo_area1_2.setVerticalSpacing(5)
        self.grid_toolinfo_area1_2.setContentsMargins(8, -1, 5, -1)
        self.Tareinfo_Fx_lineedit = QLineEdit(self.Tareinfo_edit_group)
        self.Tareinfo_Fx_lineedit.setObjectName(u"Tareinfo_Fx_lineedit")
        self.Tareinfo_Fx_lineedit.setStyleSheet(u"")
        self.Tareinfo_Fx_lineedit.setReadOnly(False)

        self.grid_toolinfo_area1_2.addWidget(self.Tareinfo_Fx_lineedit, 0, 1, 1, 1)

        self.Tareinfo_Fz_label = QLabel(self.Tareinfo_edit_group)
        self.Tareinfo_Fz_label.setObjectName(u"Tareinfo_Fz_label")
        self.Tareinfo_Fz_label.setFont(font1)
        self.Tareinfo_Fz_label.setLayoutDirection(Qt.LeftToRight)
        self.Tareinfo_Fz_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.Tareinfo_Fz_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Tareinfo_Fz_label.setMargin(2)

        self.grid_toolinfo_area1_2.addWidget(self.Tareinfo_Fz_label, 0, 2, 1, 1)

        self.Tareinfo_T_label = QLabel(self.Tareinfo_edit_group)
        self.Tareinfo_T_label.setObjectName(u"Tareinfo_T_label")
        self.Tareinfo_T_label.setFont(font1)
        self.Tareinfo_T_label.setLayoutDirection(Qt.LeftToRight)
        self.Tareinfo_T_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.Tareinfo_T_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Tareinfo_T_label.setMargin(2)

        self.grid_toolinfo_area1_2.addWidget(self.Tareinfo_T_label, 1, 2, 1, 1)

        self.Tareinfo_Fy_lineedit = QLineEdit(self.Tareinfo_edit_group)
        self.Tareinfo_Fy_lineedit.setObjectName(u"Tareinfo_Fy_lineedit")
        self.Tareinfo_Fy_lineedit.setReadOnly(False)

        self.grid_toolinfo_area1_2.addWidget(self.Tareinfo_Fy_lineedit, 1, 1, 1, 1)

        self.Tareinfo_T_lineedit = QLineEdit(self.Tareinfo_edit_group)
        self.Tareinfo_T_lineedit.setObjectName(u"Tareinfo_T_lineedit")
        self.Tareinfo_T_lineedit.setReadOnly(False)

        self.grid_toolinfo_area1_2.addWidget(self.Tareinfo_T_lineedit, 1, 3, 1, 1)

        self.Tareinfo_Fz_lineedit = QLineEdit(self.Tareinfo_edit_group)
        self.Tareinfo_Fz_lineedit.setObjectName(u"Tareinfo_Fz_lineedit")
        self.Tareinfo_Fz_lineedit.setReadOnly(False)

        self.grid_toolinfo_area1_2.addWidget(self.Tareinfo_Fz_lineedit, 0, 3, 1, 1)

        self.Tareinfo_Fx_label = QLabel(self.Tareinfo_edit_group)
        self.Tareinfo_Fx_label.setObjectName(u"Tareinfo_Fx_label")
        self.Tareinfo_Fx_label.setFont(font1)
        self.Tareinfo_Fx_label.setLayoutDirection(Qt.LeftToRight)
        self.Tareinfo_Fx_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.Tareinfo_Fx_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Tareinfo_Fx_label.setMargin(2)

        self.grid_toolinfo_area1_2.addWidget(self.Tareinfo_Fx_label, 0, 0, 1, 1)

        self.Tareinfo_Fy_label = QLabel(self.Tareinfo_edit_group)
        self.Tareinfo_Fy_label.setObjectName(u"Tareinfo_Fy_label")
        self.Tareinfo_Fy_label.setFont(font1)
        self.Tareinfo_Fy_label.setLayoutDirection(Qt.LeftToRight)
        self.Tareinfo_Fy_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.Tareinfo_Fy_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Tareinfo_Fy_label.setMargin(2)

        self.grid_toolinfo_area1_2.addWidget(self.Tareinfo_Fy_label, 1, 0, 1, 1)


        self.horizontalLayout_5.addWidget(self.Tareinfo_edit_group)

        self.Tare_btn_group = QWidget(self.Tareinfo_group)
        self.Tare_btn_group.setObjectName(u"Tare_btn_group")
        sizePolicy4 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy4.setHorizontalStretch(4)
        sizePolicy4.setVerticalStretch(0)
        sizePolicy4.setHeightForWidth(self.Tare_btn_group.sizePolicy().hasHeightForWidth())
        self.Tare_btn_group.setSizePolicy(sizePolicy4)
        self.horizontalLayout_8 = QHBoxLayout(self.Tare_btn_group)
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.horizontalSpacer = QSpacerItem(20, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_8.addItem(self.horizontalSpacer)

        self.Tool_tare_btn = QPushButton(self.Tare_btn_group)
        self.Tool_tare_btn.setObjectName(u"Tool_tare_btn")
        sizePolicy5 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        sizePolicy5.setHorizontalStretch(6)
        sizePolicy5.setVerticalStretch(0)
        sizePolicy5.setHeightForWidth(self.Tool_tare_btn.sizePolicy().hasHeightForWidth())
        self.Tool_tare_btn.setSizePolicy(sizePolicy5)
        self.Tool_tare_btn.setMinimumSize(QSize(10, 10))
        self.Tool_tare_btn.setFont(font1)
        self.Tool_tare_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: 2px solid #7AFEC6;\n"
"    color: rgb(255, 255, 255);\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")
        self.Tool_tare_btn.setIconSize(QSize(20, 20))
        self.Tool_tare_btn.setCheckable(True)

        self.horizontalLayout_8.addWidget(self.Tool_tare_btn)


        self.horizontalLayout_5.addWidget(self.Tare_btn_group)

        self.horizontalLayout_5.setStretch(0, 8)

        self.verticalLayout_3.addWidget(self.Tareinfo_group)

        self.toolinfo_title_conversion = QWidget(ToolInfo_Window)
        self.toolinfo_title_conversion.setObjectName(u"toolinfo_title_conversion")
        self.toolinfo_title_conversion.setStyleSheet(u"QWidget#toolinfo_title_conversion{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout_3 = QHBoxLayout(self.toolinfo_title_conversion)
        self.horizontalLayout_3.setSpacing(3)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_3.setContentsMargins(2, 2, 2, 2)
        self.toolinfo_label_conversion = QLabel(self.toolinfo_title_conversion)
        self.toolinfo_label_conversion.setObjectName(u"toolinfo_label_conversion")
        self.toolinfo_label_conversion.setFont(font)
        self.toolinfo_label_conversion.setLayoutDirection(Qt.LeftToRight)
        self.toolinfo_label_conversion.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.toolinfo_label_conversion.setAlignment(Qt.AlignCenter)
        self.toolinfo_label_conversion.setMargin(2)

        self.horizontalLayout_3.addWidget(self.toolinfo_label_conversion)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_3)


        self.verticalLayout_3.addWidget(self.toolinfo_title_conversion)

        self.conversion_lable = QLabel(ToolInfo_Window)
        self.conversion_lable.setObjectName(u"conversion_lable")
        self.conversion_lable.setFont(font1)
        self.conversion_lable.setLayoutDirection(Qt.LeftToRight)
        self.conversion_lable.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.conversion_lable.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.conversion_lable.setMargin(2)

        self.verticalLayout_3.addWidget(self.conversion_lable)

        self.conversion_group = QWidget(ToolInfo_Window)
        self.conversion_group.setObjectName(u"conversion_group")
        self.grid_toolinfo_area1_3 = QGridLayout(self.conversion_group)
        self.grid_toolinfo_area1_3.setObjectName(u"grid_toolinfo_area1_3")
        self.grid_toolinfo_area1_3.setHorizontalSpacing(8)
        self.grid_toolinfo_area1_3.setVerticalSpacing(5)
        self.grid_toolinfo_area1_3.setContentsMargins(8, -1, 5, -1)
        self.conversion_Fy_label = QLabel(self.conversion_group)
        self.conversion_Fy_label.setObjectName(u"conversion_Fy_label")
        self.conversion_Fy_label.setFont(font1)
        self.conversion_Fy_label.setLayoutDirection(Qt.LeftToRight)
        self.conversion_Fy_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.conversion_Fy_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.conversion_Fy_label.setMargin(2)

        self.grid_toolinfo_area1_3.addWidget(self.conversion_Fy_label, 1, 0, 1, 1)

        self.conversion_Fx_label = QLabel(self.conversion_group)
        self.conversion_Fx_label.setObjectName(u"conversion_Fx_label")
        self.conversion_Fx_label.setFont(font1)
        self.conversion_Fx_label.setLayoutDirection(Qt.LeftToRight)
        self.conversion_Fx_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.conversion_Fx_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.conversion_Fx_label.setMargin(2)

        self.grid_toolinfo_area1_3.addWidget(self.conversion_Fx_label, 0, 0, 1, 1)

        self.conversion_Fx_lineEdit = QLineEdit(self.conversion_group)
        self.conversion_Fx_lineEdit.setObjectName(u"conversion_Fx_lineEdit")
        self.conversion_Fx_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1_3.addWidget(self.conversion_Fx_lineEdit, 0, 1, 1, 1)

        self.conversion_T_label = QLabel(self.conversion_group)
        self.conversion_T_label.setObjectName(u"conversion_T_label")
        self.conversion_T_label.setFont(font1)
        self.conversion_T_label.setLayoutDirection(Qt.LeftToRight)
        self.conversion_T_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.conversion_T_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.conversion_T_label.setMargin(2)

        self.grid_toolinfo_area1_3.addWidget(self.conversion_T_label, 1, 2, 1, 1)

        self.conversion_Fz_label = QLabel(self.conversion_group)
        self.conversion_Fz_label.setObjectName(u"conversion_Fz_label")
        self.conversion_Fz_label.setFont(font1)
        self.conversion_Fz_label.setLayoutDirection(Qt.LeftToRight)
        self.conversion_Fz_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.conversion_Fz_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.conversion_Fz_label.setMargin(2)

        self.grid_toolinfo_area1_3.addWidget(self.conversion_Fz_label, 0, 2, 1, 1)

        self.conversion_Fy_lineEdit = QLineEdit(self.conversion_group)
        self.conversion_Fy_lineEdit.setObjectName(u"conversion_Fy_lineEdit")
        self.conversion_Fy_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1_3.addWidget(self.conversion_Fy_lineEdit, 1, 1, 1, 1)

        self.conversion_Fz_lineEdit = QLineEdit(self.conversion_group)
        self.conversion_Fz_lineEdit.setObjectName(u"conversion_Fz_lineEdit")
        self.conversion_Fz_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1_3.addWidget(self.conversion_Fz_lineEdit, 0, 3, 1, 1)

        self.conversion_T_lineEdit = QLineEdit(self.conversion_group)
        self.conversion_T_lineEdit.setObjectName(u"conversion_T_lineEdit")
        self.conversion_T_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1_3.addWidget(self.conversion_T_lineEdit, 1, 3, 1, 1)

        self.conversion_TOL_group = QHBoxLayout()
        self.conversion_TOL_group.setSpacing(10)
        self.conversion_TOL_group.setObjectName(u"conversion_TOL_group")
        self.conversion_TOL_group.setContentsMargins(0, -1, -1, -1)
        self.conversion_TOL_label = QLabel(self.conversion_group)
        self.conversion_TOL_label.setObjectName(u"conversion_TOL_label")
        sizePolicy6 = QSizePolicy(QSizePolicy.Maximum, QSizePolicy.Preferred)
        sizePolicy6.setHorizontalStretch(0)
        sizePolicy6.setVerticalStretch(0)
        sizePolicy6.setHeightForWidth(self.conversion_TOL_label.sizePolicy().hasHeightForWidth())
        self.conversion_TOL_label.setSizePolicy(sizePolicy6)
        self.conversion_TOL_label.setFont(font1)
        self.conversion_TOL_label.setLayoutDirection(Qt.LeftToRight)
        self.conversion_TOL_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.conversion_TOL_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.conversion_TOL_label.setMargin(2)

        self.conversion_TOL_group.addWidget(self.conversion_TOL_label)

        self.conversion_TOL_lineEdit = QLineEdit(self.conversion_group)
        self.conversion_TOL_lineEdit.setObjectName(u"conversion_TOL_lineEdit")
        sizePolicy7 = QSizePolicy(QSizePolicy.Maximum, QSizePolicy.Fixed)
        sizePolicy7.setHorizontalStretch(0)
        sizePolicy7.setVerticalStretch(0)
        sizePolicy7.setHeightForWidth(self.conversion_TOL_lineEdit.sizePolicy().hasHeightForWidth())
        self.conversion_TOL_lineEdit.setSizePolicy(sizePolicy7)
        self.conversion_TOL_lineEdit.setStyleSheet(u"")

        self.conversion_TOL_group.addWidget(self.conversion_TOL_lineEdit)

        self.conversion_TOL_unit_label = QLabel(self.conversion_group)
        self.conversion_TOL_unit_label.setObjectName(u"conversion_TOL_unit_label")
        self.conversion_TOL_unit_label.setFont(font1)
        self.conversion_TOL_unit_label.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.conversion_TOL_group.addWidget(self.conversion_TOL_unit_label)


        self.grid_toolinfo_area1_3.addLayout(self.conversion_TOL_group, 2, 0, 1, 4)


        self.verticalLayout_3.addWidget(self.conversion_group)

        self.toolinfo_title_Autorecord = QWidget(ToolInfo_Window)
        self.toolinfo_title_Autorecord.setObjectName(u"toolinfo_title_Autorecord")
        self.toolinfo_title_Autorecord.setStyleSheet(u"QWidget#toolinfo_title_Autorecord{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout_4 = QHBoxLayout(self.toolinfo_title_Autorecord)
        self.horizontalLayout_4.setSpacing(3)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout_4.setContentsMargins(2, 2, 2, 2)
        self.toolinfo_label_Autorecord = QLabel(self.toolinfo_title_Autorecord)
        self.toolinfo_label_Autorecord.setObjectName(u"toolinfo_label_Autorecord")
        self.toolinfo_label_Autorecord.setFont(font)
        self.toolinfo_label_Autorecord.setLayoutDirection(Qt.LeftToRight)
        self.toolinfo_label_Autorecord.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.toolinfo_label_Autorecord.setAlignment(Qt.AlignCenter)
        self.toolinfo_label_Autorecord.setMargin(2)

        self.horizontalLayout_4.addWidget(self.toolinfo_label_Autorecord)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_4.addItem(self.horizontalSpacer_4)


        self.verticalLayout_3.addWidget(self.toolinfo_title_Autorecord)

        self.AutoRecord_check_group = QWidget(ToolInfo_Window)
        self.AutoRecord_check_group.setObjectName(u"AutoRecord_check_group")
        self.verticalLayout_2 = QVBoxLayout(self.AutoRecord_check_group)
        self.verticalLayout_2.setSpacing(5)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.AutoRecord_check = QWidget(self.AutoRecord_check_group)
        self.AutoRecord_check.setObjectName(u"AutoRecord_check")
        self.horizontalLayout_6 = QHBoxLayout(self.AutoRecord_check)
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.Recsetting_btn = QPushButton(self.AutoRecord_check)
        self.Recsetting_btn.setObjectName(u"Recsetting_btn")
        sizePolicy8 = QSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        sizePolicy8.setHorizontalStretch(0)
        sizePolicy8.setVerticalStretch(0)
        sizePolicy8.setHeightForWidth(self.Recsetting_btn.sizePolicy().hasHeightForWidth())
        self.Recsetting_btn.setSizePolicy(sizePolicy8)
        self.Recsetting_btn.setFont(font1)
        self.Recsetting_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: 2px solid #7AFEC6;\n"
"    color: rgb(255, 255, 255);\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")

        self.horizontalLayout_6.addWidget(self.Recsetting_btn)

        self.horizontalSpacer_6 = QSpacerItem(201, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_6.addItem(self.horizontalSpacer_6)


        self.verticalLayout_2.addWidget(self.AutoRecord_check)


        self.verticalLayout_3.addWidget(self.AutoRecord_check_group)

        self.Toolinfo_save_close = QWidget(ToolInfo_Window)
        self.Toolinfo_save_close.setObjectName(u"Toolinfo_save_close")
        self.horizontalLayout_9 = QHBoxLayout(self.Toolinfo_save_close)
        self.horizontalLayout_9.setSpacing(0)
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.horizontalLayout_9.setContentsMargins(5, 5, 8, 8)
        self.horizontalSpacer_10 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_10)

        self.Toolinfo_save_btn = QPushButton(self.Toolinfo_save_close)
        self.Toolinfo_save_btn.setObjectName(u"Toolinfo_save_btn")
        self.Toolinfo_save_btn.setMinimumSize(QSize(10, 10))
        self.Toolinfo_save_btn.setFont(font1)
        self.Toolinfo_save_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color:#5448B6;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7AFEC6;\n"
"     color: rgb(255, 255, 255);\n"
"    border:none\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border:none\n"
"}")
        self.Toolinfo_save_btn.setIconSize(QSize(20, 20))
        self.Toolinfo_save_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.Toolinfo_save_btn)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_5)

        self.Toolinfo_close_btn = QPushButton(self.Toolinfo_save_close)
        self.Toolinfo_close_btn.setObjectName(u"Toolinfo_close_btn")
        self.Toolinfo_close_btn.setMinimumSize(QSize(10, 10))
        self.Toolinfo_close_btn.setFont(font1)
        self.Toolinfo_close_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: 2px solid #7AFEC6;\n"
"    color: rgb(255, 255, 255);\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")
        self.Toolinfo_close_btn.setIconSize(QSize(20, 20))
        self.Toolinfo_close_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.Toolinfo_close_btn)

        self.horizontalLayout_9.setStretch(0, 8)
        self.horizontalLayout_9.setStretch(1, 3)
        self.horizontalLayout_9.setStretch(2, 1)
        self.horizontalLayout_9.setStretch(3, 3)

        self.verticalLayout_3.addWidget(self.Toolinfo_save_close)


        self.retranslateUi(ToolInfo_Window)

        QMetaObject.connectSlotsByName(ToolInfo_Window)
    # setupUi

    def retranslateUi(self, ToolInfo_Window):
        ToolInfo_Window.setWindowTitle(QCoreApplication.translate("ToolInfo_Window", u"Form", None))
        self.toolinfo_title_label.setText(QCoreApplication.translate("ToolInfo_Window", u"\u5200\u628a\u8cc7\u8a0a", None))
        self.toolinfo_label_name.setText(QCoreApplication.translate("ToolInfo_Window", u"\u540d\u7a31 :", None))
        self.toolinfo_label_IP.setText(QCoreApplication.translate("ToolInfo_Window", u"IP :", None))
        self.toolinfo_label_Frequency.setText(QCoreApplication.translate("ToolInfo_Window", u"\u983b\u7387 :", None))
        self.Tool_system_check.setText(QCoreApplication.translate("ToolInfo_Window", u"\u5200\u628a\n"
"\u7cfb\u7d71\u6aa2\u67e5", None))
        self.toolinfo_label_tare.setText(QCoreApplication.translate("ToolInfo_Window", u"\u6821\u6b63\u4fc2\u6578", None))
        self.tare_lable.setText(QCoreApplication.translate("ToolInfo_Window", u"\u8f38\u5165\u6821\u6b63\u503c\u9032\u884c\u6e2c\u91cf\u6821\u6b63\u3002", None))
        self.Tareinfo_Fz_label.setText(QCoreApplication.translate("ToolInfo_Window", u"Fz :", None))
        self.Tareinfo_T_label.setText(QCoreApplication.translate("ToolInfo_Window", u"T :", None))
        self.Tareinfo_Fy_lineedit.setText("")
        self.Tareinfo_Fx_label.setText(QCoreApplication.translate("ToolInfo_Window", u"Fx :", None))
        self.Tareinfo_Fy_label.setText(QCoreApplication.translate("ToolInfo_Window", u"Fy :", None))
        self.Tool_tare_btn.setText(QCoreApplication.translate("ToolInfo_Window", u"\u6821\u6b63", None))
        self.toolinfo_label_conversion.setText(QCoreApplication.translate("ToolInfo_Window", u"\u63db\u7b97\u4fc2\u6578", None))
        self.conversion_lable.setText(QCoreApplication.translate("ToolInfo_Window", u"\u4f9d\u64da\u5200\u628a\u593e\u6301\u7684\u5200\u5177\u4f38\u51fa\u9577(TOL\uff0c\u55ae\u4f4d\uff1amm)\u3001\u63db\u7b97\u4fc2\u6578\uff0c\u7522\u751f\u7684\u529b\n"
"\u91cf\u8aa4\u5dee\u88dc\u511f\u3002", None))
        self.conversion_Fy_label.setText(QCoreApplication.translate("ToolInfo_Window", u"Fy :", None))
        self.conversion_Fx_label.setText(QCoreApplication.translate("ToolInfo_Window", u"Fx :", None))
        self.conversion_T_label.setText(QCoreApplication.translate("ToolInfo_Window", u"T :", None))
        self.conversion_Fz_label.setText(QCoreApplication.translate("ToolInfo_Window", u"Fz :", None))
        self.conversion_TOL_label.setText(QCoreApplication.translate("ToolInfo_Window", u"\u5200\u5177\u4f38\u51fa\u9577\u5ea6:", None))
        self.conversion_TOL_unit_label.setText(QCoreApplication.translate("ToolInfo_Window", u"(mm)", None))
        self.toolinfo_label_Autorecord.setText(QCoreApplication.translate("ToolInfo_Window", u"\u9304\u88fd\u7a0b\u5e8f", None))
        self.Recsetting_btn.setText(QCoreApplication.translate("ToolInfo_Window", u"\u8a2d\u5b9a\u9304\u88fd\u7a0b\u5e8f...", None))
        self.Toolinfo_save_btn.setText(QCoreApplication.translate("ToolInfo_Window", u"\u5132\u5b58", None))
        self.Toolinfo_close_btn.setText(QCoreApplication.translate("ToolInfo_Window", u"\u95dc\u9589", None))
    # retranslateUi

