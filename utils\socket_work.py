from PySide2.QtCore import QThread, Signal
import socket
import time
import math
import threading
from typing import Dict, List
import logging

# 初始化 logger
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

class SocketWorker(QThread):

    # raw_data_received = Signal(str)  # 當收到數據時發出 Signal
    raw_data_received = Signal(str)  # 發送原始數據信號
    sig_socket_connect = Signal(object)  # 發送 Socket 信號
    sig_client_connected = Signal(str)  # 客戶端連接信號
    sig_client_disconnected = Signal(str)  # 客戶端斷開信號

    def __init__(self, host='0.0.0.0', port=1333, sampleRate=10000, max_clients=5):

        super().__init__()
        self.host = host  # 改為監聽所有接口
        self.port = port
        self.SampleRate = sampleRate
        self.SamplePoint1 = math.ceil(self.SampleRate / 12.5)
        self.sample_N = math.ceil(self.SamplePoint1 * 1.005)
        self.sample_byte = int(self.sample_N * 16)
        self.max_clients = max_clients

        self.running = False
        self.paused = False
        self.server_socket = None
        self.client_sockets: Dict[str, socket.socket] = {}  # 存儲客戶端連接
        self.client_threads: Dict[str, threading.Thread] = {}  # 存儲客戶端處理線程
        self.active_client = None  # 當前活躍的客戶端

        self.collect_data = ""
        self.temp_data = ""

    def run(self):
        """Server 主運行循環"""
        self.running = True
        try:
            self._start_server()

            while self.running:
                if self.paused:
                    time.sleep(0.2)
                    continue

                try:
                    # 接受新的客戶端連接
                    client_socket, client_address = self.server_socket.accept()
                    client_id = f"{client_address[0]}:{client_address[1]}"

                    logger.info(f"新客戶端連接: {client_id}")

                    # 檢查是否超過最大客戶端數量
                    if len(self.client_sockets) >= self.max_clients:
                        logger.warning(f"達到最大客戶端數量 ({self.max_clients})，拒絕連接 {client_id}")
                        client_socket.close()
                        continue

                    # 優化客戶端 socket
                    client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
                    client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                    client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 131072)
                    client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 131072)

                    # 存儲客戶端連接
                    self.client_sockets[client_id] = client_socket

                    # 如果是第一個客戶端，設為活躍客戶端
                    if self.active_client is None:
                        self.active_client = client_id
                        logger.info(f"設置活躍客戶端: {client_id}")

                    # 創建客戶端處理線程
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket, client_id),
                        daemon=True
                    )
                    self.client_threads[client_id] = client_thread
                    client_thread.start()

                    # 發送客戶端連接信號
                    self.sig_client_connected.emit(client_id)

                except socket.timeout:
                    continue
                except socket.error as e:
                    if self.running:
                        logger.error(f"Server socket 錯誤: {e}")
                    break
                except Exception as e:
                    logger.error(f"SocketWorker 運行時發生異常: {e}")
                    break

        except Exception as e:
            logger.error(f"Server 啟動失敗: {e}")
        finally:
            self._cleanup_server()

        data = {"status": False, "host": self.host}
        self.sig_socket_connect.emit(data)
        self.exec_()

    def pause(self):
        """暫停數據處理"""
        self.paused = True
        # 清理暫存資料
        self.temp_data = ""
        self.collect_data = ""
        logger.info("Server 已暫停")

    def resume(self):
        """恢復數據處理"""
        logger.info("恢復數據處理...")
        # 清理暫存資料
        self.temp_data = ""
        self.collect_data = ""
        # 設定為非暫停狀態
        self.paused = False
        logger.info("Server 已恢復")

    def set_active_client(self, client_id: str):
        """設置活躍客戶端"""
        if client_id in self.client_sockets:
            self.active_client = client_id
            logger.info(f"切換活躍客戶端到: {client_id}")
        else:
            logger.warning(f"客戶端 {client_id} 不存在")

    def get_connected_clients(self) -> List[str]:
        """獲取已連接的客戶端列表"""
        return list(self.client_sockets.keys())

    def disconnect_client(self, client_id: str):
        """斷開指定客戶端"""
        if client_id in self.client_sockets:
            try:
                self.client_sockets[client_id].close()
            except Exception as e:
                logger.error(f"關閉客戶端 {client_id} 時發生錯誤: {e}")

            # 清理客戶端記錄
            self._remove_client(client_id)
            logger.info(f"已斷開客戶端: {client_id}")

    def stop(self):
        """停止 Server"""
        logger.info("正在停止 Server...")
        self.running = False
        self._cleanup_server()
        self.quit()
        if not self.wait(1000):
            self.terminate()
            self.wait()
        logger.info("Server 已停止")

    def _cleanup_server(self):
        """清理 Server 資源"""
        # 關閉所有客戶端連接
        for client_id in list(self.client_sockets.keys()):
            self.disconnect_client(client_id)

        # 關閉 server socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except Exception as e:
                logger.error(f"關閉 server socket 時發生錯誤: {e}")
            finally:
                self.server_socket = None

        # 清理客戶端記錄
        self.client_sockets.clear()
        self.client_threads.clear()
        self.active_client = None

    def emit_data(self, hex_data):
        """發送數據信號"""
        if hex_data:
            self.raw_data_received.emit(hex_data)

    def _start_server(self):
        """啟動 Server"""
        self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

        # 設置 socket 選項
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 131072)
        self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 131072)

        # 設置為非阻塞模式
        self.server_socket.settimeout(1.0)

        # 綁定並監聽
        self.server_socket.bind((self.host, self.port))
        self.server_socket.listen(self.max_clients)

        logger.info(f"Server 已啟動，監聽 {self.host}:{self.port}，最大客戶端數: {self.max_clients}")

        # 發送連接狀態信號
        data = {"status": True, "host": self.host}
        self.sig_socket_connect.emit(data)

    def _handle_client(self, client_socket: socket.socket, client_id: str):
        """處理單個客戶端的數據接收"""
        logger.info(f"開始處理客戶端 {client_id}")
        temp_data = ""

        try:
            while self.running and client_id in self.client_sockets:
                if self.paused:
                    time.sleep(0.1)
                    continue

                # 只處理活躍客戶端的數據
                if client_id != self.active_client:
                    time.sleep(0.1)
                    continue

                try:
                    # 接收數據
                    data = client_socket.recv(self.sample_byte)
                    if not data:
                        logger.info(f"客戶端 {client_id} 斷開連接")
                        break

                    hex_data = data.hex()
                    if hex_data:
                        # 處理數據包
                        processed_data = self._process_client_data(hex_data, temp_data)
                        if processed_data:
                            self.emit_data(processed_data)

                except socket.timeout:
                    continue
                except socket.error as e:
                    logger.error(f"客戶端 {client_id} 連接錯誤: {e}")
                    break
                except Exception as e:
                    logger.error(f"處理客戶端 {client_id} 數據時發生錯誤: {e}")
                    break

        except Exception as e:
            logger.error(f"客戶端 {client_id} 處理線程異常: {e}")
        finally:
            self._remove_client(client_id)

    def _process_client_data(self, hex_data: str, temp_data: str) -> str:
        """處理客戶端數據"""
        # 這裡可以實現與原來 receive_chunk 類似的邏輯
        # 簡化版本，直接返回接收到的數據
        return hex_data

    def _remove_client(self, client_id: str):
        """移除客戶端記錄"""
        if client_id in self.client_sockets:
            del self.client_sockets[client_id]

        if client_id in self.client_threads:
            del self.client_threads[client_id]

        # 如果移除的是活躍客戶端，選擇新的活躍客戶端
        if self.active_client == client_id:
            if self.client_sockets:
                self.active_client = next(iter(self.client_sockets.keys()))
                logger.info(f"切換活躍客戶端到: {self.active_client}")
            else:
                self.active_client = None
                logger.info("沒有活躍客戶端")

        # 發送客戶端斷開信號
        self.sig_client_disconnected.emit(client_id)

    def send_sleepmode(self, enable: bool):
        """向活躍客戶端發送睡眠模式命令"""
        if not self.active_client or self.active_client not in self.client_sockets:
            logger.warning("沒有活躍客戶端，無法發送睡眠模式命令")
            return False

        try:
            client_socket = self.client_sockets[self.active_client]
            # 這裡需要根據實際的協議來實現睡眠模式命令
            # 暫時使用簡單的字符串命令
            command = "SLEEP_ON" if enable else "SLEEP_OFF"
            client_socket.send(command.encode())
            logger.info(f"向客戶端 {self.active_client} 發送睡眠模式命令: {command}")
            return True
        except Exception as e:
            logger.error(f"發送睡眠模式命令失敗: {e}")
            return False

    def send_command_to_client(self, client_id: str, command: str):
        """向指定客戶端發送命令"""
        if client_id not in self.client_sockets:
            logger.warning(f"客戶端 {client_id} 不存在")
            return False

        try:
            client_socket = self.client_sockets[client_id]
            client_socket.send(command.encode())
            logger.info(f"向客戶端 {client_id} 發送命令: {command}")
            return True
        except Exception as e:
            logger.error(f"向客戶端 {client_id} 發送命令失敗: {e}")
            return False

    def broadcast_command(self, command: str):
        """向所有客戶端廣播命令"""
        success_count = 0
        for client_id in list(self.client_sockets.keys()):
            if self.send_command_to_client(client_id, command):
                success_count += 1

        logger.info(f"廣播命令 '{command}' 成功發送到 {success_count}/{len(self.client_sockets)} 個客戶端")
        return success_count
