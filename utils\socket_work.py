from PySide2.QtCore import QThread, Signal
import socket
import time
import math
from utils.cirular_queue import CircularQueue
from . import logger  # 從同一個包導入 logger

class SocketWorker(QThread):

    # raw_data_received = Signal(str)  # 當收到數據時發出 Signal
    raw_data_received = Signal(str)  # 發送原始數據信號
    sig_socket_connect = Signal(object)  # 發送 Socket 信號
     
    def __init__(self, host='*************', port=1333,sampleRate=10000):

        super().__init__()
        self.host = host
        self.port = port
        self.SampleRate = sampleRate
        self.SamplePoint1 = math.ceil(self.SampleRate / 12.5)
        self.sample_N = math.ceil(self.SamplePoint1 * 1.005)
        self.sample_byte = int(self.sample_N * 16)

        self.running = False
        self.paused = False
        self.sock = None

        self.collect_data = ""
        self.temp_data = ""

    def run(self):
        self.running = True
        while self.running:
            try:
                self._connect_socket()
                
                while self.running:
                    if self.paused:
                        # ✅ 暫停時把接收緩衝區清空（讀掉但丟棄）
                        self._drain_socket()
                        # 也清掉我們自己暫存的切片
                        self.temp_data = ""
                        self.collect_data = ""
                        time.sleep(0.2)
                        continue

                    if self.sock is None:
                        break

                    try:
                        hex_data = self.receive_chunk(self.sample_N, self.sample_byte)
                        if hex_data:
                            self.emit_data(hex_data)
                    except socket.timeout:
                        continue
                    except socket.error as e:
                        break

            except Exception as e:
                logger.error(f"SocketWorker 運行時發生異常: {e}")
            finally:
                self.close_socket()
                
            # 如果是因為 paused 結束而斷線，等待重連
            if self.running and not self.paused:
                logger.info("準備重新連線...")
                time.sleep(1)  # 短暫延遲後重連

        data = {"status": False, "host": self.host}
        self.sig_socket_connect.emit(data)
        self.exec_()

    def pause(self):
        self.paused = True
        # 先清掉尚未湊滿一組的殘留
        self.temp_data = ""
        # 可選：立刻把 OS 緩衝也清一次
        self._drain_socket()

    def resume(self):
        """恢復數據接收時觸發重連"""
        logger.info("恢復數據接收，準備重新連線...")
        # 先關閉現有連線
        self.close_socket()
        # 清理暫存資料
        self.temp_data = ""
        self.collect_data = ""
        # 設定為非暫停狀態，讓 run() 迴圈重新連線
        self.paused = False

    def _drain_socket(self):
        """非阻塞讀取並丟棄所有已到達的資料，直到目前無資料可讀"""
        if not self.sock:
            return
        try:
            # 暫時改成非阻塞
            self.sock.setblocking(False)
            while True:
                try:
                    # 單次讀大一些；讀到 0 代表對端關閉
                    chunk = self.sock.recv(65536)
                    if not chunk:
                        break
                    # 直接丟棄，不做任何處理
                except (BlockingIOError, InterruptedError):
                    # 沒資料可讀了
                    break
        finally:
            # 復原阻塞/timeout 設定
            self.sock.setblocking(True)
            self.sock.settimeout(3)

    def stop(self):
        self.running = False
        self.close_socket()
        self.quit()
        if not self.wait(1000):
            self.terminate()
            self.wait()

    def close_socket(self):
        if self.sock:
            try:
                self.sock.shutdown(socket.SHUT_RDWR)
            except Exception:
                pass
            finally:
                self.sock.close()
                self.sock = None

    def emit_data(self, hex_data):
        if hex_data:
            self.raw_data_received.emit(hex_data)

    def receive_chunk(self, sample_N, sample_byte):
        count_get = 0
        collect_data = ""
        self.temp_data = ""  # ← 如需保留殘留就不要在這裡清；視你的協議而定

        try:
            if not self.running or not self.sock:
                raise Exception("Client is not running or socket is invalid.")

            while count_get < sample_N:
                data_recv = self.sock.recv(sample_byte).hex()
                # logger.debug(f"收到資料：{data_recv}")
                if not data_recv:
                    raise Exception("Connection closed by the server.")
                collect_data += data_recv

                if self.temp_data:
                    data_recv = self.temp_data + data_recv

                N = len(data_recv) // 32
                count_get += N

                if len(data_recv) % 32 > 0:
                    self.temp_data = data_recv[N*32:]
                else:
                    self.temp_data = ""   # ✅ 修正：不要設成 []

                remaining_samples = sample_N - count_get
                sample_byte = max((remaining_samples * 32 - len(self.temp_data)) // 2, 1)

            return collect_data

        except socket.error as e:
            self.stop()
            raise

    def _connect_socket(self):
        """建立 socket 連線"""
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        self.sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
        self.sock.connect((self.host, self.port))
        self.sock.settimeout(3)
        logger.info(f"已連接到 {self.host}:{self.port}")
