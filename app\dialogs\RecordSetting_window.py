# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'RecordSetting_window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

import system_image.system_image_rc

class Ui_RecordSetting_window(object):
    def setupUi(self, RecordSetting_window):
        if not RecordSetting_window.objectName():
            RecordSetting_window.setObjectName(u"RecordSetting_window")
        RecordSetting_window.resize(900, 635)
        RecordSetting_window.setMinimumSize(QSize(900, 635))
        RecordSetting_window.setMaximumSize(QSize(900, 635))
        font = QFont()
        font.setFamily(u"Arial Black")
        font.setPointSize(10)
        RecordSetting_window.setFont(font)
        RecordSetting_window.setStyleSheet(u"QWidget#RecordSetting_window{background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;}")
        self.verticalLayout = QVBoxLayout(RecordSetting_window)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(9, 9, 9, 9)
        self.RecordSetting_body = QWidget(RecordSetting_window)
        self.RecordSetting_body.setObjectName(u"RecordSetting_body")
        self.horizontalLayout = QHBoxLayout(self.RecordSetting_body)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.RecordSetting_settings = QWidget(self.RecordSetting_body)
        self.RecordSetting_settings.setObjectName(u"RecordSetting_settings")
        sizePolicy = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Maximum)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.RecordSetting_settings.sizePolicy().hasHeightForWidth())
        self.RecordSetting_settings.setSizePolicy(sizePolicy)
        self.verticalLayout_2 = QVBoxLayout(self.RecordSetting_settings)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(12, 9, 9, 0)
        self.manual_title = QWidget(self.RecordSetting_settings)
        self.manual_title.setObjectName(u"manual_title")
        self.manual_title.setEnabled(False)
        sizePolicy1 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.manual_title.sizePolicy().hasHeightForWidth())
        self.manual_title.setSizePolicy(sizePolicy1)
        self.manual_title.setStyleSheet(u"QWidget#manual_title{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout_2 = QHBoxLayout(self.manual_title)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.manual_label = QLabel(self.manual_title)
        self.manual_label.setObjectName(u"manual_label")
        font1 = QFont()
        font1.setFamily(u"Arial Black")
        font1.setPointSize(12)
        self.manual_label.setFont(font1)
        self.manual_label.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.horizontalLayout_2.addWidget(self.manual_label)


        self.verticalLayout_2.addWidget(self.manual_title)

        self.manual_group = QWidget(self.RecordSetting_settings)
        self.manual_group.setObjectName(u"manual_group")
        sizePolicy1.setHeightForWidth(self.manual_group.sizePolicy().hasHeightForWidth())
        self.manual_group.setSizePolicy(sizePolicy1)
        self.horizontalLayout_6 = QHBoxLayout(self.manual_group)
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.record_img = QWidget(self.manual_group)
        self.record_img.setObjectName(u"record_img")
        sizePolicy2 = QSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.record_img.sizePolicy().hasHeightForWidth())
        self.record_img.setSizePolicy(sizePolicy2)
        self.record_img.setMinimumSize(QSize(16, 16))
        self.record_img.setStyleSheet(u"QWidget#record_img{\n"
"image: url(:/btn_record/btn_record_s0_1.png);\n"
"}")

        self.horizontalLayout_6.addWidget(self.record_img)

        self.manual_description = QLabel(self.manual_group)
        self.manual_description.setObjectName(u"manual_description")
        self.manual_description.setFont(font)
        self.manual_description.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.horizontalLayout_6.addWidget(self.manual_description)


        self.verticalLayout_2.addWidget(self.manual_group)

        self.auto_title = QWidget(self.RecordSetting_settings)
        self.auto_title.setObjectName(u"auto_title")
        sizePolicy1.setHeightForWidth(self.auto_title.sizePolicy().hasHeightForWidth())
        self.auto_title.setSizePolicy(sizePolicy1)
        self.auto_title.setStyleSheet(u"QWidget#auto_title{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.gridLayout_2 = QGridLayout(self.auto_title)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout_2.setSizeConstraint(QLayout.SetDefaultConstraint)
        self.auto_record_checkBox = QCheckBox(self.auto_title)
        self.auto_record_checkBox.setObjectName(u"auto_record_checkBox")
        sizePolicy3 = QSizePolicy(QSizePolicy.Maximum, QSizePolicy.Preferred)
        sizePolicy3.setHorizontalStretch(0)
        sizePolicy3.setVerticalStretch(0)
        sizePolicy3.setHeightForWidth(self.auto_record_checkBox.sizePolicy().hasHeightForWidth())
        self.auto_record_checkBox.setSizePolicy(sizePolicy3)
        self.auto_record_checkBox.setFont(font1)
        self.auto_record_checkBox.setLayoutDirection(Qt.LeftToRight)
        self.auto_record_checkBox.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"\n"
"QCheckBox{margin-top: 4px; margin-left:4px;}\n"
"\n"
"QCheckBox::indicator:unchecked{\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"QCheckBox::indicator:unchecked:hover {\n"
"image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"QCheckBox::indicator:checked{\n"
"image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"QCheckBox::indicator:checked:hover {\n"
"image: url(:/btn_check/btn_check_s1_1.png);\n"
"}\n"
"	")
        self.auto_record_checkBox.setIconSize(QSize(16, 16))

        self.gridLayout_2.addWidget(self.auto_record_checkBox, 0, 0, 1, 1, Qt.AlignVCenter)

        self.auto_record_label = QLabel(self.auto_title)
        self.auto_record_label.setObjectName(u"auto_record_label")
        sizePolicy4 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy4.setHorizontalStretch(0)
        sizePolicy4.setVerticalStretch(0)
        sizePolicy4.setHeightForWidth(self.auto_record_label.sizePolicy().hasHeightForWidth())
        self.auto_record_label.setSizePolicy(sizePolicy4)
        self.auto_record_label.setFont(font1)
        self.auto_record_label.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.gridLayout_2.addWidget(self.auto_record_label, 0, 1, 1, 1)


        self.verticalLayout_2.addWidget(self.auto_title)

        self.auto_group = QWidget(self.RecordSetting_settings)
        self.auto_group.setObjectName(u"auto_group")
        sizePolicy.setHeightForWidth(self.auto_group.sizePolicy().hasHeightForWidth())
        self.auto_group.setSizePolicy(sizePolicy)
        self.gridLayout = QGridLayout(self.auto_group)
        self.gridLayout.setObjectName(u"gridLayout")
        self.max_auto_record_count_comboBox = QComboBox(self.auto_group)
        self.max_auto_record_count_comboBox.addItem("")
        self.max_auto_record_count_comboBox.addItem("")
        self.max_auto_record_count_comboBox.addItem("")
        self.max_auto_record_count_comboBox.setObjectName(u"max_auto_record_count_comboBox")
        self.max_auto_record_count_comboBox.setEnabled(True)

        self.gridLayout.addWidget(self.max_auto_record_count_comboBox, 2, 1, 1, 1)

        self.pre_record_label = QLabel(self.auto_group)
        self.pre_record_label.setObjectName(u"pre_record_label")
        self.pre_record_label.setFont(font)
        self.pre_record_label.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.gridLayout.addWidget(self.pre_record_label, 5, 0, 1, 1)

        self.max_auto_record_count_label = QLabel(self.auto_group)
        self.max_auto_record_count_label.setObjectName(u"max_auto_record_count_label")
        sizePolicy5 = QSizePolicy(QSizePolicy.MinimumExpanding, QSizePolicy.Fixed)
        sizePolicy5.setHorizontalStretch(0)
        sizePolicy5.setVerticalStretch(0)
        sizePolicy5.setHeightForWidth(self.max_auto_record_count_label.sizePolicy().hasHeightForWidth())
        self.max_auto_record_count_label.setSizePolicy(sizePolicy5)
        self.max_auto_record_count_label.setFont(font)
        self.max_auto_record_count_label.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.gridLayout.addWidget(self.max_auto_record_count_label, 2, 0, 1, 1)

        self.record_seconds_label = QLabel(self.auto_group)
        self.record_seconds_label.setObjectName(u"record_seconds_label")
        self.record_seconds_label.setFont(font)
        self.record_seconds_label.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.gridLayout.addWidget(self.record_seconds_label, 6, 0, 1, 1)

        self.record_seconds_lineEdit = QLineEdit(self.auto_group)
        self.record_seconds_lineEdit.setObjectName(u"record_seconds_lineEdit")

        self.gridLayout.addWidget(self.record_seconds_lineEdit, 6, 1, 1, 1)

        self.cf_enabled_checkBox = QCheckBox(self.auto_group)
        self.cf_enabled_checkBox.setObjectName(u"cf_enabled_checkBox")
        self.cf_enabled_checkBox.setEnabled(True)
        self.cf_enabled_checkBox.setFont(font)
        self.cf_enabled_checkBox.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"\n"
"QCheckBox::indicator:unchecked{\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"QCheckBox::indicator:unchecked:hover {\n"
"image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"QCheckBox::indicator:checked{\n"
"image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"QCheckBox::indicator:checked:hover {\n"
"image: url(:/btn_check/btn_check_s1_1.png);\n"
"}\n"
"QCheckBox::indicator:disabled {\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}")
        self.cf_enabled_checkBox.setChecked(False)

        self.gridLayout.addWidget(self.cf_enabled_checkBox, 8, 0, 1, 1)

        self.pre_record_seconds_lineEdit = QLineEdit(self.auto_group)
        self.pre_record_seconds_lineEdit.setObjectName(u"pre_record_seconds_lineEdit")
        sizePolicy1.setHeightForWidth(self.pre_record_seconds_lineEdit.sizePolicy().hasHeightForWidth())
        self.pre_record_seconds_lineEdit.setSizePolicy(sizePolicy1)

        self.gridLayout.addWidget(self.pre_record_seconds_lineEdit, 5, 1, 1, 1)

        self.cf_threshold_lineEdit = QLineEdit(self.auto_group)
        self.cf_threshold_lineEdit.setObjectName(u"cf_threshold_lineEdit")

        self.gridLayout.addWidget(self.cf_threshold_lineEdit, 8, 1, 1, 1)

        self.fz_enabled_checkBox = QCheckBox(self.auto_group)
        self.fz_enabled_checkBox.setObjectName(u"fz_enabled_checkBox")
        self.fz_enabled_checkBox.setFont(font)
        self.fz_enabled_checkBox.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"\n"
"QCheckBox::indicator:unchecked{\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"QCheckBox::indicator:unchecked:hover {\n"
"image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"QCheckBox::indicator:checked{\n"
"image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"QCheckBox::indicator:checked:hover {\n"
"image: url(:/btn_check/btn_check_s1_1.png);\n"
"}\n"
"QCheckBox::indicator:disabled {\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}")

        self.gridLayout.addWidget(self.fz_enabled_checkBox, 9, 0, 1, 1)

        self.fz_threshold_lineEdit = QLineEdit(self.auto_group)
        self.fz_threshold_lineEdit.setObjectName(u"fz_threshold_lineEdit")

        self.gridLayout.addWidget(self.fz_threshold_lineEdit, 9, 1, 1, 1)

        self.t_threshold_lineEdit = QLineEdit(self.auto_group)
        self.t_threshold_lineEdit.setObjectName(u"t_threshold_lineEdit")

        self.gridLayout.addWidget(self.t_threshold_lineEdit, 10, 1, 1, 1)

        self.t_enabled_checkbox = QCheckBox(self.auto_group)
        self.t_enabled_checkbox.setObjectName(u"t_enabled_checkbox")
        self.t_enabled_checkbox.setFont(font)
        self.t_enabled_checkbox.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"\n"
"QCheckBox::indicator:unchecked{\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"QCheckBox::indicator:unchecked:hover {\n"
"image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"QCheckBox::indicator:checked{\n"
"image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"QCheckBox::indicator:checked:hover {\n"
"image: url(:/btn_check/btn_check_s1_1.png);\n"
"}\n"
"QCheckBox::indicator:disabled {\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}")

        self.gridLayout.addWidget(self.t_enabled_checkbox, 10, 0, 1, 1)

        self.verticalSpacer_3 = QSpacerItem(20, 10, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.gridLayout.addItem(self.verticalSpacer_3, 1, 0, 1, 2)

        self.verticalSpacer = QSpacerItem(208, 5, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.gridLayout.addItem(self.verticalSpacer, 4, 0, 1, 2)

        self.verticalSpacer_2 = QSpacerItem(208, 5, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.gridLayout.addItem(self.verticalSpacer_2, 7, 0, 1, 2)

        self.auto_description = QLabel(self.auto_group)
        self.auto_description.setObjectName(u"auto_description")
        self.auto_description.setFont(font)
        self.auto_description.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.gridLayout.addWidget(self.auto_description, 0, 0, 1, 2)

        self.gridLayout.setColumnStretch(0, 1)

        self.verticalLayout_2.addWidget(self.auto_group)

        self.machine_title = QWidget(self.RecordSetting_settings)
        self.machine_title.setObjectName(u"machine_title")
        sizePolicy1.setHeightForWidth(self.machine_title.sizePolicy().hasHeightForWidth())
        self.machine_title.setSizePolicy(sizePolicy1)
        self.machine_title.setStyleSheet(u"QWidget#machine_title{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout_5 = QHBoxLayout(self.machine_title)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.machine_checkBox = QCheckBox(self.machine_title)
        self.machine_checkBox.setObjectName(u"machine_checkBox")
        sizePolicy6 = QSizePolicy(QSizePolicy.Maximum, QSizePolicy.Fixed)
        sizePolicy6.setHorizontalStretch(0)
        sizePolicy6.setVerticalStretch(0)
        sizePolicy6.setHeightForWidth(self.machine_checkBox.sizePolicy().hasHeightForWidth())
        self.machine_checkBox.setSizePolicy(sizePolicy6)
        self.machine_checkBox.setFont(font1)
        self.machine_checkBox.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"\n"
"QCheckBox{margin-top: 4px; margin-left:4px;}\n"
"\n"
"QCheckBox::indicator:unchecked{\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"QCheckBox::indicator:unchecked:hover {\n"
"image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"QCheckBox::indicator:checked{\n"
"image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"QCheckBox::indicator:checked:hover {\n"
"image: url(:/btn_check/btn_check_s1_1.png);\n"
"}\n"
"	")

        self.horizontalLayout_5.addWidget(self.machine_checkBox)

        self.machine_label = QLabel(self.machine_title)
        self.machine_label.setObjectName(u"machine_label")
        self.machine_label.setFont(font1)
        self.machine_label.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.horizontalLayout_5.addWidget(self.machine_label)


        self.verticalLayout_2.addWidget(self.machine_title)

        self.machine_group = QWidget(self.RecordSetting_settings)
        self.machine_group.setObjectName(u"machine_group")
        sizePolicy.setHeightForWidth(self.machine_group.sizePolicy().hasHeightForWidth())
        self.machine_group.setSizePolicy(sizePolicy)
        self.verticalLayout_3 = QVBoxLayout(self.machine_group)
        self.verticalLayout_3.setSpacing(9)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.machine_description = QLabel(self.machine_group)
        self.machine_description.setObjectName(u"machine_description")
        sizePolicy1.setHeightForWidth(self.machine_description.sizePolicy().hasHeightForWidth())
        self.machine_description.setSizePolicy(sizePolicy1)
        self.machine_description.setFont(font)
        self.machine_description.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.verticalLayout_3.addWidget(self.machine_description)

        self.verticalSpacer_4 = QSpacerItem(20, 10, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.verticalLayout_3.addItem(self.verticalSpacer_4)

        self.M101_enabled_checkBox = QCheckBox(self.machine_group)
        self.M101_enabled_checkBox.setObjectName(u"M101_enabled_checkBox")
        self.M101_enabled_checkBox.setFont(font)
        self.M101_enabled_checkBox.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"\n"
"QCheckBox::indicator:unchecked{\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"QCheckBox::indicator:unchecked:hover {\n"
"image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"QCheckBox::indicator:checked{\n"
"image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"QCheckBox::indicator:checked:hover {\n"
"image: url(:/btn_check/btn_check_s1_1.png);\n"
"}\n"
"QCheckBox::indicator:disabled {\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}")

        self.verticalLayout_3.addWidget(self.M101_enabled_checkBox)

        self.M102_enabled_checkBox = QCheckBox(self.machine_group)
        self.M102_enabled_checkBox.setObjectName(u"M102_enabled_checkBox")
        self.M102_enabled_checkBox.setFont(font)
        self.M102_enabled_checkBox.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"\n"
"QCheckBox::indicator:unchecked{\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"QCheckBox::indicator:unchecked:hover {\n"
"image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"QCheckBox::indicator:checked{\n"
"image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"QCheckBox::indicator:checked:hover {\n"
"image: url(:/btn_check/btn_check_s1_1.png);\n"
"}\n"
"QCheckBox::indicator:disabled {\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}")

        self.verticalLayout_3.addWidget(self.M102_enabled_checkBox)

        self.M103_enabled_checkBox = QCheckBox(self.machine_group)
        self.M103_enabled_checkBox.setObjectName(u"M103_enabled_checkBox")
        self.M103_enabled_checkBox.setEnabled(True)
        self.M103_enabled_checkBox.setFont(font)
        self.M103_enabled_checkBox.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"\n"
"QCheckBox::indicator:unchecked{\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"QCheckBox::indicator:unchecked:hover {\n"
"image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"QCheckBox::indicator:checked{\n"
"image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"QCheckBox::indicator:checked:hover {\n"
"image: url(:/btn_check/btn_check_s1_1.png);\n"
"}\n"
"QCheckBox::indicator:disabled {\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}")

        self.verticalLayout_3.addWidget(self.M103_enabled_checkBox)


        self.verticalLayout_2.addWidget(self.machine_group)


        self.horizontalLayout.addWidget(self.RecordSetting_settings, 0, Qt.AlignTop)

        self.RecordSetting_sample = QWidget(self.RecordSetting_body)
        self.RecordSetting_sample.setObjectName(u"RecordSetting_sample")
        self.verticalLayout_4 = QVBoxLayout(self.RecordSetting_sample)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.verticalLayout_4.setContentsMargins(-1, 9, -1, 9)
        self.sample_title_group = QWidget(self.RecordSetting_sample)
        self.sample_title_group.setObjectName(u"sample_title_group")
        sizePolicy.setHeightForWidth(self.sample_title_group.sizePolicy().hasHeightForWidth())
        self.sample_title_group.setSizePolicy(sizePolicy)
        self.horizontalLayout_7 = QHBoxLayout(self.sample_title_group)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.horizontalLayout_7.setContentsMargins(9, 0, -1, -1)
        self.sample_title = QWidget(self.sample_title_group)
        self.sample_title.setObjectName(u"sample_title")
        sizePolicy5.setHeightForWidth(self.sample_title.sizePolicy().hasHeightForWidth())
        self.sample_title.setSizePolicy(sizePolicy5)
        self.sample_title.setStyleSheet(u"QWidget#sample_title{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout_4 = QHBoxLayout(self.sample_title)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.sample_label = QLabel(self.sample_title)
        self.sample_label.setObjectName(u"sample_label")
        sizePolicy.setHeightForWidth(self.sample_label.sizePolicy().hasHeightForWidth())
        self.sample_label.setSizePolicy(sizePolicy)
        self.sample_label.setFont(font1)
        self.sample_label.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.horizontalLayout_4.addWidget(self.sample_label)


        self.horizontalLayout_7.addWidget(self.sample_title)

        self.horizontalSpacer_3 = QSpacerItem(360, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_7.addItem(self.horizontalSpacer_3)


        self.verticalLayout_4.addWidget(self.sample_title_group)

        self.sample_buttons = QWidget(self.RecordSetting_sample)
        self.sample_buttons.setObjectName(u"sample_buttons")
        sizePolicy1.setHeightForWidth(self.sample_buttons.sizePolicy().hasHeightForWidth())
        self.sample_buttons.setSizePolicy(sizePolicy1)
        self.horizontalLayout_8 = QHBoxLayout(self.sample_buttons)
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.sample_record_btn = QPushButton(self.sample_buttons)
        self.sample_record_btn.setObjectName(u"sample_record_btn")
        sizePolicy2.setHeightForWidth(self.sample_record_btn.sizePolicy().hasHeightForWidth())
        self.sample_record_btn.setSizePolicy(sizePolicy2)
        self.sample_record_btn.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }")
        icon = QIcon()
        icon.addFile(u":/btn_record/btn_record_s0_0.png", QSize(), QIcon.Normal, QIcon.Off)
        icon.addFile(u":/btn_record/btn_record_s1_0.png", QSize(), QIcon.Normal, QIcon.On)
        icon.addFile(u":/btn_record/btn_record_s0_0.png", QSize(), QIcon.Active, QIcon.Off)
        icon.addFile(u":/btn_record/btn_record_s1_0.png", QSize(), QIcon.Active, QIcon.On)
        self.sample_record_btn.setIcon(icon)
        self.sample_record_btn.setIconSize(QSize(30, 30))
        self.sample_record_btn.setCheckable(True)

        self.horizontalLayout_8.addWidget(self.sample_record_btn)

        self.sample_record_label = QLabel(self.sample_buttons)
        self.sample_record_label.setObjectName(u"sample_record_label")
        sizePolicy7 = QSizePolicy(QSizePolicy.Fixed, QSizePolicy.Preferred)
        sizePolicy7.setHorizontalStretch(0)
        sizePolicy7.setVerticalStretch(0)
        sizePolicy7.setHeightForWidth(self.sample_record_label.sizePolicy().hasHeightForWidth())
        self.sample_record_label.setSizePolicy(sizePolicy7)
        self.sample_record_label.setFont(font)
        self.sample_record_label.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.horizontalLayout_8.addWidget(self.sample_record_label)

        self.horizontalSpacer = QSpacerItem(30, 20, QSizePolicy.Fixed, QSizePolicy.Minimum)

        self.horizontalLayout_8.addItem(self.horizontalSpacer)

        self.sample_redo_btn = QPushButton(self.sample_buttons)
        self.sample_redo_btn.setObjectName(u"sample_redo_btn")
        sizePolicy2.setHeightForWidth(self.sample_redo_btn.sizePolicy().hasHeightForWidth())
        self.sample_redo_btn.setSizePolicy(sizePolicy2)
        self.sample_redo_btn.setStyleSheet(u"QPushButton { border: none; color: rgb(255, 255, 255); }")
        icon1 = QIcon()
        icon1.addFile(u":/btn_update/btn_update.png", QSize(), QIcon.Normal, QIcon.Off)
        self.sample_redo_btn.setIcon(icon1)
        self.sample_redo_btn.setIconSize(QSize(30, 30))

        self.horizontalLayout_8.addWidget(self.sample_redo_btn)

        self.sample_redo_label = QLabel(self.sample_buttons)
        self.sample_redo_label.setObjectName(u"sample_redo_label")
        sizePolicy7.setHeightForWidth(self.sample_redo_label.sizePolicy().hasHeightForWidth())
        self.sample_redo_label.setSizePolicy(sizePolicy7)
        self.sample_redo_label.setFont(font)
        self.sample_redo_label.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.horizontalLayout_8.addWidget(self.sample_redo_label)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_8.addItem(self.horizontalSpacer_4)


        self.verticalLayout_4.addWidget(self.sample_buttons)

        self.sample_animation_group = QWidget(self.RecordSetting_sample)
        self.sample_animation_group.setObjectName(u"sample_animation_group")
        self.horizontalLayout_9 = QHBoxLayout(self.sample_animation_group)
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.horizontalLayout_9.setContentsMargins(-1, -1, -1, 9)
        self.sample_2d_animation = QOpenGLWidget(self.sample_animation_group)
        self.sample_2d_animation.setObjectName(u"sample_2d_animation")

        self.horizontalLayout_9.addWidget(self.sample_2d_animation)

        self.horizontalSpacer_5 = QSpacerItem(5, 20, QSizePolicy.Fixed, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_5)


        self.verticalLayout_4.addWidget(self.sample_animation_group)


        self.horizontalLayout.addWidget(self.RecordSetting_sample)

        self.horizontalLayout.setStretch(0, 2)
        self.horizontalLayout.setStretch(1, 5)

        self.verticalLayout.addWidget(self.RecordSetting_body)

        self.RecordSetting_save_close = QWidget(RecordSetting_window)
        self.RecordSetting_save_close.setObjectName(u"RecordSetting_save_close")
        self.horizontalLayout_3 = QHBoxLayout(self.RecordSetting_save_close)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_3.setContentsMargins(-1, -1, 20, 20)
        self.horizontalSpacer_6 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_6)

        self.RecordSetting_save_btn = QPushButton(self.RecordSetting_save_close)
        self.RecordSetting_save_btn.setObjectName(u"RecordSetting_save_btn")
        sizePolicy1.setHeightForWidth(self.RecordSetting_save_btn.sizePolicy().hasHeightForWidth())
        self.RecordSetting_save_btn.setSizePolicy(sizePolicy1)
        self.RecordSetting_save_btn.setMinimumSize(QSize(10, 10))
        self.RecordSetting_save_btn.setFont(font)
        self.RecordSetting_save_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color:#5448B6;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7AFEC6;\n"
"     color: rgb(255, 255, 255);\n"
"    border:none\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border:none\n"
"}")

        self.horizontalLayout_3.addWidget(self.RecordSetting_save_btn)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_2)

        self.RecordSetting_close_btn = QPushButton(self.RecordSetting_save_close)
        self.RecordSetting_close_btn.setObjectName(u"RecordSetting_close_btn")
        sizePolicy1.setHeightForWidth(self.RecordSetting_close_btn.sizePolicy().hasHeightForWidth())
        self.RecordSetting_close_btn.setSizePolicy(sizePolicy1)
        self.RecordSetting_close_btn.setMinimumSize(QSize(10, 10))
        self.RecordSetting_close_btn.setFont(font)
        self.RecordSetting_close_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: 2px solid #7AFEC6;\n"
"    color: rgb(255, 255, 255);\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")

        self.horizontalLayout_3.addWidget(self.RecordSetting_close_btn)

        self.horizontalLayout_3.setStretch(0, 60)
        self.horizontalLayout_3.setStretch(1, 8)
        self.horizontalLayout_3.setStretch(2, 2)
        self.horizontalLayout_3.setStretch(3, 8)

        self.verticalLayout.addWidget(self.RecordSetting_save_close)

        self.verticalLayout.setStretch(0, 10)
        self.verticalLayout.setStretch(1, 1)

        self.retranslateUi(RecordSetting_window)

        self.max_auto_record_count_comboBox.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(RecordSetting_window)
    # setupUi

    def retranslateUi(self, RecordSetting_window):
        RecordSetting_window.setWindowTitle(QCoreApplication.translate("RecordSetting_window", u"Form", None))
        self.manual_label.setText(QCoreApplication.translate("RecordSetting_window", u"\u624b\u52d5", None))
        self.manual_description.setText(QCoreApplication.translate("RecordSetting_window", u"\u5728\u4e3b\u756b\u9762\u9ede\u64ca\u300c\u9304\u88fd\u300d\u6309\u9215\u3002", None))
        self.auto_record_checkBox.setText("")
        self.auto_record_label.setText(QCoreApplication.translate("RecordSetting_window", u"\u81ea\u52d5", None))
        self.max_auto_record_count_comboBox.setItemText(0, QCoreApplication.translate("RecordSetting_window", u"100\u7b46", None))
        self.max_auto_record_count_comboBox.setItemText(1, QCoreApplication.translate("RecordSetting_window", u"500\u7b46", None))
        self.max_auto_record_count_comboBox.setItemText(2, QCoreApplication.translate("RecordSetting_window", u"1000\u7b46", None))

        self.pre_record_label.setText(QCoreApplication.translate("RecordSetting_window", u"\u9810\u9304\u79d2\u6578", None))
        self.max_auto_record_count_label.setText(QCoreApplication.translate("RecordSetting_window", u"\u9304\u88fd\u7b46\u6578", None))
        self.record_seconds_label.setText(QCoreApplication.translate("RecordSetting_window", u"\u9304\u88fd\u79d2\u6578", None))
        self.record_seconds_lineEdit.setText(QCoreApplication.translate("RecordSetting_window", u"10", None))
        self.record_seconds_lineEdit.setPlaceholderText(QCoreApplication.translate("RecordSetting_window", u"10", None))
        self.cf_enabled_checkBox.setText(QCoreApplication.translate("RecordSetting_window", u"C.F.:", None))
        self.pre_record_seconds_lineEdit.setText(QCoreApplication.translate("RecordSetting_window", u"0", None))
        self.pre_record_seconds_lineEdit.setPlaceholderText(QCoreApplication.translate("RecordSetting_window", u"0", None))
        self.fz_enabled_checkBox.setText(QCoreApplication.translate("RecordSetting_window", u"Fz:", None))
        self.t_enabled_checkbox.setText(QCoreApplication.translate("RecordSetting_window", u"T:", None))
        self.auto_description.setText(QCoreApplication.translate("RecordSetting_window", u"\u5728\u53d7\u529b\u5230\u9054\u95a5\u503c\u6642\u81ea\u52d5\u89f8\u767c\u9304\u88fd\u3002", None))
        self.machine_checkBox.setText("")
        self.machine_label.setText(QCoreApplication.translate("RecordSetting_window", u"\u6a5f\u53f0\u63a7\u5236 M Code", None))
        self.machine_description.setText(QCoreApplication.translate("RecordSetting_window", u"[\u8aaa\u660e\u6587\u5b57] \u4ee5\u6a5f\u53f0\u66ab\u5b58\u5668\u503c\u89f8\u767c\u3002", None))
        self.M101_enabled_checkBox.setText(QCoreApplication.translate("RecordSetting_window", u"M101", None))
        self.M102_enabled_checkBox.setText(QCoreApplication.translate("RecordSetting_window", u"M102", None))
        self.M103_enabled_checkBox.setText(QCoreApplication.translate("RecordSetting_window", u"M103", None))
        self.sample_label.setText(QCoreApplication.translate("RecordSetting_window", u"\u9304\u88fd\u6a23\u672c", None))
        self.sample_record_btn.setText("")
        self.sample_record_label.setText(QCoreApplication.translate("RecordSetting_window", u"\u958b\u59cb\u9304\u88fd", None))
        self.sample_redo_btn.setText("")
        self.sample_redo_label.setText(QCoreApplication.translate("RecordSetting_window", u"\u91cd\u65b0\u9304\u88fd", None))
        self.RecordSetting_save_btn.setText(QCoreApplication.translate("RecordSetting_window", u"\u5132\u5b58", None))
        self.RecordSetting_close_btn.setText(QCoreApplication.translate("RecordSetting_window", u"\u95dc\u9589", None))
    # retranslateUi

