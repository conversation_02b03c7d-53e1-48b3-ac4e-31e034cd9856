<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Setting_Window</class>
 <widget class="QWidget" name="Setting_Window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>500</width>
    <height>165</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>500</width>
    <height>165</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>500</width>
    <height>170</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#Setting_Window
{
background-color: #0C0C44;
border: 2px solid #5448B6;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QWidget" name="setting_title" native="true">
     <property name="styleSheet">
      <string notr="true">QWidget#setting_title{
background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));
border-radius:5px;
}
</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel" name="Setting_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>檔案路徑</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
        <property name="margin">
         <number>5</number>
        </property>
        <property name="indent">
         <number>-1</number>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_1">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QGridLayout" name="gridLayout" columnstretch="1,2,15,1">
     <property name="rightMargin">
      <number>8</number>
     </property>
     <property name="horizontalSpacing">
      <number>8</number>
     </property>
     <property name="verticalSpacing">
      <number>6</number>
     </property>
     <item row="0" column="2">
      <widget class="QLineEdit" name="FolderPath_lineEdit"/>
     </item>
     <item row="1" column="1">
      <widget class="QLabel" name="txtname_lable">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="layoutDirection">
        <enum>Qt::LeftToRight</enum>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(255, 255, 255);</string>
       </property>
       <property name="text">
        <string>msra檔名 :</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="margin">
        <number>2</number>
       </property>
      </widget>
     </item>
     <item row="1" column="2">
      <widget class="QLineEdit" name="txtname_lineEdit"/>
     </item>
     <item row="0" column="1">
      <widget class="QLabel" name="FolderPath_lable">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="layoutDirection">
        <enum>Qt::LeftToRight</enum>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(255, 255, 255);</string>
       </property>
       <property name="text">
        <string>資料夾路徑 :</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
       </property>
       <property name="margin">
        <number>2</number>
       </property>
      </widget>
     </item>
     <item row="1" column="3">
      <widget class="QPushButton" name="Setting_AutoFileName_btn">
       <property name="styleSheet">
        <string notr="true">border:none;</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../../system_image/system_image.qrc">
         <normaloff>:/btn_update/btn_update.png</normaloff>:/btn_update/btn_update.png</iconset>
       </property>
       <property name="iconSize">
        <size>
         <width>18</width>
         <height>18</height>
        </size>
       </property>
      </widget>
     </item>
     <item row="0" column="0">
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QWidget" name="Setting_save_close" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="0,8,3,1,3">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>16</number>
      </property>
      <property name="topMargin">
       <number>8</number>
      </property>
      <property name="rightMargin">
       <number>8</number>
      </property>
      <property name="bottomMargin">
       <number>8</number>
      </property>
      <item>
       <widget class="QCheckBox" name="save_rawdata_checkBox">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
QCheckBox::indicator:disabled {
image: url(:/btn_check/btn_check_s0_0.png);
}</string>
        </property>
        <property name="text">
         <string>儲存原始數據 (raw data)</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_10">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="Setting_save_btn">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color:#5448B6;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color:#7AFEC6;
     color: rgb(255, 255, 255);
    border:none
}

QPushButton:pressed {
    background-color: gray;
	border:none
}</string>
        </property>
        <property name="text">
         <string>儲存</string>
        </property>
        <property name="iconSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_5">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="Setting_close_btn">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color: #0C0C44;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color: #0C0C44;
	border: 2px solid #7AFEC6;
    color: rgb(255, 255, 255);
	border-radius:5px;
	padding:2px;
}

QPushButton:pressed {
    background-color: gray;
	border: none;
}</string>
        </property>
        <property name="text">
         <string>關閉</string>
        </property>
        <property name="iconSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../system_image/system_image.qrc"/>
 </resources>
 <connections/>
</ui>
