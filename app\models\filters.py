import numpy as np
from scipy import signal
import pandas as pd
from abc import ABC, abstractmethod
from . import logger


NdArrayFloat1D = np.ndarray  # 語意型別提示
FILTER_TYPES = ["Meanfilter_radio", "Medianfilter_radio", "Gaussianfilter_radio", "MSfilter_radio", "Lowfilter_radio", "Highfilter_radio", "SGfilter_radio", "MAfilter_radio"]

class Filter(ABC):
    MAX_WINDOW_SIZE = 200
    MIN_WINDOW_SIZE = 2

    def __init__(self, window_size=3):
        self.window_size = window_size

    @property
    def window_size(self):
        return self._window_size

    @window_size.setter
    def window_size(self, value):
        self._window_size = self._validate_window_size(value)

    def _validate_window_size(self, value: int) -> int:
        # if not self.MIN_WINDOW_SIZE <= value <= self.MAX_WINDOW_SIZE:
        #     logger.info(f"Filter: window size({value}) out of range, "
        #     f"auto fix to {self.MIN_WINDOW_SIZE}~{self.MAX_WINDOW_SIZE}")
        return min(max(value, self.MIN_WINDOW_SIZE), self.MAX_WINDOW_SIZE)

    def applyTo(self, data: NdArrayFloat1D, hist_data: NdArrayFloat1D=None) -> None:
        """Apply the concrete filter _do_filter to *data* in-place.

        If *hist_data* is supplied the last (window_size-1) points of that
        history are prepended **only for the purpose of filtering**; the
        filtered tail that corresponds to the original *data* length is then
        copied back into *data* so the caller always sees the result inside
        the same ndarray object.
        """

        if data is None or len(data) == 0:
            return

        if hist_data is not None and len(hist_data):
            # take up to window_size-1 valid history samples
            tail = pd.Series(hist_data).dropna().values[-(self.window_size - 1):]
            if len(tail):
                original_len = len(data)
                combined = np.concatenate([tail, data])
                self._do_filter(combined)               # in-place on temp
                # copy the filtered part that corresponds to *data* back
                pad = len(tail) // 2
                data[:] = combined[pad: pad+original_len]
                return

        # fall-back: no (usable) history – filter in-place directly
        self._do_filter(data)

    @abstractmethod
    def _do_filter(self, data: NdArrayFloat1D) -> None:
        pass


class OddWindowSizeFilter(Filter):
    def __init__(self, window_size=3):
        super().__init__(window_size=window_size)
    
    def _validate_window_size(self, value: int) -> int:
        value = super()._validate_window_size(value)
        if value % 2 == 0:
            value -= 1
            # logger.info(f"OddWindowSizeFilter: auto fix window size to {value}")
        return value



class MovingAverage(Filter):
    def applyTo(self, data: NdArrayFloat1D, hist_data: NdArrayFloat1D=None) -> None:

        if data is None or len(data) == 0:
            return

        if hist_data is not None and len(hist_data):
            # take up to window_size-1 valid history samples
            tail = pd.Series(hist_data).dropna().values[-(self.window_size - 1):]
            if len(tail):
                original_len = len(data)
                combined = np.concatenate([tail, data])
                self._do_filter(combined)               # in-place on temp
                # copy the filtered part that corresponds to *data* back
                data[:] = combined[-original_len:]
                return

        # fall-back: no (usable) history – filter in-place directly
        self._do_filter(data)

    def _do_filter(self, data: NdArrayFloat1D) -> None:
        Avg_Bending = pd.Series(data)
        data[:] = Avg_Bending.rolling(window=self.window_size, min_periods=1).mean().values


class MeanFilter(OddWindowSizeFilter):
    def _do_filter(self, data: NdArrayFloat1D) -> None:
        padding_size = self.window_size // 2
        data_padded = np.pad(data, pad_width=padding_size, mode='edge')  #copy with padding

        n = len(data)
        for i in range(n):
            data[i] = np.mean(data_padded[i : i + self.window_size])


class MedianFilter(OddWindowSizeFilter):
    def _do_filter(self, data: NdArrayFloat1D) -> None:
        padding_size = self.window_size // 2
        data_padded = np.pad(data, pad_width=padding_size, mode='edge')  #copy with padding

        n = len(data)
        for i in range(n):
            data[i] = np.median(data_padded[i : i + self.window_size])


class GaussianFilter(OddWindowSizeFilter):
    def __init__(self, window_size: int = 3, sigma: float = 1.0):
        self._sigma = sigma
        super().__init__(window_size)  #it will use _sigma to reset _kernel
    
    def _build_kernel(self) -> NdArrayFloat1D:
        half_size = self.window_size // 2
        filter_range = np.linspace(-half_size, half_size, self.window_size)
        kernel = [1 / (self.sigma * np.sqrt(2*np.pi)) * np.exp(-i**2 / (2*self.sigma**2)) for i in filter_range]
        kernel = np.array(kernel, dtype=np.float64)
        kernel /= kernel.sum()
        return kernel

    @OddWindowSizeFilter.window_size.setter
    def window_size(self, value):
        OddWindowSizeFilter.window_size.fset(self, value)
        self._kernel = self._build_kernel()

    @property
    def sigma(self):
        return self._sigma

    @sigma.setter
    def sigma(self, value):
        self._sigma = value
        self._kernel = self._build_kernel()

    def _do_filter(self, data: NdArrayFloat1D) -> None:
        padding_size = self.window_size // 2
        data_padded = np.pad(data, pad_width=padding_size, mode='edge')  #copy with padding
        data[:] = np.convolve(data_padded, self._kernel, mode='valid')


class MSFilter(OddWindowSizeFilter):
    """ Machsync濾波 """
    def __init__(self, window_size: int = 3, diff_value: float = 0.003, result_value: float = 10, sample_rate: int = 10000, decimal_places: int = 3):      
        super().__init__(window_size)
        self.diff_value = diff_value
        self.result_value = result_value
        self.sample_rate = sample_rate
        self.decimal_places = decimal_places

    def _do_filter(self, data: NdArrayFloat1D) -> None:
        result_list = data.copy()

        # 先進行中值濾波和均值濾波
        median_filter = MedianFilter(self.window_size)
        median_filter.applyTo(result_list)
        mean_filter = MeanFilter(self.window_size)
        mean_filter.applyTo(result_list)
        
        ms_sample_rate = self.sample_rate

        diff_x = []
        diff_time=[]
        index=0
        for i in range(0, len(result_list)-1):
            diff_x.append(abs(result_list[i] - result_list[i+1]))
            diff_time.append(index)
            index+=1

        tolerance_count = 0
        test_start = False
        threshold_value = max(diff_x) / 2
        result_value_tmp = round(max(data, key=abs) / self.result_value, self.decimal_places)

        for i in range(0, len(diff_x)):
            if (diff_x[i] > threshold_value and diff_x[i] > self.diff_value) or result_list[i] >= result_value_tmp:
                tolerance_count = ms_sample_rate * 0.05  # 保留0.05s資料
                result_list[i] = data[i]  # 保留原資料

                if test_start == False:  # 第一次超越起伏，保留前0.1s資料
                    temp_i = int(i - tolerance_count) if i - tolerance_count >= 0 else 0 #以防超越第0筆資料
                    result_list[temp_i:i] = data[temp_i:i]

                test_start = True

            elif tolerance_count > 0:
                tolerance_count -= 1
                result_list[i] = data[i]  # 保留原資料
            else:
                test_start = False
        
        data[:] = result_list


class ButterworthFilter(Filter):
    def __init__(self, cutoff_freq: float = 999,
    fs: int = 10000, btype: str = 'low', order: int = 4,
    window_size: int = 3):     
        super().__init__(window_size)
        self.cutoff_freq = cutoff_freq
        self.fs = fs
        self.btype = btype
        self.order = order

    def _minRequiredLength(self, a, b) -> int:
        return 3 * max(len(b), len(a))  #this is the logic in scipy.signal.filtfilt

    def _do_filter(self, data: NdArrayFloat1D) -> None:
        nyquist_fs = self.fs / 2
        normalized_cutoff_freq = self.cutoff_freq / nyquist_fs
        b, a = signal.butter(self.order, normalized_cutoff_freq, self.btype)
        minRequiredLength = self._minRequiredLength(a, b)
        if len(data) < minRequiredLength:
            return
        data[:] = signal.filtfilt(b, a, data)


class SavitzkyGolayFilter(OddWindowSizeFilter):
    def __init__(self, window_size: int = 4, polyorder: int = 3):  
        super().__init__(window_size)
        self.polyorder = polyorder
    
    @OddWindowSizeFilter.window_size.setter
    def window_size(self, value):
        if hasattr(self, '_polyorder') and self.polyorder >= self.window_size:
            value += 1
        OddWindowSizeFilter.window_size.fset(self, value)

    @property
    def polyorder(self):
        return self._polyorder

    @polyorder.setter
    def polyorder(self, value):
        self._polyorder = min(value, self.window_size - 1) #polyorder must be less than window_size

    def _do_filter(self, data: NdArrayFloat1D) -> None:
        minRequiredLength = self.window_size
        if len(data) < minRequiredLength:
            return
        data[:] = signal.savgol_filter(data, self.window_size, self._polyorder)



def apply_filter(filter_type: str, filter_data: dict, 
bending_x: np.ndarray, bending_y: np.ndarray, bending_xy: np.ndarray, 
tension: np.ndarray, torsion: np.ndarray,
hist_bending_x: np.ndarray=None, hist_bending_y: np.ndarray=None, 
hist_bending_xy: np.ndarray=None,
hist_tension: np.ndarray=None, hist_torsion: np.ndarray=None) -> tuple:
    # 沒用濾波器則直接回傳
    if filter_type is None or filter_type == "" or filter_type == "Nofilter_radio":
        return bending_x, bending_y, bending_xy, tension, torsion
    
    # 根據濾波器類型進行處理
    if filter_type == "Meanfilter_radio":
        k = filter_data["filter_values"]
        filter = MeanFilter(k)
    elif filter_type == "Medianfilter_radio":
        k = filter_data["filter_values"]
        filter = MedianFilter(k)
    elif filter_type == "Gaussianfilter_radio":
        k = filter_data["filter_values"]
        filter = GaussianFilter(k, 1)
    elif filter_type == "MSfilter_radio":
        k = filter_data["filter_values"]
        filter = MSFilter(k, 0.003, 10, filter_data["sampleRate"], filter_data["decimal_places"])
    elif filter_type == "Lowfilter_radio":
        cutoff_low = int(filter_data["Lowfilter_edit"])
        filter = ButterworthFilter(cutoff_low, filter_data["sampleRate"], "low", order=2)
    elif filter_type == "Highfilter_radio":
        cutoff_high = int(filter_data["Highfilter_edit"])
        filter = ButterworthFilter(cutoff_high, filter_data["sampleRate"], "high")
    elif filter_type == "SGfilter_radio":
        WL = int(filter_data["SGfilter_edit"])
        filter = SavitzkyGolayFilter(WL, 3)
    elif filter_type == "MAfilter_radio":
        MV = int(filter_data["MAfilter_edit"])
        filter = MovingAverage(MV)
    else:
        logger.debug(f"undefined filter type: {filter_type}")
        return bending_x, bending_y, bending_xy, tension, torsion

    if filter_type != "MAfilter_radio":
        filter.applyTo(bending_x, hist_bending_x)
        filter.applyTo(bending_y, hist_bending_y)
        bending_xy = np.sqrt(bending_x**2 + bending_y**2)
        filter.applyTo(tension, hist_tension)
        filter.applyTo(torsion, hist_torsion)
    else:
        # 套用filter的順序與上面不同：先算bending_xy，再套用filter
        bending_xy = np.sqrt(bending_x**2 + bending_y**2)
        filter.applyTo(bending_x, hist_bending_x)
        filter.applyTo(bending_y, hist_bending_y)
        filter.applyTo(bending_xy, hist_bending_xy)
        filter.applyTo(tension, hist_tension)
        filter.applyTo(torsion, hist_torsion)

    return bending_x, bending_y, bending_xy, tension, torsion
