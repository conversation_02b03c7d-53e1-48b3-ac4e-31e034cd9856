#!/usr/bin/env python3
"""
測試 Controller 中的 Socket Server 功能
"""

import sys
import os
import time
import socket
import threading

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_server_connection(host='127.0.0.1', port=1333, max_attempts=5):
    """測試 server 連接"""
    print(f"測試連接到 {host}:{port}")
    
    for attempt in range(max_attempts):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"✅ 成功連接到 {host}:{port}")
                return True
            else:
                print(f"❌ 嘗試 {attempt + 1}/{max_attempts}: 無法連接到 {host}:{port}")
                
        except Exception as e:
            print(f"❌ 嘗試 {attempt + 1}/{max_attempts}: 連接錯誤 - {e}")
        
        if attempt < max_attempts - 1:
            time.sleep(1)
    
    print(f"❌ 所有嘗試都失敗，無法連接到 {host}:{port}")
    return False

def send_test_data(host='127.0.0.1', port=1333, duration=5):
    """發送測試數據"""
    try:
        print(f"連接到 {host}:{port} 並發送測試數據...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.connect((host, port))
        print("✅ 已連接")
        
        start_time = time.time()
        packet_count = 0
        
        while (time.time() - start_time) < duration:
            # 模擬 32 字節的數據包
            test_data = f"TestData_{packet_count:06d}_" + "A" * 50
            sock.send(test_data.encode())
            packet_count += 1
            
            if packet_count % 100 == 0:
                print(f"已發送 {packet_count} 個數據包")
            
            time.sleep(0.01)  # 10ms 間隔
        
        print(f"✅ 完成發送，總共 {packet_count} 個數據包")
        
    except Exception as e:
        print(f"❌ 發送數據時發生錯誤: {e}")
    finally:
        try:
            sock.close()
        except:
            pass
        print("連接已關閉")

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='測試 Controller Socket Server')
    parser.add_argument('--host', default='127.0.0.1', help='服務器地址')
    parser.add_argument('--port', type=int, default=1333, help='服務器端口')
    parser.add_argument('--mode', choices=['test', 'send'], default='test',
                       help='測試模式: test(僅測試連接), send(發送數據)')
    parser.add_argument('--duration', type=int, default=5, help='發送數據持續時間（秒）')
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("Controller Socket Server 測試工具")
    print("=" * 50)
    
    if args.mode == 'test':
        print("測試 Socket Server 是否正在運行...")
        if test_server_connection(args.host, args.port):
            print("✅ Socket Server 正在運行")
        else:
            print("❌ Socket Server 未運行或無法連接")
            print("\n請確保：")
            print("1. 主程式已啟動 (npm run dev)")
            print("2. Socket Server 已初始化")
            print("3. 防火牆允許連接")
    
    elif args.mode == 'send':
        print("測試發送數據到 Socket Server...")
        if test_server_connection(args.host, args.port):
            send_test_data(args.host, args.port, args.duration)
        else:
            print("❌ 無法連接到 Socket Server，跳過數據發送測試")
    
    print("=" * 50)
    print("測試完成")

if __name__ == '__main__':
    main()
