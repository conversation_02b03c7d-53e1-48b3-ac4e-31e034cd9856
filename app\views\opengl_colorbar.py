import numpy as np
import pyqtgraph as pg
from PySide2.QtWidgets import QVBoxLayout, QWidget

class DrawColorBar(QWidget):
    def __init__(self, colorbar=None):  # 允許接收 colorbar 參數
        super().__init__()
        self.colorbar = colorbar
        self.setGeometry(100, 100, 800, 600)


        self.color_spectrum = 0  # 預設顏色範圍
        
        self.CFmax = 10  # 預設最大值

        self.setupUI()
        self.setupColorBar()
    
    def setupUI(self):
        """設置 UI 佈局"""
        layout = QVBoxLayout(self)
        self.setLayout(layout)
        self.gr_wid = pg.GraphicsLayoutWidget()
        layout.addWidget(self.gr_wid)
        layout.setContentsMargins(0, 0, 0, 0)
    
    def getColorMap(self):
        """根據 self.color_spectrum 設置顏色映射"""
        if self.color_spectrum == 0:  # 綠紅圖
            colors = [
                (0.0, (0, 255, 0)),  # 綠色
                (0.5, (255, 255, 0)),  # 黃色
                (1.0, (255, 0, 0))  # 紅色
            ]
        elif self.color_spectrum == 1:  # 紅白色溫
            colors = [
                (0.0, (255, 244, 237)),  # 淡粉橙色
                (0.5, (255, 185, 105)),  # 橘黃色
                (1.0, (255, 51, 0))  # 深橙紅色
            ]
        elif self.color_spectrum == 2:  # 彩虹
            colors = [
                (0.0, (127, 0, 255)),  # 紫色
                (0.166, (0, 0, 255)),  # 深藍色
                (0.333, (0, 127, 255)),  # 淺藍色
                (0.5, (0, 255, 0)),  # 綠色
                (0.666, (255, 255, 0)),  # 黃色
                (1.0, (255, 0, 0))  # 紅色
            ]
        else:
            colors = [(0.0, (0, 0, 0)), (1.0, (255, 255, 255))]  # 默認黑白漸變
        
        return pg.ColorMap(*zip(*colors))
    
    def setupColorBar(self):
        """設置顏色條，保留刻度文字但不需要可調整的按鈕"""
        # 如果已經存在 ColorBar，先移除它
        if hasattr(self, 'bar'):
            self.gr_wid.removeItem(self.bar)
            self.bar = None
        
        self.cmap = self.getColorMap()
        
        # 創建 ColorBarItem
        self.bar = pg.ColorBarItem(
            limits=(-300, 300),  # 設定顏色條的數值範圍
            rounding=1000,
            width=5,
            colorMap=self.cmap,
            interactive=False  # 設置為非交互式
        )

        # 動態計算刻度
        # 計算刻度間距
        # CFmax = self.CFmax
        tick_interval = self.CFmax / 6  # 因為要分成7個點，所以間距是CFmax/6
        # 計算刻度範圍的延伸量（約為CFmax的1/6）
        range_extension = self.CFmax / 6
        
        # 計算刻度範圍
        low = -range_extension
        high = self.CFmax + range_extension
        
        # 生成刻度位置和標籤
        tick_positions = [i * tick_interval for i in range(7)]  # 0到6，共7個點
        tick_labels = [str(int(pos)) for pos in tick_positions]
        
        # 更新顏色條刻度
        self.updateColorBarTicks(low, high, tick_positions, tick_labels)

        # 隱藏可調整的範圍按鈕
        self.bar.interactive = False
        
        # 調整顏色條的位置
        self.bar.getAxis('bottom').setHeight(20)  # 減少底部空間
        self.bar.getAxis('top').setHeight(20)     # 減少頂部空間
        
        self.gr_wid.addItem(self.bar, row=2, col=0, colspan=2)
        self.gr_wid.setContentsMargins(0, 0, 0, 0)
        
    
    #TODO:動態調整colorbar的數值範圍和刻度文字
    def updateColorBarRange(self, low, high):
        """更新顏色條的數值範圍"""
        self.bar.setLevels(low=low, high=high)
        self.bar.setImageItem(None)  # 可能需要根據實際情況更新顏色條的顯示

    def updateColorBarTicks(self, low, high, tick_positions, tick_labels):
        """更新顏色條的刻度文字"""
        self.bar.setLevels(low=low, high=high)  # 設定顯示範圍
        self.bar.getAxis('right').setTicks([[ (tick_positions[i], tick_labels[i]) for i in range(len(tick_positions)) ]])
