from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
from .message_box import Ui_Dialog  

class Ui_Confirm_Window(QDialog, Ui_Dialog):
    def __init__(self, message, parent=None):
        super().__init__(parent)
        self.setupUi(self)
        # 設定無邊框
        self.setWindowFlags(self.windowFlags() | Qt.FramelessWindowHint)

        # 設定訊息內容
        self.set_message(message)

        # 綁定按鈕事件
        self.btv_yes_btn.clicked.connect(self.accept)  # 點擊 Yes 按鈕時關閉對話框並回傳 accept
        self.btn_no_btn.clicked.connect(self.reject)  # 點擊 No 按鈕時關閉對話框並回傳 reject
        
	

    def set_message(self, message):
        """更新對話框的訊息"""
        self.message.setText(message)

    def get_result(self):
        """顯示對話框並回傳使用者的選擇"""
        result = self.exec_()
        return result == QDialog.Accepted  # 回傳 True 代表 Yes，False 代表 No
