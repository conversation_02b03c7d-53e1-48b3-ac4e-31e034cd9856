# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'hint_window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *


class Ui_hint_window(object):
    def setupUi(self, hint_window):
        if not hint_window.objectName():
            hint_window.setObjectName(u"hint_window")
        hint_window.resize(400, 100)
        hint_window.setStyleSheet(u"QWidget#hint_window{\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"}")
        self.gridLayout = QGridLayout(hint_window)
        self.gridLayout.setObjectName(u"gridLayout")
        self.label = QLabel(hint_window)
        self.label.setObjectName(u"label")
        font = QFont()
        font.setFamily(u"Arial Black")
        font.setPointSize(12)
        font.setBold(True)
        font.setWeight(75)
        self.label.setFont(font)
        self.label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.label.setAlignment(Qt.AlignCenter)

        self.gridLayout.addWidget(self.label, 0, 0, 1, 1)


        self.retranslateUi(hint_window)

        QMetaObject.connectSlotsByName(hint_window)
    # setupUi

    def retranslateUi(self, hint_window):
        hint_window.setWindowTitle(QCoreApplication.translate("hint_window", u"Form", None))
        self.label.setText(QCoreApplication.translate("hint_window", u"message", None))
    # retranslateUi

