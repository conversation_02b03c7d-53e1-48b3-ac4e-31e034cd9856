"""
End-to-end test for <PERSON>oder<PERSON>orker when Gaussian filter is selected.

It verifies that:
1.  output array lengths are correct
2.  tare / linear scaling keeps 0 V → 0 N
3.  Gaussian filter (window 5, σ=1) uses the four history points from the
    previous frame when filtering the very first sample of the next frame.
"""

import numpy as np
from app.models.decoder_Worker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.models.filters import GaussianFilter

DIV     = 6553.5                # _HEX16_TO_FLOAT_DIVISOR
SR      = 10_000
WIN     = 5                     # odd window
SIGMA   = 1.0
NPKT    = 800                   # sample_N for 10 kHz
HIFREQS = [i for i in range(NPKT) if i % 201]    # 796 usable pkt-indices


# ---------- helpers ----------------------------------------------------------
def w_from_v(v: float) -> str:
    """encode volts → 4-hex characters"""
    return f"{int(round(v*DIV)) & 0xFFFF:04x}"

def low_pkt() -> str:           # 32 chars = 16 bytes
    return "00"*16

def hi_pkt(v: float) -> str:
    return w_from_v(v)*4 + "00"*8         # vary Fx only

def frame_tail(tail_values):
    """Build a frame whose LAST *queued* hi-freq packets carry the given
    voltages so they become the history for the next frame.

    DecoderWorker skips the final (window_size-1) packets when queuing raw
    data (see socket_decoder).  Therefore, placing the history values in the
    very last packets would prevent them from being queued.  Instead we
    inject them just *before* that skipped tail so they are included in the
    queue and available for the next frame.
    """
    # index of the last packet that WILL be queued
    last_queued_idx = NPKT - (WIN - 1) - 1  # 800-4-1 = 795 when WIN=5
    inject = list(range(last_queued_idx, last_queued_idx - len(tail_values), -1))
    vals   = tail_values[::-1]
    vi = 0
    pkts = []
    for i in range(NPKT):
        if i % 201 == 0:
            pkts.append(low_pkt())
        elif i in inject:
            pkts.append(hi_pkt(vals[vi])); vi += 1
        else:
            pkts.append(hi_pkt(0.0))
    return "".join(pkts)

def frame_head(head_values):
    """frame with head_values in first usable packets 1.."""
    vi = 0
    pkts = []
    for i in range(NPKT):
        if i % 201 == 0:
            pkts.append(low_pkt())
        elif vi < len(head_values):
            pkts.append(hi_pkt(head_values[vi])); vi += 1
        else:
            pkts.append(hi_pkt(0.0))
    return "".join(pkts)

def tooldata():
    return dict(
        toolname="UT", toolip="0", toolmac="00", sample_rate=SR,
        tare_xv=0, tare_yv=0, tare_zv=0, tare_tv=0,
        Linear_x=1, Linear_y=1, Linear_z=1, Linear_t=1,
        tare_gx=0, tare_gy=0, tare_gz=0,
        Lc=0.1, Hl=0.1, Kl=0.05, CO2_id=None,
        auto_record_enabled=0, auto_pre_record_seconds=0,
        auto_record_seconds=0, auto_max_record_count=0,
        auto_cf_enabled=0, auto_fz_enabled=0, auto_t_enabled=0,
        auto_cf_threshold=0, auto_fz_threshold=0, auto_t_threshold=0,
    )


# ---------- reproduce GaussianFilter _do_filter algorithm --------------------
def gaussian_kernel(size, sigma=1.0):
    half = size // 2
    rng  = np.linspace(-half, half, size)
    k    = np.exp(-rng**2 / (2*sigma**2))
    return k / k.sum()

def filtered_first_sample(history_volts, new_volt):
    """Exact replica of applyTo(GaussianFilter) for first sample of new frame.

    We build the *full* 804-length combined array: 4 history volts + 800 new
    samples (new_volt followed by 799 zeros).  After filtering we take the
    element at index len(history_volts) which is what applyTo copies back to
    data[0]."""
    gf = GaussianFilter(WIN, SIGMA)

    combined = np.concatenate([history_volts, [new_volt] + [0.0]*(NPKT-1)])
    gf._do_filter(combined)
    return combined[len(history_volts)]


# ------------------------------------------------------------------  test
def test_decoderworker_gaussian_filter():
    dw = DecoderWorker(tooldata())
    dw.filter_data = {
        "filter_type": "Gaussianfilter_radio",
        "filter_values": WIN,
        "sampleRate": SR,
        "decimal_places": 3
    }

    # frame-1: voltages 0.2 0.3 0.4 0.5 V in final 4 hi-freq packets
    frame1 = frame_tail([0.2, 0.3, 0.4, 0.5])
    msB1, *_ = dw.socket_decoder(frame1)

    # frame-2: first usable packet = 1.0 V
    frame2 = frame_head([1.0])
    msB2, msBy2, msBxy2, msT2, msTor2, *_ = dw.socket_decoder(frame2)

    # ---------------- shape checks
    assert len(msB1) == len(msB2) == dw.SamplePoint1 == 800

    # ---------------- scaling check  (last sample still zero)
    assert msB2[-1] == 0.0

    # ---------------- expected first sample after Gaussian filtering
    hist_bx_volts = [row[0] for row in dw.decoded_high_freq_data_queue.get_all()]
    # Take a copy; apply filter in the same way as apply_filter does
    new_force = round(1.0 * dw.parm_N_X, 3)

    data_array = np.zeros(NPKT, dtype=float)
    data_array[0] = new_force
    gf = GaussianFilter(WIN, SIGMA)
    gf.applyTo(data_array, np.asarray(hist_bx_volts))
    force_expected = data_array[0]

    assert np.isclose(msB2[0], force_expected, rtol=1e-3), \
        f"Gaussian history wrong: got {msB2[0]:.6f}, expected {force_expected:.6f}"

    # ---------------- consistency BendingXY
    assert np.isclose(msBxy2[0], np.sqrt(msB2[0]**2 + msBy2[0]**2), rtol=1e-6)