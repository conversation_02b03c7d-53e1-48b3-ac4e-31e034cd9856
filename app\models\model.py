from app.models.database_model import DatabaseModel
from . import logger  # 從同一個包導入 logger

class Model:
    def __init__(self):
        logger.debug("Initializing Model")
        self.count = 0
        self.machDatabase = None  # 預設不建立資料庫連線
        self.machradar_tables = [
            {
                "table_name": "tool_magazine",
                "table_temp_name": "tool_magazine_temp",
                "ignore_fields": ["localtime"]
            },
            {
                "table_name": "machradar_co2",
                "table_temp_name": "machradar_co2_temp",
                "ignore_fields": ["localtime"]
            }
        ]
        

    def connect_db(self):
        """手動開啟資料庫連線"""
        if self.machDatabase is None:
            self.machDatabase = DatabaseModel()
            logger.debug("Database connection opened.")

    def close_db(self):
        """手動關閉資料庫連線"""
        if self.machDatabase:
            self.machDatabase.close_db()
            self.machDatabase = None
            logger.debug("Database connection closed.")

    def start_up(self):
        """軟體啟動時的處理"""
        logger.debug("軟體啟動：複製刀庫資料到暫存刀庫")
        self.connect_db()
        self.machDatabase.copy_table("tool_magazine", "tool_magazine_temp")
        logger.debug("軟體啟動：複製 CO2 資料到暫存 CO2 資料庫")
        self.machDatabase.copy_table("machradar_co2", "machradar_co2_temp")
        self.close_db()

    def compare_tables(self, table1_name, table2_name, ignore_fields):
        """比較兩個資料表的資料是否相同"""
        self.connect_db()
        is_same = self.machDatabase.compare_tables(table1_name, table2_name, ignore_fields)
        self.close_db()
        return is_same

    def compare_machradar_tables(self):
        all_same = True
        for table_info in self.machradar_tables:
            table_name = table_info["table_name"]
            table_temp_name = table_info["table_temp_name"]
            ignore_fields = table_info["ignore_fields"]
            if not self.compare_tables(table_name, table_temp_name, ignore_fields):
                all_same = False            
        return all_same

    def final_update(self, result):
        """軟體關閉時的處理"""
        self.connect_db()
        if result == True:
            logger.debug("軟體關閉：複製暫存刀庫資料到刀庫，並刪除暫存刀庫")
            self.machDatabase.copy_table("tool_magazine_temp", "tool_magazine")
            self.machDatabase.deleteTable("tool_magazine_temp")
            logger.debug("軟體關閉：複製暫存 CO2 資料到 CO2 資料庫，並刪除暫存 CO2 資料庫")
            self.machDatabase.copy_table("machradar_co2_temp", "machradar_co2")
            self.machDatabase.deleteTable("machradar_co2_temp")
        else:
            logger.info("軟體關閉：不儲存資料，刪除暫存刀庫和暫存 CO2 資料庫")
            self.machDatabase.deleteTable("tool_magazine_temp")
            self.machDatabase.deleteTable("machradar_co2_temp")
        self.close_db()

    # 取得資料庫刀把資料
    # FIXME: 先拿刀庫測試，再確認是否要改回 暫存刀褲來存取
    def get_tool_data(self):
        """DB 取得資料 資料庫取得暫存的刀把"""
        if not self.machDatabase:
            logger.error("Database connection is not open. Call connect_db() first.")
        return self.machDatabase.get_items("tool_magazine_temp")
    
    def get_co2_data(self, CO2_id):
        """DB 取得資料 資料庫取得暫存的刀把"""
        if not self.machDatabase:
            logger.error("Database connection is not open. Call connect_db() first.")
        return self.machDatabase.get_item("machradar_co2_temp",CO2_id)

    
    def get_machradar_set(self):
        """DB 取得資料 資料庫取得暫存的刀把"""
        if not self.machDatabase:
            logger.error("Database connection is not open. Call connect_db() first.")
        return self.machDatabase.get_items("machradar_setting")
    
    def update_tool_data(self, tool_data):
        """DB 更新資料 更新刀把資料"""

        if not self.machDatabase:
            logger.error("Database connection is not open. Call connect_db() first.")
        
        return self.machDatabase.post_update("tool_magazine_temp", update_values=tool_data, conditions={'toolip': tool_data['toolip']})


    def update_tare_data(self,current_data, tare_BendingX,tare_BendingY,tare_Tension,tare_Torsion):

        if not self.machDatabase:
            logger.error("Database connection is not open. Call connect_db() first.")
        # return self.machDatabase.update_items("tool_magazine_temp", tool_data)
        ToolHolderIP = current_data['toolip']
        tare_xv=tare_BendingX
        tare_yv=tare_BendingY
        tare_zv=tare_Tension 
        tare_tv=tare_Torsion 
        self.machDatabase.post_update('tool_magazine_temp',update_values={'tare_xv': tare_xv,'tare_yv': tare_yv,'tare_zv': tare_zv,'tare_tv': tare_tv},conditions={'toolip': ToolHolderIP})

        # machDatabase.close_db()
        logger.info("Updata success.")
    
    def update_tool_magazine_co2id(self,current_data,CO2_id):
        """DB 更新tool_magazine_co2_id資料"""
        ToolHolderIP = current_data['toolip']
        if not self.machDatabase:
            logger.error("Database connection is not open. Call connect_db() first.")
        return self.machDatabase.post_update("tool_magazine_temp",update_values={'CO2_id': CO2_id},conditions={'toolip': ToolHolderIP})

    def update_co2_data(self,CO2_id,CO2_data):
        """DB 更新資料 更新CO2資料"""
        # 提取 CO2_id 作為 item_id
        logger.debug(f"CO2_id: {CO2_id}")
        if CO2_id is not None:
            # 移除 CO2_id，因為它是主鍵，通常不應該更新
            updated_CO2_data = {k: v for k, v in CO2_data.items() if k != "CO2_id"}
            # logger.debug(f"updated_data: {updated_CO2_data}")
        if not self.machDatabase:
            logger.error("Database connection is not open. Call connect_db() first.")
        return self.machDatabase.post_update("machradar_co2_temp",update_values=updated_CO2_data,conditions={'id': CO2_id} )
    
    def insert_CO2_data(self,co2_data):
        """DB 新增資料 新增CO2資料"""
        if not self.machDatabase:
            logger.error("Database connection is not open. Call connect_db() first.")
        return self.machDatabase.insert_CO2_data_temp(co2_data)

    def update_file_path(self, file_path):
        """DB 更新資料 更新檔案路徑"""
        print("file_path",file_path)
        if not self.machDatabase:
            logger.error("Database connection is not open. Call connect_db() first.")
        return self.machDatabase.post_update("machradar_setting",update_values={'msra_file_path': file_path},conditions={'id': 1})
    # def increment(self):
    #     self.count += 1

    # def get_count(self):
    #     return self.count

    #   ```python
    #     resultsUpdate = machDatabase.post_update(
    #         'tool_setting',
    #         update_values={'bending_x': Tare_BX, 'bending_y': Tare_BY, 'tension': Tare_Tension, 'torsion': Tare_Torsion},
    #         conditions={'toolip': ToolHolderIP}
    #     )
    #     ```