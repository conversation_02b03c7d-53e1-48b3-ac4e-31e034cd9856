#!/usr/bin/env python3
"""
簡單的客戶端示例，用於連接到 SocketWorker Server
"""

import socket
import time
import threading
import random

class SimpleClient:
    def __init__(self, host='127.0.0.1', port=1333, client_name="TestClient"):
        self.host = host
        self.port = port
        self.client_name = client_name
        self.socket = None
        self.running = False
        
    def connect(self):
        """連接到服務器"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.connect((self.host, self.port))
            print(f"{self.client_name} 已連接到 {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"{self.client_name} 連接失敗: {e}")
            return False
    
    def disconnect(self):
        """斷開連接"""
        self.running = False
        if self.socket:
            try:
                self.socket.close()
                print(f"{self.client_name} 已斷開連接")
            except:
                pass
            self.socket = None
    
    def send_data(self, data):
        """發送數據"""
        if self.socket:
            try:
                if isinstance(data, str):
                    data = data.encode()
                self.socket.send(data)
                return True
            except Exception as e:
                print(f"{self.client_name} 發送數據失敗: {e}")
                return False
        return False
    
    def send_simulated_sensor_data(self, duration=30):
        """發送模擬的傳感器數據"""
        self.running = True
        start_time = time.time()
        packet_count = 0
        
        print(f"{self.client_name} 開始發送模擬傳感器數據，持續 {duration} 秒...")
        
        while self.running and (time.time() - start_time) < duration:
            try:
                # 模擬 32 字節的傳感器數據包
                # 這裡使用簡單的格式：時間戳 + 隨機數據
                timestamp = int(time.time() * 1000) % 0xFFFFFFFF
                
                # 模擬 BendingX, BendingY, Tension, Torsion 等數據
                bending_x = random.uniform(-10.0, 10.0)
                bending_y = random.uniform(-10.0, 10.0)
                tension = random.uniform(-50.0, 50.0)
                torsion = random.uniform(-20.0, 20.0)
                temperature = random.uniform(20.0, 80.0)
                
                # 簡單的數據包格式（實際應用中需要根據協議調整）
                data_packet = f"{timestamp:08X}{bending_x:+08.3f}{bending_y:+08.3f}{tension:+08.3f}{torsion:+08.3f}{temperature:06.2f}"
                
                # 確保數據包是 32 字節
                if len(data_packet) < 64:  # 32 bytes = 64 hex chars
                    data_packet = data_packet.ljust(64, '0')
                elif len(data_packet) > 64:
                    data_packet = data_packet[:64]
                
                if self.send_data(data_packet):
                    packet_count += 1
                    if packet_count % 100 == 0:
                        print(f"{self.client_name} 已發送 {packet_count} 個數據包")
                
                # 模擬 10kHz 採樣率（實際上會比較快）
                time.sleep(0.001)  # 1ms
                
            except Exception as e:
                print(f"{self.client_name} 發送數據時發生錯誤: {e}")
                break
        
        print(f"{self.client_name} 完成數據發送，總共發送 {packet_count} 個數據包")

def create_multiple_clients(num_clients=3, host='127.0.0.1', port=1333, duration=30):
    """創建多個客戶端進行測試"""
    clients = []
    threads = []
    
    print(f"創建 {num_clients} 個客戶端...")
    
    for i in range(num_clients):
        client = SimpleClient(host, port, f"Client_{i+1}")
        clients.append(client)
        
        if client.connect():
            # 為每個客戶端創建發送線程
            thread = threading.Thread(
                target=client.send_simulated_sensor_data,
                args=(duration,),
                daemon=True
            )
            threads.append(thread)
            thread.start()
            
            # 錯開啟動時間
            time.sleep(0.5)
    
    try:
        # 等待所有線程完成
        for thread in threads:
            thread.join()
    except KeyboardInterrupt:
        print("\n收到中斷信號，正在停止所有客戶端...")
        for client in clients:
            client.disconnect()

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='簡單客戶端測試工具')
    parser.add_argument('--host', default='127.0.0.1', help='服務器地址')
    parser.add_argument('--port', type=int, default=1333, help='服務器端口')
    parser.add_argument('--clients', type=int, default=1, help='客戶端數量')
    parser.add_argument('--duration', type=int, default=30, help='發送數據持續時間（秒）')
    parser.add_argument('--mode', choices=['single', 'multiple'], default='single',
                       help='運行模式: single(單客戶端), multiple(多客戶端)')
    
    args = parser.parse_args()
    
    if args.mode == 'single':
        # 單客戶端模式
        client = SimpleClient(args.host, args.port, "SingleClient")
        if client.connect():
            try:
                client.send_simulated_sensor_data(args.duration)
            except KeyboardInterrupt:
                print("\n收到中斷信號，正在停止...")
            finally:
                client.disconnect()
    else:
        # 多客戶端模式
        create_multiple_clients(args.clients, args.host, args.port, args.duration)

if __name__ == '__main__':
    main()
