import numpy as np
from typing import List, <PERSON><PERSON>, Dict
# from math import sqrt
# import config
from . import logger  # 從同一個包導入 logger

class TcpData:
    # def __init__(self,ip):
    def __init__(self):
        # self.toolHolder_IP=ip
        self.data = {}

        self.count_second=0
        self.sample_N=160800
        self.sample_index=160800//16  #資料每秒最多筆數

        self.MS_ADXL_X = None
        self.MS_ADXL_Y = None
        self.MS_ADXL_Z = None
        self.MS_BendingX = None
        self.MS_BendingY = None
        self.MS_BendingXY = None
        self.MS_Tension = None
        self.MS_Torsion = None
        self.Tare_GX = 0
        self.Tare_GY = 0
        self.Tare_GZ = 0
        self.txt_gx = 0
        self.AutoTare = True


    #---初始化---
    def init_MS_ADXL_Data(self,Sample_Point1):
        SamplePoint1=Sample_Point1
        SamplePoint2=SamplePoint1//200
        self.MS_ADXL_X = np.zeros(SamplePoint2) 
        self.MS_ADXL_Y = np.zeros(SamplePoint2) 
        self.MS_ADXL_Z = np.zeros(SamplePoint2) 
        self.MS_BendingX = np.zeros(SamplePoint1)
        self.MS_BendingY = np.zeros(SamplePoint1)
        self.MS_BendingXY = np.zeros(SamplePoint1)
        self.MS_Tension = np.zeros(SamplePoint1)
        self.MS_Torsion = np.zeros(SamplePoint1)

        self.MS_Temperature = np.zeros(SamplePoint2)


    # def updateTcpData(self, data,data_sample_N,Sample_Point1,toolSettingTable):
    def updateTcpData(self, data,data_sample_N,Sample_Point1):
        # 假設 data 是 JSON 字串或類似結構的數據
        # 資料整理 賦值 Start
        # dataList = toolSettingTable
        U_Tension= float(40.0)
        U_Torsion= float(40.0)
        Tare_Tension= float(0.036)
        Tare_Torsion= float(0.069)
        Tare_BX= float(5.508) # 5.508
        Tare_BY= float(0.013) # 0.013
        U_BX= float(30.0)
        U_BY= float(30.0)
        SamplePoint1=Sample_Point1
        SamplePoint2=SamplePoint1//200
        Collect_data=data
        sample_N=data_sample_N
        # 資料整理 賦值 End

        # sample_N=804
        Append_index=0 #塞正常資料的index

        if self.AutoTare and self.txt_gx != 0:
            self.Tare_GX=np.mean( self.MS_ADXL_X )
            self.Tare_GY=np.mean( self.MS_ADXL_Y )
            self.Tare_GZ=np.mean( self.MS_ADXL_Z )
            self.AutoTare=False  


        Wifi_RSSI = 0
        Charging_Flag_temp=0 #--判斷充電--
        sleep_mode_temp = None #--判斷睡眠模式--
        Data_battery = None
        co2_ten = None # 給CO2 判斷用
        Data_txt = ""

        '''
        Data_Temp=0
        Data_txt=""
        txt_gx=0
        global Wifi_RSSI
        Wifi_RSSI=0
        Charging_Flag_temp=0
        '''
        
        for i in range(sample_N):
            offset=i*32
        
            if Collect_data[0+offset:2+offset] == "f1":
                # print(Collect_data[0+offset:32+offset])
                logger.error(f"Collect_data: {Collect_data[0+offset:32+offset]}")
            elif Collect_data[0+offset:2+offset] == "f2": 
                # print(Collect_data[0+offset:32+offset])
                logger.error(f"Collect_data: {Collect_data[0+offset:32+offset]}")
            elif Collect_data[0+offset:2+offset] == "f3": 
                # print(Collect_data[0+offset:32+offset])
                logger.error(f"Collect_data: {Collect_data[0+offset:32+offset]}")
            elif Collect_data[0+offset:2+offset] == "f4": 
                # print(Collect_data[0+offset:32+offset])
                logger.error(f"Collect_data: {Collect_data[0+offset:32+offset]}")
            else:  #正常1+200
                if i%201==0 :  #第一筆放其他資訊的資料  
                
                    Data_ADXL_X = int(Collect_data[0+offset:4+offset],16)  
                    Data_ADXL_Y = int(Collect_data[4+offset:8+offset],16)
                    Data_ADXL_Z = int(Collect_data[8+offset:12+offset],16)
                    Data_Temp = int( ( Collect_data[14+offset:16+offset] + Collect_data[12+offset:14+offset] ) ,16) /128 #溫度，高低位元有錯位
                    Wifi_RSSI = int(Collect_data[16+offset:18+offset],16)
                    

                    self.txt_gx = round( (Data_ADXL_X - self.Tare_GX) ,3)  #/10000
                    txt_gy = round( (Data_ADXL_Y - self.Tare_GY) ,3)
                    txt_gz = round( (Data_ADXL_Z - self.Tare_GZ) ,3)
                    index = i//201
                
                
                    # print(len(MS_ADXL_X),index)
                
                    self.MS_ADXL_X[index] = self.txt_gx
                    self.MS_ADXL_Y[index] = txt_gy
                    self.MS_ADXL_Z[index] = txt_gz
                    
                    #解決溫度異常的讀取，採取不讀維持上一筆
                    if 125>Data_Temp:  #DS620溫度感測範圍-55~125
                        self.MS_Temperature[index] = Data_Temp
                    
                    # 這裡儲存的是要寫入 文字檔的地方
                    Data_txt += "*," + str('{:.3f}'.format(self.txt_gx)) + ',' + str('{:.3f}'.format(txt_gy)) + ',' + str('{:.3f}'.format(txt_gz)) + ',' + str('{:.2f}'.format(Data_Temp)) + ',' + str(Wifi_RSSI) + '\n'
                    
                    #--判斷充電-- {"0": "未充電","1": "充電中"}
                    Charging_Flag_temp = int(Collect_data[19+offset:20+offset],16)  
                    
                    
                    #--判斷睡眠模式-- {"b": "沒睡","1": "睡了"}
                    sleep_mode_temp = bin(int(Collect_data[19+offset:20+offset]))[-2]
                    

                    #--讀MAC--
                    MAC = Collect_data[20+offset:32+offset]
                    # print("MAC:  ", MAC )

                
                else:  #其他放bending的資料
                  

                    Data_x= int(Collect_data[0+offset:4+offset],16) /6553.5 
                    Data_y= int(Collect_data[4+offset:8+offset],16) /6553.5   
                    Data_ten= int(Collect_data[8+offset:12+offset],16) /6553.5   
                    Data_tor= int(Collect_data[12+offset:16+offset],16) /6553.5   
                    Data_battery=round(int(Collect_data[16+offset:20+offset],16) /6553.5 ,3)
            
                    #電壓資料
                    Data_x = Data_x
                    Data_y = Data_y
                    Data_ten = Data_ten
                    Data_tor = Data_tor
                      
                    #Tare後的單位轉換資料儲存  
                    txt_bx = round( (Data_x *1.33 - Tare_BX) * U_BX,3)
                    txt_by = round( (Data_y *1.33 - Tare_BY) * U_BY,3)
                    txt_ten = round( (Data_ten *3.77 - Tare_Tension) * U_Tension,3)  
                    txt_tor = round( (Data_tor *7.33 - Tare_Torsion) * U_Torsion,3)
                    
                    
                    self.MS_BendingX[Append_index] = txt_bx   #index -198 是因為i從2開始算，且資料是201後面的200開始算
                    self.MS_BendingY[Append_index] = txt_by
                    self.MS_BendingXY[Append_index] = np.sqrt(txt_bx**2 + txt_by**2)
                    self.MS_Tension[Append_index] = txt_ten
                    self.MS_Torsion[Append_index] = txt_tor
                    
                    # 這裡儲存的是要寫入 文字檔的地方
                    # Data_txt += str('{:.3f}'.format(self.MS_BendingX[Append_index])) + ', ' + str('{:.3f}'.format(self.MS_BendingY[Append_index])) + ', ' + str('{:.3f}'.format(self.MS_Tension[Append_index])) + ', ' + str('{:.3f}'.format(self.MS_Torsion[Append_index])) + ', ' + str('{:.3f}'.format(Data_battery)) + '\n'

                    # #---計算CO2 g---
                    co2_ten = txt_ten
                    # caculate_co2_g(txt_ten)
        
                    Append_index+=1
       
        return self.MS_BendingX, self.MS_BendingY, self.MS_BendingXY, self.MS_Tension, self.MS_Torsion, Data_battery, Wifi_RSSI, Charging_Flag_temp, sleep_mode_temp, co2_ten,self.MS_Temperature,self.MS_ADXL_X,self.MS_ADXL_Y,self.MS_ADXL_Z,SamplePoint2,self.Tare_GX,self.Tare_GY,self.Tare_GZ,Data_txt


    def getTcpFileData(self) -> Dict[str, List[str]]:
        return self.data

    def loadTcpData(self, file_path):
        # 載入檔案資料的邏輯
        pass