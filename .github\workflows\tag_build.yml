# .github/workflows/tag_build.yml
name: tag_build

on:
  workflow_dispatch:
    inputs:
      branch:
          description: "Branch to tag"
          type: choice
          required: true
          default: "master"
          options:
            - master
            - chore/git_action_release
      tag:
        description: "Tag or version (e.g., v2.0.801.121)"
        required: true
        default: "v0.0.0.1"
      publish_release:
        description: "Create Draft Release(With EXE)"
        type: boolean
        required: true
        default: true

permissions:
  contents: write
  
jobs:
  build:
    runs-on: windows-latest

    steps:
    - name: Checkout selected branch
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.inputs.branch }}
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Ensure tag exists (create if missing)
      shell: pwsh
      run: |
        git fetch --tags --prune origin
        $tag = "${{ github.event.inputs.tag }}"
        $currentCommit = git rev-parse HEAD
        
        # Find all tags pointing to the current commit
        $existingTags = git tag --points-at $currentCommit
        
        if ($existingTags -match $tag) {
          Write-Output "✅ Tag $tag already exists at the current commit."
          Write-Output "   Current commit: $currentCommit"
          Write-Output "   Tags on commit: $existingTags"
          exit 0
        } elseif ($existingTags) {
          Write-Output "⚠️ Commit $currentCommit already has tag(s): $existingTags"
          Write-Output "   Skipping creation of new tag $tag (one tag per commit policy)."
          exit 0
        } elseif (git show-ref --tags --quiet "refs/tags/$tag") {
          $tagCommit = git rev-list -n 1 $tag
          Write-Error "❌ Tag $tag already exists, but on a different commit ($tagCommit). Aborting."
          exit 1
        } else {
          git config user.name "github-actions"
          git config user.email "<EMAIL>"
          git tag $tag
          git push origin $tag
          Write-Output "✅ Created and pushed new tag $tag at commit $currentCommit."
        }

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.8'
        architecture: 'x64'

    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install PyOpenGL_accelerate-3.1.5-cp38-cp38-win_amd64.whl
        pip install PyOpenGL-3.1.5-cp38-cp38-win_amd64.whl

    - name: Prepare version variables
      shell: pwsh
      run: |
        $tag = "${{ github.event.inputs.tag }}"
        $version = $tag -replace '^v', ''
        $numbers = $version -split '\.'
        $cleanedNumbers = $numbers | ForEach-Object { [int]$_ }
        $cleanedVersion = ($cleanedNumbers -join '.')
        echo "TAG=$version" | Out-File -FilePath $env:GITHUB_ENV -Encoding utf8 -Append
        $versiondot = $cleanedVersion -replace '\.', ','
        echo "TAGDOT=$versiondot" | Out-File -FilePath $env:GITHUB_ENV -Encoding utf8 -Append
        $year = (Get-Date).Year
        echo "YEAR=$year" | Out-File -FilePath $env:GITHUB_ENV -Encoding utf8 -Append
        Write-Output "Using tag: $tag"

    - name: Generate version_info.version
      shell: pwsh
      run: |
        @"
        VSVersionInfo(
            ffi=FixedFileInfo(
                filevers=(${env:TAGDOT}, 0),
                prodvers=(${env:TAGDOT}, 0),
                mask=0x3f,
                flags=0x0,
                OS=0x40004,
                fileType=0x1,
                subtype=0x0,
                date=(0, 0)
            ),
            kids=[
                StringFileInfo(
                    [
                        StringTable(
                            u'040904B0',
                            [StringStruct(u'CompanyName', u'Machsync'),
                             StringStruct(u'FileDescription', u'MachRadarPro_RealTime RC'),
                             StringStruct(u'FileVersion', u'${env:TAG}'),
                             StringStruct(u'InternalName', u'MachRadarPro_RealTime'),
                             StringStruct(u'LegalCopyright', u'Machsync© 2018-${env:YEAR} All right reserved.'),
                             StringStruct(u'OriginalFilename', u'MachRadarPro_RealTime.exe'),
                             StringStruct(u'ProductName', u'MachRadar®Pro'),
                             StringStruct(u'ProductVersion', u'${env:TAG}')])
                    ]),
                VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
            ]
        )
        "@ | Out-File version_info.version -Encoding utf8
        Write-Output "Generated version_info.version:"
        type version_info.version

    - name: Inject version into main.py
      shell: pwsh
      run: |
        $tag = "${{ github.event.inputs.tag }}"
        (Get-Content main.py) -replace '\{VERSION\}', $tag | Set-Content main.py
        Write-Output "Injected version $tag into main.py"

    - name: Build EXE with PyInstaller
      run: |
        pyinstaller -w -F -i .\system_image\machradarpro.ico --version-file=version_info.version --name MachRadarPro_RealTime main.py

    - name: Create/Update Draft Release
      if: ${{ github.event.inputs.publish_release == 'true' }}
      uses: softprops/action-gh-release@v2
      with:
        tag_name: ${{ github.event.inputs.tag }}
        name: "*** Release version *** ${{ github.event.inputs.tag }}"
        draft: true
        files: ./dist/MachRadarPro_RealTime.exe
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
