# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'Event_window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

import system_image.system_image_rc

class Ui_Event_window(object):
    def setupUi(self, Event_window):
        if not Event_window.objectName():
            Event_window.setObjectName(u"Event_window")
        Event_window.resize(800, 600)
        Event_window.setMinimumSize(QSize(800, 600))
        Event_window.setMaximumSize(QSize(800, 600))
        Event_window.setStyleSheet(u"QWidget#Event_window{\n"
"background-color: #0C0C44;\n"
"border: 2px solid #5448B6;\n"
"}")
        self.verticalLayout = QVBoxLayout(Event_window)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.CO2_title1_label = QLabel(Event_window)
        self.CO2_title1_label.setObjectName(u"CO2_title1_label")
        font = QFont()
        font.setFamily(u"Arial Black")
        font.setPointSize(20)
        self.CO2_title1_label.setFont(font)
        self.CO2_title1_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_title1_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_title1_label.setAlignment(Qt.AlignCenter)
        self.CO2_title1_label.setMargin(2)

        self.horizontalLayout.addWidget(self.CO2_title1_label)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer)

        self.close_button = QPushButton(Event_window)
        self.close_button.setObjectName(u"close_button")
        font1 = QFont()
        font1.setPointSize(20)
        self.close_button.setFont(font1)
        self.close_button.setAutoFillBackground(False)
        self.close_button.setStyleSheet(u"QPushButton {\n"
"	image: url(:/btn_close/btn_close_0.png);\n"
"border:none;\n"
"}\n"
"\n"
"QPushButton:hover{\n"
"	image: url(:/btn_close/btn_close_1.png);\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"	image: url(:/btn_close/btn_close_2.png);\n"
"}")
        self.close_button.setIconSize(QSize(30, 30))
        self.close_button.setCheckable(True)
        self.close_button.setAutoDefault(False)
        self.close_button.setFlat(False)

        self.horizontalLayout.addWidget(self.close_button)


        self.verticalLayout.addLayout(self.horizontalLayout)

        self.tableWidget = QTableWidget(Event_window)
        if (self.tableWidget.columnCount() < 3):
            self.tableWidget.setColumnCount(3)
        __qtablewidgetitem = QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        if (self.tableWidget.rowCount() < 5):
            self.tableWidget.setRowCount(5)
        __qtablewidgetitem3 = QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(0, __qtablewidgetitem3)
        __qtablewidgetitem4 = QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(1, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(2, __qtablewidgetitem5)
        __qtablewidgetitem6 = QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(3, __qtablewidgetitem6)
        __qtablewidgetitem7 = QTableWidgetItem()
        self.tableWidget.setVerticalHeaderItem(4, __qtablewidgetitem7)
        __qtablewidgetitem8 = QTableWidgetItem()
        self.tableWidget.setItem(0, 0, __qtablewidgetitem8)
        __qtablewidgetitem9 = QTableWidgetItem()
        self.tableWidget.setItem(0, 1, __qtablewidgetitem9)
        __qtablewidgetitem10 = QTableWidgetItem()
        self.tableWidget.setItem(0, 2, __qtablewidgetitem10)
        self.tableWidget.setObjectName(u"tableWidget")
        self.tableWidget.setAutoFillBackground(True)
        self.tableWidget.setStyleSheet(u"QWidget {\n"
"    background-color: #0C0C44;\n"
"}\n"
"\n"
"QTableWidget {\n"
"    background-color:  #0C0C44;  /* \u6df1\u85cd\u8272\u80cc\u666f */\n"
"    color: white;  /* \u6587\u5b57\u984f\u8272\u767d\u8272 */\n"
"    font-size: 18px;\n"
"    font-weight: bold;\n"
"    border: none;  /* \u79fb\u9664\u908a\u6846 */\n"
"    gridline-color: transparent;  /* \u79fb\u9664\u7db2\u683c\u7dda */\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    background-color:  #0C0C44;  /* \u8868\u982d\u80cc\u666f\u984f\u8272 */\n"
"    color: white;\n"
"    font-size: 18px;\n"
"    font-weight: bold;\n"
"    border: none;  /* \u79fb\u9664\u8868\u982d\u908a\u6846 */\n"
"    padding: 5px;\n"
"gridline-color: transparent;  /* \u79fb\u9664\u7db2\u683c\u7dda */\n"
"}\n"
"\n"
"QTableWidget::item {\n"
"background-color: #0C0C44;\n"
"    padding: 10px;\n"
"    border: none;\n"
"gridline-color: transparent;  /* \u79fb\u9664\u7db2\u683c\u7dda */\n"
"}\n"
"\n"
"QTableWidget::item:selected {\n"
"    background-color:  #0C0C44;  /* \u9078\u64c7"
                        "\u6642\u7684\u80cc\u666f\u984f\u8272 */\n"
"gridline-color: transparent;  /* \u79fb\u9664\u7db2\u683c\u7dda */\n"
"}")
        self.tableWidget.setDragEnabled(False)

        self.verticalLayout.addWidget(self.tableWidget)


        self.retranslateUi(Event_window)

        self.close_button.setDefault(False)


        QMetaObject.connectSlotsByName(Event_window)
    # setupUi

    def retranslateUi(self, Event_window):
        Event_window.setWindowTitle(QCoreApplication.translate("Event_window", u"Form", None))
        self.CO2_title1_label.setText(QCoreApplication.translate("Event_window", u"\u4e8b\u4ef6\u8a18\u9304", None))
        self.close_button.setText("")
#if QT_CONFIG(shortcut)
        self.close_button.setShortcut("")
#endif // QT_CONFIG(shortcut)
        ___qtablewidgetitem = self.tableWidget.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("Event_window", u"\u6642\u9593", None));
        ___qtablewidgetitem1 = self.tableWidget.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("Event_window", u"\u8cc7\u8a0a", None));
        ___qtablewidgetitem2 = self.tableWidget.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("Event_window", u"\u5200\u628a\u540d\u7a31", None));
        ___qtablewidgetitem3 = self.tableWidget.verticalHeaderItem(0)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("Event_window", u"1", None));
        ___qtablewidgetitem4 = self.tableWidget.verticalHeaderItem(1)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("Event_window", u"2", None));
        ___qtablewidgetitem5 = self.tableWidget.verticalHeaderItem(2)
        ___qtablewidgetitem5.setText(QCoreApplication.translate("Event_window", u"3", None));
        ___qtablewidgetitem6 = self.tableWidget.verticalHeaderItem(3)
        ___qtablewidgetitem6.setText(QCoreApplication.translate("Event_window", u"4", None));
        ___qtablewidgetitem7 = self.tableWidget.verticalHeaderItem(4)
        ___qtablewidgetitem7.setText(QCoreApplication.translate("Event_window", u"5", None));

        __sortingEnabled = self.tableWidget.isSortingEnabled()
        self.tableWidget.setSortingEnabled(False)
        ___qtablewidgetitem8 = self.tableWidget.item(0, 0)
        ___qtablewidgetitem8.setText(QCoreApplication.translate("Event_window", u"test", None));
        ___qtablewidgetitem9 = self.tableWidget.item(0, 1)
        ___qtablewidgetitem9.setText(QCoreApplication.translate("Event_window", u"test", None));
        ___qtablewidgetitem10 = self.tableWidget.item(0, 2)
        ___qtablewidgetitem10.setText(QCoreApplication.translate("Event_window", u"test", None));
        self.tableWidget.setSortingEnabled(__sortingEnabled)

    # retranslateUi

