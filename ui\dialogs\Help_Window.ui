<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Help_Window</class>
 <widget class="QWidget" name="Help_Window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>700</width>
    <height>1000</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <family>Arial</family>
    <weight>75</weight>
    <bold>true</bold>
   </font>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">background-color: #0C0C44;
border:none</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QVBoxLayout" name="main_layout" stretch="5,95">
     <property name="sizeConstraint">
      <enum>QLayout::SetNoConstraint</enum>
     </property>
     <item>
      <widget class="QWidget" name="title_widget" native="true">
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <layout class="QHBoxLayout" name="title_layout" stretch="0,100,1">
          <property name="rightMargin">
           <number>5</number>
          </property>
          <item>
           <widget class="QLabel" name="title_label">
            <property name="font">
             <font>
              <family>Arial Black</family>
              <pointsize>18</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string>操作說明</string>
            </property>
            <property name="textFormat">
             <enum>Qt::AutoText</enum>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_5">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="close_button">
            <property name="font">
             <font>
              <family>PMingLiU</family>
              <pointsize>20</pointsize>
              <weight>50</weight>
              <bold>false</bold>
             </font>
            </property>
            <property name="autoFillBackground">
             <bool>false</bool>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton {
    background: url(:/btn_close/btn_close_0.png) no-repeat right center;
    border: none;
}

QPushButton:hover {
    background: url(:/btn_close/btn_close_1.png) no-repeat right center;
}

QPushButton:pressed {
    background: url(:/btn_close/btn_close_2.png) no-repeat right center;
}</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="iconSize">
             <size>
              <width>30</width>
              <height>30</height>
             </size>
            </property>
            <property name="shortcut">
             <string/>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
            <property name="autoDefault">
             <bool>false</bool>
            </property>
            <property name="default">
             <bool>false</bool>
            </property>
            <property name="flat">
             <bool>false</bool>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QWidget" name="pdf_widget" native="true">
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <item>
         <layout class="QVBoxLayout" name="pdf_layout"/>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../system_image/system_image.qrc"/>
 </resources>
 <connections/>
</ui>
