# Socket Server 架構修改說明

## 概述

根據 `server_demo.py` 的架構，將 `socket_work.py` 從 client 模式改造為 server 模式，並同步修正了 `controller.py`。

## 主要修改

### 1. SocketWorker 類別修改 (`utils/socket_work.py`)

#### 新增功能：
- **Server 模式**: 從 client 連接模式改為 server 監聽模式
- **多客戶端支持**: 可同時處理多個客戶端連接（預設最大5個）
- **活躍客戶端管理**: 支持設置和切換活躍客戶端
- **客戶端管理**: 提供客戶端連接/斷開的管理功能

#### 新增屬性：
```python
self.host = '0.0.0.0'  # 監聽所有接口
self.max_clients = 5   # 最大客戶端數量
self.server_socket = None  # Server socket
self.client_sockets: Dict[str, socket.socket] = {}  # 客戶端連接字典
self.client_threads: Dict[str, threading.Thread] = {}  # 客戶端處理線程
self.active_client = None  # 當前活躍的客戶端
```

#### 新增信號：
```python
sig_client_connected = Signal(str)     # 客戶端連接信號
sig_client_disconnected = Signal(str)  # 客戶端斷開信號
```

#### 新增方法：
- `set_active_client(client_id)`: 設置活躍客戶端
- `get_connected_clients()`: 獲取已連接的客戶端列表
- `disconnect_client(client_id)`: 斷開指定客戶端
- `send_sleepmode(enable)`: 向活躍客戶端發送睡眠模式命令
- `send_command_to_client(client_id, command)`: 向指定客戶端發送命令
- `broadcast_command(command)`: 向所有客戶端廣播命令
- `_start_server()`: 啟動 Server
- `_handle_client(client_socket, client_id)`: 處理單個客戶端
- `_cleanup_server()`: 清理 Server 資源

### 2. Controller 類別修改 (`app/controllers/controller.py`)

#### 修改的方法：

**`get_current_data()`**:
- 移除對特定 `toolholder_ip` 的依賴
- 改為使用固定的 server 端口配置
- 新增客戶端連接信號的綁定

**新增信號處理方法**:
- `server_status_changed(data)`: 處理 Server 狀態變化
- `client_connected(client_id)`: 處理客戶端連接事件
- `client_disconnected(client_id)`: 處理客戶端斷開事件

**修改現有方法**:
- 將所有 `socker_disconnection` 重命名為 `server_status_changed`
- 更新日誌訊息，將 "連線" 改為 "Server"
- 保持 `send_sleepmode` 功能的兼容性

## 架構變化

### 原架構 (Client 模式)
```
Controller -> SocketWorker (Client) -> Remote Server
```

### 新架構 (Server 模式)
```
Controller -> SocketWorker (Server) <- Multiple Clients
                    |
                    v
            Active Client Management
```

## 使用方式

### 1. 啟動 Server
```python
# 在 Controller 中，SocketWorker 會自動以 server 模式啟動
socket_worker = SocketWorker(host='0.0.0.0', port=1333, max_clients=5)
```

### 2. 客戶端連接
客戶端需要連接到運行此程序的機器的 1333 端口：
```python
# 客戶端代碼示例
import socket
client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
client.connect(('SERVER_IP', 1333))
```

### 3. 測試工具
提供了 `utils/test_socket_server.py` 測試腳本：

```bash
# 僅啟動 server
python test_socket_server.py --mode server

# 僅啟動客戶端（需要 server 已運行）
python test_socket_server.py --mode client --clients 3

# 同時啟動 server 和客戶端
python test_socket_server.py --mode both --clients 2
```

## 兼容性

### 保持兼容的功能：
- `raw_data_received` 信號
- `sig_socket_connect` 信號
- `pause()` 和 `resume()` 方法
- `stop()` 方法
- `send_sleepmode()` 方法

### 新增的功能：
- 多客戶端管理
- 活躍客戶端切換
- 客戶端狀態監控
- 命令廣播功能

## 注意事項

1. **端口配置**: Server 預設監聽 1333 端口，可在 Controller 中修改
2. **客戶端數量**: 預設最大支持 5 個客戶端，可在初始化時調整
3. **活躍客戶端**: 只有活躍客戶端的數據會被處理，其他客戶端的數據會被忽略
4. **向後兼容**: 保持了原有的信號和方法接口，確保現有代碼正常運行

## 測試建議

1. 使用提供的測試腳本驗證基本功能
2. 測試多客戶端同時連接的情況
3. 測試客戶端斷開重連的穩定性
4. 驗證活躍客戶端切換功能
5. 測試睡眠模式命令的發送

## 未來擴展

1. 可以添加客戶端認證機制
2. 可以實現更複雜的數據路由邏輯
3. 可以添加客戶端優先級管理
4. 可以實現數據緩存和重發機制
