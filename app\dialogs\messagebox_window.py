from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
from .message_box import Ui_Dialog  

class Ui_message_box_Window(QDialog, Ui_Dialog):
    def __init__(self, message, timeout=2000, parent=None):
        """
        message: 要顯示的訊息
        timeout: 視窗顯示的時間 (預設 2000 毫秒 = 2 秒)
        """
        super().__init__(parent)
        self.setupUi(self)

        # 設定無邊框
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)

        # 設定訊息內容
        self.set_message(message)

        # 移除所有按鈕
        self.remove_buttons()

        # 設定定時關閉
        QTimer.singleShot(timeout, self.close)  # 指定時間後關閉視窗

    def set_message(self, message):
        """更新對話框的訊息"""
        self.message.setText(message)

    def remove_buttons(self):
        """移除對話框內的按鈕"""
        try:
            self.btv_yes_btn.deleteLater()
            self.btv_yes_btn.setParent(None)
        except AttributeError:
            pass

        try:
            self.btn_no_btn.deleteLater()
            self.btn_no_btn.setParent(None)
        except AttributeError:
            pass
