<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>main_window</class>
 <widget class="QWidget" name="main_window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1280</width>
    <height>800</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>800</width>
    <height>600</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MachRadarPro</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../system_image/system_image.qrc">
    <normaloff>:/other/machradarpro.ico</normaloff>:/other/machradarpro.ico</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#main_window {
	background-color: rgb(0, 0, 0);
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="0,2,1,0">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="Top_frame">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>49</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">QFrame#Top_frame {
	background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1, stop:0 rgba(0, 0, 127, 141), stop:1 rgba(0, 0, 0, 72));
}</string>
     </property>
     <layout class="QHBoxLayout" name="TOPH_layout" stretch="10,15,10">
      <item>
       <layout class="QVBoxLayout" name="TOP1V_layout_" stretch="1,5,2">
        <item>
         <spacer name="verticalSpacer_4">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QHBoxLayout" name="TOP1H_layout_2">
          <item>
           <widget class="QPushButton" name="Btn_Setting">
            <property name="font">
             <font>
              <family>Arial Black</family>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { border: none; color: #7AFEC6; }</string>
            </property>
            <property name="text">
             <string>設定</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="Btn_Filter">
            <property name="font">
             <font>
              <family>Arial Black</family>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { border: none; color: #7AFEC6; }</string>
            </property>
            <property name="text">
             <string>濾波</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="Btn_View">
            <property name="font">
             <font>
              <family>Arial Black</family>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { border: none; color: #7AFEC6; }</string>
            </property>
            <property name="text">
             <string>顯示</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="Btn_Event">
            <property name="font">
             <font>
              <family>Arial Black</family>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { border: none; color: #7AFEC6; }</string>
            </property>
            <property name="text">
             <string>事件</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="Btn_Mode">
            <property name="font">
             <font>
              <family>Arial Black</family>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { border: none; color: #7AFEC6; }</string>
            </property>
            <property name="text">
             <string>模式</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_5">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="Btn_RecordScript">
            <property name="font">
             <font>
              <family>Arial Black</family>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { border: none; color: #7AFEC6; }</string>
            </property>
            <property name="text">
             <string>腳本</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_6">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="Btn_Help">
            <property name="font">
             <font>
              <family>Arial Black</family>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { border: none; color: #7AFEC6; }</string>
            </property>
            <property name="text">
             <string>說明</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_7">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="lable">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string>|</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_8">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="Btn_Toolname">
            <property name="font">
             <font>
              <family>Arial Black</family>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { border: none; color: #7AFEC6; }</string>
            </property>
            <property name="text">
             <string>&lt;智慧刀把&gt;</string>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer_3">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="horizontalSpacer_9">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QVBoxLayout" name="TOP2V_layout" stretch="1,5,2">
        <item>
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <layout class="QHBoxLayout" name="TOP2H_layout" stretch="1,1,1,1,1,1,1,1,3">
          <item>
           <widget class="QLabel" name="Label_ECO">
            <property name="font">
             <font>
              <family>Arial Black</family>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string>0.0 g CO2e</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_12">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QPushButton" name="Btn_CO2">
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { image: url(:/icon_co2/icon_co2_1.png); }</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="icon">
             <iconset resource="../system_image/system_image.qrc">
              <normaloff>:/icon_co2/icon_co2_0.png</normaloff>
              <normalon>:/icon_co2/icon_co2_0.png</normalon>
              <disabledoff>:/icon_co2/icon_co2_2.png</disabledoff>
              <disabledon>:/icon_co2/icon_co2_0.png</disabledon>
              <activeoff>:/icon_co2/icon_co2_0.png</activeoff>
              <activeon>:/icon_co2/icon_co2_0.png</activeon>
              <selectedoff>:/icon_co2/icon_co2_0.png</selectedoff>
              <selectedon>:/icon_co2/icon_co2_0.png</selectedon>:/icon_co2/icon_co2_0.png</iconset>
            </property>
            <property name="iconSize">
             <size>
              <width>40</width>
              <height>30</height>
             </size>
            </property>
            <property name="checkable">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_10">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="wifi_rssi">
            <property name="font">
             <font>
              <family>Arial Black</family>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string>-0dBm</string>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_13">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="WIFI">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>41</width>
              <height>31</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>50</width>
              <height>50</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap resource="../system_image/system_image.qrc">:/wifi/wifi_0.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_14">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="Battery">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>51</width>
              <height>31</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>60</width>
              <height>50</height>
             </size>
            </property>
            <property name="font">
             <font>
              <pointsize>12</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: rgb(255, 255, 255);</string>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="pixmap">
             <pixmap resource="../system_image/system_image.qrc">:/battery/battery_0.png</pixmap>
            </property>
            <property name="scaledContents">
             <bool>true</bool>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <spacer name="verticalSpacer_2">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="middle_layout" stretch="2,12,14">
     <item>
      <layout class="QVBoxLayout" name="Funtion_layout" stretch="1,0,1,0,5,0">
       <item>
        <spacer name="verticalSpacer_9">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="Btn_Link">
         <property name="font">
          <font>
           <pointsize>12</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="../system_image/system_image.qrc">
           <normaloff>:/btn_link/btn_link_0.png</normaloff>
           <normalon>:/btn_link/btn_link_1.png</normalon>
           <disabledoff>:/btn_link/btn_link_0.png</disabledoff>
           <disabledon>:/btn_link/btn_link_0.png</disabledon>
           <activeoff>:/btn_link/btn_link_0.png</activeoff>
           <activeon>:/btn_link/btn_link_1.png</activeon>
           <selectedoff>:/btn_link/btn_link_0.png</selectedoff>
           <selectedon>:/btn_link/btn_link_1.png</selectedon>:/btn_link/btn_link_0.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>50</width>
           <height>50</height>
          </size>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_8">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="Btn_Record">
         <property name="font">
          <font>
           <pointsize>12</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="../system_image/system_image.qrc">
           <normaloff>:/btn_record/btn_record_s0_0.png</normaloff>
           <normalon>:/btn_record/btn_record_s1_0.png</normalon>
           <disabledoff>:/btn_record/btn_record_s0_0.png</disabledoff>
           <disabledon>:/btn_record/btn_record_s0_0.png</disabledon>
           <activeoff>:/btn_record/btn_record_s0_0.png</activeoff>
           <activeon>:/btn_record/btn_record_s1_0.png</activeon>
           <selectedoff>:/btn_record/btn_record_s0_1.png</selectedoff>
           <selectedon>:/btn_record/btn_record_s1_1.png</selectedon>:/btn_record/btn_record_s0_0.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>50</width>
           <height>50</height>
          </size>
         </property>
         <property name="checkable">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer_5">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QScrollArea" name="scrollArea">
         <property name="autoFillBackground">
          <bool>true</bool>
         </property>
         <property name="styleSheet">
          <string notr="true">QScrollArea {background: black; border: none;}
QScrollBar:vertical {
                background: black;
                width: 2px;
                border: none;
                margin: 0px 0px 0px 0px;
            }
QScrollBar::handle:vertical {
                background: #7AFEC6;
                min-height: 20px;
                border-radius: 3px;
            }
QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                background: none;
                height: 0px;
           }
QScrollBar:horizontal {
                background: black;
                height: 2px;
                border: none;
                margin: 0px 0px 0px 0px;
            }
QScrollBar::handle:horizontal {
                background: #7AFEC6;
                min-width: 20px;
                border-radius: 3px;
            }
QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                background: none;
                width: 0px;
            }</string>
         </property>
         <property name="widgetResizable">
          <bool>true</bool>
         </property>
         <property name="alignment">
          <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
         </property>
         <widget class="QWidget" name="scrollAreaWidgetContents">
          <property name="geometry">
           <rect>
            <x>0</x>
            <y>0</y>
            <width>86</width>
            <height>460</height>
           </rect>
          </property>
          <property name="minimumSize">
           <size>
            <width>54</width>
            <height>0</height>
           </size>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="styleSheet">
           <string notr="true">border: none;</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_4">
           <item>
            <widget class="QPushButton" name="Btn1_Tare">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="font">
              <font>
               <pointsize>12</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { image: url(:/bnt_vp_tare/btn_vp_tare_1.png); }</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="icon">
              <iconset resource="../system_image/system_image.qrc">
               <normaloff>:/bnt_vp_tare/btn_vp_tare_2.png</normaloff>
               <normalon>:/bnt_vp_tare/btn_vp_tare_2.png</normalon>
               <disabledoff>:/bnt_vp_tare/bnt_vp_cant_tare.png</disabledoff>
               <disabledon>:/bnt_vp_tare/bnt_vp_cant_tare.png</disabledon>
               <activeoff>:/bnt_vp_tare/btn_vp_tare_2.png</activeoff>
               <activeon>:/bnt_vp_tare/btn_vp_tare_2.png</activeon>
               <selectedoff>:/bnt_vp_tare/btn_vp_tare_0.png</selectedoff>
               <selectedon>:/bnt_vp_tare/btn_vp_tare_1.png</selectedon>:/bnt_vp_tare/btn_vp_tare_2.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
             <property name="autoDefault">
              <bool>true</bool>
             </property>
             <property name="default">
              <bool>false</bool>
             </property>
             <property name="flat">
              <bool>false</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="Btn2_BGMode">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="font">
              <font>
               <pointsize>12</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { image: url(:/btn_vp_bgmode/btn_vp_bgmode_1.png); }</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="icon">
              <iconset resource="../system_image/system_image.qrc">
               <normaloff>:/btn_vp_bgmode/btn_vp_bgmode_0.png</normaloff>
               <normalon>:/btn_vp_bgmode/btn_vp_bgmode_0.png</normalon>
               <disabledoff>:/btn_vp_bgmode/btn_vp_bgmode_2.png</disabledoff>
               <disabledon>:/btn_vp_bgmode/btn_vp_bgmode_2.png</disabledon>
               <activeoff>:/btn_vp_bgmode/btn_vp_bgmode_0.png</activeoff>
               <activeon>:/btn_vp_bgmode/btn_vp_bgmode_0.png</activeon>
               <selectedoff>:/btn_vp_bgmode/btn_vp_bgmode_2.png</selectedoff>
               <selectedon>:/btn_vp_bgmode/btn_vp_bgmode_1.png</selectedon>:/btn_vp_bgmode/btn_vp_bgmode_0.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
             <property name="autoDefault">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="Btn3_PlotMode">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="font">
              <font>
               <pointsize>12</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { image: url(:/btn_vp_plotmode/btn_vp_plotmode_1.png); }</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="icon">
              <iconset resource="../system_image/system_image.qrc">
               <normaloff>:/btn_vp_plotmode/btn_vp_plotmode_0.png</normaloff>
               <normalon>:/btn_vp_plotmode/btn_vp_plotmode_0.png</normalon>
               <disabledoff>:/btn_vp_plotmode/btn_vp_plotmode_2.png</disabledoff>
               <disabledon>:/btn_vp_plotmode/btn_vp_plotmode_2.png</disabledon>
               <activeoff>:/btn_vp_plotmode/btn_vp_plotmode_0.png</activeoff>
               <activeon>:/btn_vp_plotmode/btn_vp_plotmode_0.png</activeon>
               <selectedoff>:/btn_vp_plotmode/btn_vp_plotmode_0.png</selectedoff>
               <selectedon>:/btn_vp_plotmode/btn_vp_plotmode_1.png</selectedon>:/btn_vp_plotmode/btn_vp_plotmode_0.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
             <property name="autoDefault">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="Btn8_target">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>12</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">border:none;
color: rgb(255, 255, 255);</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="icon">
              <iconset resource="../system_image/system_image.qrc">
               <normaloff>:/target/target_0.png</normaloff>
               <normalon>:/target/target_1.png</normalon>:/target/target_0.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
             <property name="autoDefault">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="Btn4_ResetAngle">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="font">
              <font>
               <pointsize>12</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { image: url(:/btn_vp_resetangle/btn_vp_resetangle_1.png); }</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="icon">
              <iconset resource="../system_image/system_image.qrc">
               <normaloff>:/btn_vp_resetangle/btn_vp_resetangle_0.png</normaloff>:/btn_vp_resetangle/btn_vp_resetangle_0.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
             <property name="autoDefault">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="Btn5_cnc_Link">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>12</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">border:none;
color: rgb(255, 255, 255);</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="icon">
              <iconset resource="../system_image/system_image.qrc">
               <normaloff>:/btn_vp_cnc_link/btn_vp_cnc_link_0.png</normaloff>
               <normalon>:/btn_vp_cnc_link/btn_vp_cnc_link_0.png</normalon>
               <disabledoff>:/btn_vp_cnc_link/btn_vp_cnc_link_0.png</disabledoff>
               <disabledon>:/btn_vp_cnc_link/btn_vp_cnc_link_2.png</disabledon>
               <activeoff>:/btn_vp_cnc_link/btn_vp_cnc_link_2.png</activeoff>
               <activeon>:/btn_vp_cnc_link/btn_vp_cnc_link_1.png</activeon>
               <selectedoff>:/btn_vp_cnc_link/btn_vp_cnc_link_2.png</selectedoff>
               <selectedon>:/btn_vp_cnc_link/btn_vp_cnc_link_1.png</selectedon>:/btn_vp_cnc_link/btn_vp_cnc_link_0.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
             <property name="autoDefault">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="Btn6_angle1">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="font">
              <font>
               <pointsize>12</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { image: url(:/btn_angle/btn_angle_1_0.png); }</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="icon">
              <iconset resource="../system_image/system_image.qrc">
               <normaloff>:/btn_angle/btn_angle_1_2.png</normaloff>
               <normalon>:/btn_angle/btn_angle_1_0.png</normalon>
               <disabledoff>:/btn_angle/btn_angle_1_2.png</disabledoff>
               <disabledon>:/btn_angle/btn_angle_1_0.png</disabledon>
               <activeoff>:/btn_angle/btn_angle_1_1.png</activeoff>
               <activeon>:/btn_angle/btn_angle_1_0.png</activeon>
               <selectedoff>:/btn_angle/btn_angle_1_1.png</selectedoff>
               <selectedon>:/btn_angle/btn_angle_1_0.png</selectedon>:/btn_angle/btn_angle_1_2.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
             <property name="autoDefault">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="Btn7_sleep_mode">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="font">
              <font>
               <pointsize>12</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }
QPushButton:hover { image: url(:/bnt_vp_sleep_mode/btn_vp_sleep_on.png); }</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="icon">
              <iconset resource="../system_image/system_image.qrc">
               <normaloff>:/bnt_vp_sleep_mode/bnt_vp_sleep_mode_none.png</normaloff>
               <normalon>:/bnt_vp_sleep_mode/btn_vp_sleep_on.png</normalon>
               <disabledoff>:/bnt_vp_sleep_mode/bnt_vp_sleep_mode_none.png</disabledoff>
               <disabledon>:/bnt_vp_sleep_mode/btn_vp_sleep_off.png</disabledon>
               <activeoff>:/bnt_vp_sleep_mode/bnt_vp_sleep_mode_none.png</activeoff>
               <activeon>:/bnt_vp_sleep_mode/btn_vp_sleep_on.png</activeon>
               <selectedoff>:/bnt_vp_sleep_mode/bnt_vp_sleep_mode_none.png</selectedoff>
               <selectedon>:/bnt_vp_sleep_mode/btn_vp_sleep_on.png</selectedon>:/bnt_vp_sleep_mode/bnt_vp_sleep_mode_none.png</iconset>
             </property>
             <property name="iconSize">
              <size>
               <width>50</width>
               <height>50</height>
              </size>
             </property>
             <property name="checkable">
              <bool>true</bool>
             </property>
             <property name="autoDefault">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QFrame" name="plot3D">
       <property name="styleSheet">
        <string notr="true">QFrame#plot3D {
    border: 2px solid  rgb(0, 0, 111); /* 邊框樣式 */
}</string>
       </property>
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,9">
        <property name="leftMargin">
         <number>2</number>
        </property>
        <property name="topMargin">
         <number>2</number>
        </property>
        <property name="rightMargin">
         <number>2</number>
        </property>
        <property name="bottomMargin">
         <number>2</number>
        </property>
        <item>
         <widget class="QOpenGLWidget" name="colorbar">
          <property name="styleSheet">
           <string notr="true">background-color: rgb(170, 85, 255);</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QOpenGLWidget" name="plot3D_opengl">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="styleSheet">
           <string notr="true"/>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <layout class="QVBoxLayout" name="Fz_T_TempV_layout" stretch="1,1,1">
       <item>
        <widget class="QFrame" name="FzV_frame">
         <property name="styleSheet">
          <string notr="true">QFrame#FzV_frame {
	background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0.0227273 rgba(0, 0, 58, 255), stop:1 rgba(27, 0, 88, 255));
    border: 2px solid  rgb(0, 0, 111); /* 邊框樣式 */
}</string>
         </property>
         <layout class="QVBoxLayout" name="TorqueV_layout_2" stretch="0,20">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>5</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QFrame" name="FzH_frame">
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <layout class="QHBoxLayout" name="TorqueH_layout_2" stretch="2,1,2,1,2,1">
             <property name="leftMargin">
              <number>5</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>5</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="Fz_lable">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>Fz [N]</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_51">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="Fz_max">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>Max : 0</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_52">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="Fz_avg">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>Avg : 0</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_53">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QOpenGLWidget" name="Torque_opengl_2"/>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="TorqueV_frame">
         <property name="font">
          <font>
           <pointsize>9</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QFrame#TorqueV_frame {
	background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0.0227273 rgba(0, 0, 58, 255), stop:1 rgba(27, 0, 88, 255));
    border: 2px solid  rgb(0, 0, 111); /* 邊框樣式 */
}</string>
         </property>
         <layout class="QVBoxLayout" name="TorqueV_layout" stretch="0,20">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>5</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QFrame" name="TorqueH_frame">
            <layout class="QHBoxLayout" name="TorqueH_layout" stretch="2,1,2,1,2,1">
             <property name="leftMargin">
              <number>5</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>5</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="Torque_lable">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>Torque [Nm]</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_48">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="Torque_max">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>Max : 0</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_49">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="Torque_avg">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>Avg : 0</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_50">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QOpenGLWidget" name="Torque_opengl"/>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="TempV_frame">
         <property name="styleSheet">
          <string notr="true">QFrame#TempV_frame {
	background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0.0227273 rgba(0, 0, 58, 255), stop:1 rgba(27, 0, 88, 255));
    border: 2px solid  rgb(0, 0, 111); /* 邊框樣式 */
}</string>
         </property>
         <layout class="QVBoxLayout" name="TorqueV_layout_3" stretch="0,20">
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>5</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QFrame" name="TempH_frame">
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <layout class="QHBoxLayout" name="TorqueH_layout_3" stretch="2,1,2,1,2,1">
             <property name="leftMargin">
              <number>5</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>5</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="Temp_lable">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>溫度 [°C]</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_54">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="Temp_max">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>Max : 0</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_55">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="Temp_avg">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>Avg : 0</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_56">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QOpenGLWidget" name="Torque_opengl_3"/>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="Fx_Fy_CF_layout" stretch="1,100">
     <item>
      <spacer name="horizontalSpacer_22">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>13</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QFrame" name="Fx_Fy_CFH_frame">
       <property name="styleSheet">
        <string notr="true">QFrame#Fx_Fy_CFH_frame {
	background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0.0227273 rgba(0, 0, 58, 255), stop:1 rgba(27, 0, 88, 255));
    border: 2px solid  rgb(0, 0, 111); /* 邊框樣式 */
}</string>
       </property>
       <layout class="QHBoxLayout" name="Fx_Fy_CFH_layout" stretch="15,120">
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QFrame" name="Fx_Fy_CFV_frame">
          <layout class="QVBoxLayout" name="Fx_Fy_CFV_layout" stretch="0,0,15,0,0,1">
           <item>
            <layout class="QHBoxLayout" name="N_lable_layout">
             <item>
              <widget class="QLabel" name="N_lable">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>[N]</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_19">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="Fx_Fy_CF_btn_layout" stretch="1,1,1,1,1,5">
             <item>
              <widget class="QPushButton" name="Fx_btn">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
color:#B4E100;
border:none;
}

QPushButton:checked {
color:#666666;
border:none;
}</string>
               </property>
               <property name="text">
                <string>Fx</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_28">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="Fy_btn">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
color:#FFFFFF;
border:none;
}

QPushButton:checked {
color:#666666;
border:none;
}</string>
               </property>
               <property name="text">
                <string>Fy</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_32">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="CF_btn">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
color:#00FFFF;
border:none;
}
 
QPushButton:checked {
color:#666666;
border:none;
}</string>
               </property>
               <property name="text">
                <string>C.F.</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_17">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <spacer name="verticalSpacer_10">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="CF_max">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>12</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(255, 255, 255);</string>
             </property>
             <property name="text">
              <string>Max : 0</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="CF_avg">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>12</pointsize>
               <weight>75</weight>
               <bold>true</bold>
              </font>
             </property>
             <property name="styleSheet">
              <string notr="true">color: rgb(255, 255, 255);</string>
             </property>
             <property name="text">
              <string>Avg  : 0</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_6">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QOpenGLWidget" name="Fx_Fy_CF_opengl"/>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QFrame" name="bottom_frame">
     <property name="styleSheet">
      <string notr="true">QFrame#bottom_frame {
	background-color: qlineargradient(spread:pad, x1:0, y1:1, x2:0, y2:0, stop:0 rgba(0, 0, 127, 141), stop:1 rgba(0, 0, 0, 72));
}</string>
     </property>
     <layout class="QHBoxLayout" name="Bottom" stretch="1,2,1,12,1,4,1,3">
      <item>
       <spacer name="horizontalSpacer_21">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="Label_Info">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>點狀模式</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_20">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="Label_Status">
        <property name="palette">
         <palette>
          <active>
           <colorrole role="WindowText">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>0</green>
              <blue>0</blue>
             </color>
            </brush>
           </colorrole>
          </active>
          <inactive>
           <colorrole role="WindowText">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>0</green>
              <blue>0</blue>
             </color>
            </brush>
           </colorrole>
          </inactive>
          <disabled>
           <colorrole role="WindowText">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>120</red>
              <green>120</green>
              <blue>120</blue>
             </color>
            </brush>
           </colorrole>
          </disabled>
         </palette>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>&lt;span style=&quot;color:red;&quot;&gt;C.F.&lt;/span&gt;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;
&lt;span style=&quot;color:yellow;&quot;&gt;Fz&lt;/span&gt;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;
&lt;span style=&quot;color:white;&quot;&gt;Torque&lt;/span&gt;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;
&lt;span style=&quot;color:white;&quot;&gt;Temp.(示意字串,開始執行時為空)&lt;/span&gt;</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_15">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="Label_Rate">
        <property name="palette">
         <palette>
          <active>
           <colorrole role="WindowText">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>255</blue>
             </color>
            </brush>
           </colorrole>
          </active>
          <inactive>
           <colorrole role="WindowText">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>255</red>
              <green>255</green>
              <blue>255</blue>
             </color>
            </brush>
           </colorrole>
          </inactive>
          <disabled>
           <colorrole role="WindowText">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>120</red>
              <green>120</green>
              <blue>120</blue>
             </color>
            </brush>
           </colorrole>
          </disabled>
         </palette>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>10000筆/ 秒</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_11">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="Image_machradar">
        <property name="font">
         <font>
          <pointsize>12</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">border:none;
color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset resource="../system_image/system_image.qrc">
          <normaloff>:/other/logo_machradarpro.png</normaloff>:/other/logo_machradarpro.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>255</width>
          <height>15</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../system_image/system_image.qrc"/>
 </resources>
 <connections/>
</ui>
