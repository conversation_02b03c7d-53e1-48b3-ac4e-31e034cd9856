import os
import subprocess
import sys
from pathlib import Path
from PySide2.QtCore import QTimer
from utils.logging_config import LoggerConfigurator

# 初始化日誌系統
log_configurator = LoggerConfigurator()
logger = log_configurator.get_logger(__name__)


def check_mode_path(mode_name, exe_mapping=None):
    """
    檢查指定的檔案路徑是否存在

    Args:
        mode_name (str): 要檢查的模式名稱
        exe_mapping (dict, optional): 模式名稱到可執行文件的映射字典
        
    Returns:
        bool, str: 檔案存在返回 True, 路徑 | 不存在返回 False, 路徑(或None)
    """

    # 預設映射
    default_mapping = {
        "Analyzer": "MachAnalyzer.exe",
        "Homepage": "MachRadar_Homepage.exe",
        "ReadFile": "MachRadar_ReadFile.exe",
        "RealTime": "MachRadar_RealTime.exe",
        "MachRadarPro_ReadFile": "MachRadarPro_ReadFile.exe",
        "MachRadarPro_RealTime": "MachRadarPro_RealTime.exe"
    }

    
    # 使用提供的映射或默認映射
    mapping = exe_mapping if exe_mapping else default_mapping
    
    # 檢查模式是否存在
    if mode_name not in mapping:
        logger.error(f"未知的模式：{mode_name}")
        return False, None

    # 檢查可執行文件路徑是否為空
    exe_name = mapping[mode_name]
    if not exe_name:
        logger.error(f"可執行文件路徑為空：{exe_name}")
        return False, exe_name

    # exe_name不為空，檢查可執行文件路徑是否存在
    logger.info(f"切換到模式：{mode_name}，對應可執行文件：{exe_name}")
    try:
        # 如果是絕對路徑，直接檢查
        if os.path.isabs(exe_name):
            abs_result = os.path.exists(exe_name)
            logger.debug(f"(絕對路徑)檢查檔案路徑是否存在：{exe_name} | 結果：{abs_result}")
            return abs_result, exe_name
        
        # 相對路徑，在當前目錄中搜索
        current_dir = Path.cwd()
        full_path = current_dir / exe_name
        
        rel_result = full_path.exists()
        logger.debug(f"(相對路徑)檢查檔案路徑是否存在：{full_path} | 結果：{rel_result}")
        return rel_result, full_path
        
    except Exception as e:
        logger.error(f"檢查檔案路徑是否存在時發生錯誤：{e}")
        return False, exe_name


def open_exe(exe_path=None):
    """
    開啟指定的可執行文件
    
    Args:
        exe_path (str, optional): 可執行文件的完整路徑。如果未提供，將在當前目錄和系統路徑中搜索
    
    Returns:
        bool: 成功開啟返回 True，失敗返回 False
    """
    try:
        # 根據操作系統選擇適當的方法開啟
        if sys.platform.startswith('win'):
            # Windows 系統
            subprocess.Popen([str(exe_path)], shell=True)
        elif sys.platform.startswith('darwin'):
            # macOS 系統
            subprocess.Popen(['open', str(exe_path)])
        else:
            # Linux 和其他 Unix 系統
            subprocess.Popen([str(exe_path)])
        
        logger.info(f"開啟可執行文件：{exe_path}")
        return True

    except Exception as e:
        logger.error(f"開啟可執行文件時發生錯誤：{e}")
        return False