# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'add_tool_window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *


class Ui_add_tool_window(object):
    def setupUi(self, add_tool_window):
        if not add_tool_window.objectName():
            add_tool_window.setObjectName(u"add_tool_window")
        add_tool_window.resize(399, 59)
        add_tool_window.setMaximumSize(QSize(399, 59))
        add_tool_window.setStyleSheet(u"QWidget#add_tool_window{\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"}")
        self.gridLayout = QGridLayout(add_tool_window)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setHorizontalSpacing(6)
        self.gridLayout.setVerticalSpacing(2)
        self.gridLayout.setContentsMargins(9, 5, 9, 5)
        self.mac_label = QLabel(add_tool_window)
        self.mac_label.setObjectName(u"mac_label")
        font = QFont()
        font.setFamily(u"Arial Black")
        self.mac_label.setFont(font)
        self.mac_label.setStyleSheet(u"color: rgb(255, 255, 255);")

        self.gridLayout.addWidget(self.mac_label, 0, 0, 1, 1)

        self.mac_lineEdit = QLineEdit(add_tool_window)
        self.mac_lineEdit.setObjectName(u"mac_lineEdit")
        self.mac_lineEdit.setStyleSheet(u"color: rgb(0, 0, 0);")

        self.gridLayout.addWidget(self.mac_lineEdit, 0, 1, 1, 1)

        self.add_cancel_Btn = QPushButton(add_tool_window)
        self.add_cancel_Btn.setObjectName(u"add_cancel_Btn")
        font1 = QFont()
        font1.setFamily(u"Arial Black")
        font1.setPointSize(10)
        self.add_cancel_Btn.setFont(font1)
        self.add_cancel_Btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: 2px solid #7AFEC6;\n"
"    color: rgb(255, 255, 255);\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")
        self.add_cancel_Btn.setIconSize(QSize(20, 20))
        self.add_cancel_Btn.setCheckable(True)

        self.gridLayout.addWidget(self.add_cancel_Btn, 0, 2, 1, 1)

        self.add_save_Btn = QPushButton(add_tool_window)
        self.add_save_Btn.setObjectName(u"add_save_Btn")
        self.add_save_Btn.setFont(font1)
        self.add_save_Btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color:#5448B6;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7AFEC6;\n"
"     color: rgb(255, 255, 255);\n"
"    border:none\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border:none\n"
"}")
        self.add_save_Btn.setText(u"\u65b0\u589e")
        self.add_save_Btn.setCheckable(True)

        self.gridLayout.addWidget(self.add_save_Btn, 0, 3, 1, 1)

        self.gridLayout.setColumnStretch(0, 2)
        self.gridLayout.setColumnStretch(1, 5)
        self.gridLayout.setColumnStretch(2, 1)
        self.gridLayout.setColumnStretch(3, 1)

        self.retranslateUi(add_tool_window)

        QMetaObject.connectSlotsByName(add_tool_window)
    # setupUi

    def retranslateUi(self, add_tool_window):
        add_tool_window.setWindowTitle(QCoreApplication.translate("add_tool_window", u"Form", None))
        self.mac_label.setText(QCoreApplication.translate("add_tool_window", u"MAC : ", None))
        self.add_cancel_Btn.setText(QCoreApplication.translate("add_tool_window", u"\u53d6\u6d88", None))
    # retranslateUi

