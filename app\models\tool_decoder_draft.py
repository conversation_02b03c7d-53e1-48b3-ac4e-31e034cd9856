import math
from typing import <PERSON><PERSON>
import numpy as np
from config import DEFAULT_DISPLAY_DECIMALS
from . import logger


class ToolDecoder:
    _HEX16_TO_FLOAT_DIVISOR: float = 6553.5

    def __init__(self):
        self.display_decimal_places = DEFAULT_DISPLAY_DECIMALS

    @staticmethod
    def decode_hex16str_to_float(hex_str: str, divisor: float = _HEX16_TO_FLOAT_DIVISOR) -> float:
        if divisor <= 0:
            logger.warning(f"{__name__}: Divisor is {divisor}. Using 1.0 instead.")
            divisor = 1.0
        return int(hex_str, 16) / divisor
    
    @staticmethod
    def decode_hex16str_to_int(hex_str: str) -> int:
        return int(hex_str, 16)
    
    @staticmethod
    def parse_charging_and_sleeping_flags(status_code: int) -> Tuple[bool, bool]:
        """
        Returns: (is_charging, is_sleeping)
        """
        FLAG_Charge  = 0x01  # 二進位：0001 
        FLAG_Sleep   = 0x02  # 二進位：0010 
        flags = status_code
        
        # 檢查 FLAG 是否被置位(是否被Set)
        if flags & FLAG_Charge:
            is_charging = True
        else:
            is_charging = False
        
        if flags & FLAG_Sleep:
            is_sleeping = True
        else:
            is_sleeping = False
        
        return (is_charging, is_sleeping)

    @staticmethod
    def decode_low_freq_tare_reference(hex_data: str, offset: int = 32) -> Tuple[int, int, int, int, int, bool, bool, str]:
        """
        Returns: (data_ADXL_X, data_ADXL_Y, data_ADXL_Z, data_Temperature, wifi_RSSI, is_charging, is_sleeping, MAC)
        """
        data_ADXL_X = ToolDecoder.decode_hex16str_to_int(hex_data[0+offset:4+offset])
        data_ADXL_Y = ToolDecoder.decode_hex16str_to_int(hex_data[4+offset:8+offset])
        data_ADXL_Z = ToolDecoder.decode_hex16str_to_int(hex_data[8+offset:12+offset])
        data_Temperature = ToolDecoder.decode_hex16str_to_int(hex_data[14+offset:16+offset]) + ToolDecoder.decode_hex16str_to_int(hex_data[12+offset:14+offset]) #溫度，高低位元有錯位
        wifi_RSSI = ToolDecoder.decode_hex16str_to_int(hex_data[16+offset:18+offset])

        is_charging, is_sleeping = ToolDecoder.parse_charging_and_sleeping_flags(ToolDecoder.decode_hex16str_to_int(hex_data[19+offset:20+offset]))
        MAC = hex_data[20+offset:32+offset]

        return (data_ADXL_X, data_ADXL_Y, data_ADXL_Z, data_Temperature, wifi_RSSI, is_charging, is_sleeping, MAC)
    
    @staticmethod
    def decode_high_freq_tare_reference(hex_data: str, offset: int = 32) -> Tuple[float, float, float, float, float, float]:
        """
        Returns: (data_x, data_y, data_xy, data_tension, data_torsion, data_battery)
        """
        Data_x= ToolDecoder.decode_hex16str_to_float(hex_data[0+offset:4+offset]) 
        Data_y= ToolDecoder.decode_hex16str_to_float(hex_data[4+offset:8+offset])
        Data_xy = math.sqrt( (Data_x*Data_x) + (Data_y*Data_y) )
        Data_tension= ToolDecoder.decode_hex16str_to_float(hex_data[8+offset:12+offset])
        Data_torsion= ToolDecoder.decode_hex16str_to_float(hex_data[12+offset:16+offset])
        Data_battery= ToolDecoder.decode_hex16str_to_float(hex_data[16+offset:20+offset])
        return (Data_x, Data_y, Data_xy, Data_tension, Data_torsion, Data_battery)
