import pytest
import numpy as np
from scipy import signal
import app.models.filters as filters



FILTER_CLASSES = [
    filters.MeanFilter,
    filters.Median<PERSON>ilter,
    filters.GaussianFilter,
    filters.ButterworthFilter,
    filters.<PERSON><PERSON>tzkyGolayFilter,
    filters.MovingAverage,
    filters.MSFilter,
]



@pytest.mark.parametrize("filter_class", FILTER_CLASSES)
def test_filters_defaultInit_can_run(filter_class):
    data = np.array([0.0, 0.0, 0.0], dtype=float)
    filter_instance = filter_class()  #init with default setting

    try:
        filter_instance.applyTo(data)
    except Exception as e:
        pytest.fail(f"{filter_class.__name__} raised {type(e).__name__}: {e}")


@pytest.mark.parametrize("filter_class", FILTER_CLASSES)
def test_filters_defaultInit_with_single_element_array_expect_data_unchanged(filter_class):
    data = np.array([0.0], dtype=float)
    filter_instance = filter_class()  #init with default setting
    print(f"{filter_class.__name__}: data before = {data}") #TEST

    try:
        filter_instance.applyTo(data)
    except Exception as e:
        pytest.fail(f"{filter_class.__name__} raised {type(e).__name__}: {e}")

    assert data.size == 1, \
        f"{filter_class.__name__}: Output size changed from 1 to {data.size}"
    assert np.allclose(data, np.array([0.0])), \
        f"{filter_class.__name__}: data changed to {data[0]} (expected unchanged)"


@pytest.mark.parametrize("filter_class", FILTER_CLASSES)
def test_filters_defaultInit_with_empty_array_expect_data_unchanged(filter_class):
    data = np.array([])
    filter_instance = filter_class()  #init with default setting
    print(f"{filter_class.__name__}: data before = {data}") #TEST

    try:
        filter_instance.applyTo(data)
    except Exception as e:
        pytest.fail(f"{filter_class.__name__} raised {type(e).__name__}: {e}")

    assert data.size == 0, \
        f"{filter_class.__name__}: Output size is {data.size} instead of 0"


def test_MeanFilter_with_size3_10float():
    test_data = np.array([-3.0, 6.0, 0.0, 90.0, 123.0, -30.6, -60.3, -0.0, 15.0, -21.0])
    test_filter = filters.MeanFilter(window_size=3)
    expect_result = np.array([0.0, 1.0, 32.0, 71.0, 60.8, 10.7, -30.3, -15.1, -2.0, -9.0])

    test_filter.applyTo(test_data)

    # Note that float comparison is not exact
    assert np.allclose(test_data, expect_result, atol=1e-10), \
        f"[{__name__}] Failed: returned {repr(test_data)}, expected {repr(expect_result)}"


def test_MedianFilter_with_size3_10float():
    test_data = np.array([-3.0, 6.0, 0.0, 90.0, 123.0, -30.6, -60.3, -0.0, 15.0, -21.0])
    test_filter = filters.MedianFilter(window_size=3)
    expect_result = np.array([-3.0, 0.0, 6.0, 90.0, 90.0, -30.6, -30.6, 0.0, 0.0, -21.0])

    test_filter.applyTo(test_data)

    # Note that float comparison is not exact
    assert np.allclose(test_data, expect_result, atol=1e-10), \
        f"[{__name__}] Failed: returned {repr(test_data)}, expected {repr(expect_result)}"


def test_MedianFilter_with_size3_10int():
    test_data = np.array([5, -1, 0, 8, -7, 0, 6, -2, 9, 0], dtype=int)
    test_filter = filters.MedianFilter(window_size=3)
    expect_result = np.array([5, 0, 0, 0, 0, 0, 0, 6, 0, 0], dtype=int)

    test_filter.applyTo(test_data)

    assert test_data.dtype == np.int_, \
        f"Type check failed: expected np.int_, got {test_data.dtype}"
    assert (test_data == expect_result).all(), \
        f"[{__name__}] Failed: returned {repr(test_data)}, expected {repr(expect_result)}"


def test_GaussianFilter_with_size3_sigma1_10float():
    test_data = np.array([-3.0, 6.0, 0.0, 90.0, 123.0, -30.6, -60.3, -0.0, 15.0, -21.0])
    test_sigma = 1.0
    test_filter = filters.GaussianFilter(window_size=3, sigma=test_sigma)
    kernel = [1 / (test_sigma * np.sqrt(2*np.pi)) * np.exp(-i**2 / (2*test_sigma**2)) for i in (-1, 0, 1)]
    kernel = np.array(kernel, dtype=np.float64)
    kernel /= kernel.sum()
    expect_result = np.array([
        kernel[0]*test_data[0] + kernel[1]*test_data[0] + kernel[2]*test_data[1],
        kernel[0]*test_data[0] + kernel[1]*test_data[1] + kernel[2]*test_data[2],
        kernel[0]*test_data[1] + kernel[1]*test_data[2] + kernel[2]*test_data[3],
        kernel[0]*test_data[2] + kernel[1]*test_data[3] + kernel[2]*test_data[4],
        kernel[0]*test_data[3] + kernel[1]*test_data[4] + kernel[2]*test_data[5],
        kernel[0]*test_data[4] + kernel[1]*test_data[5] + kernel[2]*test_data[6],
        kernel[0]*test_data[5] + kernel[1]*test_data[6] + kernel[2]*test_data[7],
        kernel[0]*test_data[6] + kernel[1]*test_data[7] + kernel[2]*test_data[8],
        kernel[0]*test_data[7] + kernel[1]*test_data[8] + kernel[2]*test_data[9],
        kernel[0]*test_data[8] + kernel[1]*test_data[9] + kernel[2]*test_data[9]
    ])

    test_filter.applyTo(test_data)

    # Note that float comparison is not exact
    assert np.allclose(test_data, expect_result, atol=1e-10), \
        f"[{__name__}] Failed: returned {repr(test_data)}, expected {repr(expect_result)}"


def test_ButterworthFilter_lowpass_with_cutoff999_fs10000_order4_20float():
    test_data = np.array([
        100, -100, 100, 0, 100, -100, 100, -100, 100, -100,
        100, -100, 100, -100, 100, -100, 0, -100, 100, -100
    ], dtype=float)
    cutoff_freq = 999
    fs = 10000
    order = 4
    test_filter = filters.ButterworthFilter(cutoff_freq=cutoff_freq, fs=fs, btype='low', order=order)
    normal_cutoff = cutoff_freq / (fs / 2)
    expect_result = signal.filtfilt(*signal.butter(order, normal_cutoff, btype='low'), test_data)

    test_filter.applyTo(test_data)

    # Note that float comparison is not exact
    assert np.allclose(test_data, expect_result, atol=1e-10), \
        f"[{__name__}] Failed: returned {repr(test_data)}, expected {repr(expect_result)}"


def test_ButterworthFilter_highpass_with_cutoff1_fs10000_order4_20float():
    test_data = np.array([
        10, 10, 10, 10, 10, 5, 0, -5, -10, -15,
        -200, -100, 0, 5, 10, 15, 50, 25, 30, 50
    ], dtype=float)
    cutoff_freq = 1
    fs = 10000
    order = 4
    test_filter = filters.ButterworthFilter(cutoff_freq=cutoff_freq, fs=fs, btype='high', order=order)
    normal_cutoff = cutoff_freq / (fs / 2)
    expect_result = signal.filtfilt(*signal.butter(order, normal_cutoff, btype='high'), test_data)

    test_filter.applyTo(test_data)

    # Note that float comparison is not exact
    assert np.allclose(test_data, expect_result, atol=1e-10), \
        f"[{__name__}] Failed: returned {repr(test_data)}, expected {repr(expect_result)}"


def test_SavitzkyGolayFilter_with_size3_polyorder1_10float():
    test_data = np.array([-50, -45, -30, -20, -5, 0, 10, 20, 33, 40], dtype=float)
    window_size = 3
    polyorder = 1
    test_filter = filters.SavitzkyGolayFilter(window_size=window_size, polyorder=polyorder)
    expect_result = signal.savgol_filter(test_data, test_filter.window_size, test_filter.polyorder)

    test_filter.applyTo(test_data)

    assert test_filter.window_size == window_size, \
        f"[{__name__}] Failed: does not set window_size correctly."
    assert test_filter.polyorder == polyorder, \
        f"[{__name__}] Failed: does not set polyorder correctly."
    # Note that float comparison is not exact
    assert np.allclose(test_data, expect_result, atol=1e-10), \
        f"[{__name__}] Failed: returned {repr(test_data)}, expected {repr(expect_result)}"


def test_SavitzkyGolayFilter_with_size3_polyorder3_10float():
    test_data = np.array([-50, -45, -30, -20, -5, 0, 10, 20, 33, 40], dtype=float)
    window_size = 3
    polyorder = window_size #it should be auto reset to < window_size
    test_filter = filters.SavitzkyGolayFilter(window_size=window_size, polyorder=polyorder)
    expect_result = signal.savgol_filter(test_data, test_filter.window_size, test_filter.polyorder)

    test_filter.applyTo(test_data)

    assert test_filter.window_size == window_size, \
        f"[{__name__}] Failed: does not set window_size correctly."
    assert test_filter.polyorder == window_size - 1, \
        f"[{__name__}] Failed: does not auto reset polyorder to be window_size - 1."
    # Note that float comparison is not exact
    assert np.allclose(test_data, expect_result, atol=1e-10), \
        f"[{__name__}] Failed: returned {repr(test_data)}, expected {repr(expect_result)}"




