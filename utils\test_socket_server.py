#!/usr/bin/env python3
"""
測試 SocketWorker Server 功能的簡單腳本
"""

import sys
import os
import time
import socket
import threading
from PySide2.QtCore import QCoreApplication

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 直接導入 socket_work 模組，避免 utils.__init__.py 的依賴問題
import importlib.util
spec = importlib.util.spec_from_file_location("socket_work",
    os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "utils", "socket_work.py"))
socket_work_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(socket_work_module)
SocketWorker = socket_work_module.SocketWorker

class TestSocketServer:
    def __init__(self):
        self.app = QCoreApplication(sys.argv)
        self.socket_worker = SocketWorker(host='0.0.0.0', port=1333, max_clients=3)
        
        # 連接信號
        self.socket_worker.raw_data_received.connect(self.on_data_received)
        self.socket_worker.sig_socket_connect.connect(self.on_server_status_changed)
        self.socket_worker.sig_client_connected.connect(self.on_client_connected)
        self.socket_worker.sig_client_disconnected.connect(self.on_client_disconnected)
        
    def on_data_received(self, data):
        """處理接收到的數據"""
        print(f"收到數據: {data[:100]}...")  # 只顯示前100個字符
        
    def on_server_status_changed(self, status_data):
        """處理 Server 狀態變化"""
        print(f"Server 狀態變化: {status_data}")
        
    def on_client_connected(self, client_id):
        """處理客戶端連接"""
        print(f"客戶端已連接: {client_id}")
        connected_clients = self.socket_worker.get_connected_clients()
        print(f"當前連接的客戶端: {connected_clients}")
        
    def on_client_disconnected(self, client_id):
        """處理客戶端斷開"""
        print(f"客戶端已斷開: {client_id}")
        connected_clients = self.socket_worker.get_connected_clients()
        print(f"當前連接的客戶端: {connected_clients}")
        
    def start_server(self):
        """啟動 Server"""
        print("啟動 Socket Server...")
        self.socket_worker.start()
        
    def stop_server(self):
        """停止 Server"""
        print("停止 Socket Server...")
        self.socket_worker.stop()
        
    def run(self):
        """運行測試"""
        try:
            self.start_server()
            print("Server 已啟動，按 Ctrl+C 停止...")
            self.app.exec_()
        except KeyboardInterrupt:
            print("\n收到中斷信號，正在停止...")
        finally:
            self.stop_server()

def create_test_client(host='127.0.0.1', port=1333, client_id=1):
    """創建測試客戶端"""
    def client_thread():
        try:
            print(f"客戶端 {client_id} 嘗試連接到 {host}:{port}")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.connect((host, port))
            print(f"客戶端 {client_id} 已連接")
            
            # 發送測試數據
            for i in range(10):
                test_data = f"Client{client_id}_Data_{i:03d}_" + "A" * 20
                sock.send(test_data.encode())
                print(f"客戶端 {client_id} 發送: {test_data}")
                time.sleep(1)
                
        except Exception as e:
            print(f"客戶端 {client_id} 錯誤: {e}")
        finally:
            try:
                sock.close()
                print(f"客戶端 {client_id} 已斷開")
            except:
                pass
    
    thread = threading.Thread(target=client_thread, daemon=True)
    thread.start()
    return thread

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Socket Server 測試工具')
    parser.add_argument('--mode', choices=['server', 'client', 'both'], default='server',
                       help='運行模式: server(僅服務器), client(僅客戶端), both(服務器+客戶端)')
    parser.add_argument('--host', default='127.0.0.1', help='服務器地址')
    parser.add_argument('--port', type=int, default=1333, help='服務器端口')
    parser.add_argument('--clients', type=int, default=2, help='測試客戶端數量')
    
    args = parser.parse_args()
    
    if args.mode in ['server', 'both']:
        # 啟動服務器
        server = TestSocketServer()
        
        if args.mode == 'both':
            # 延遲啟動客戶端
            def start_clients():
                time.sleep(2)  # 等待服務器啟動
                print(f"啟動 {args.clients} 個測試客戶端...")
                for i in range(args.clients):
                    create_test_client(args.host, args.port, i+1)
            
            client_starter = threading.Thread(target=start_clients, daemon=True)
            client_starter.start()
        
        server.run()
        
    elif args.mode == 'client':
        # 僅啟動客戶端
        print(f"啟動 {args.clients} 個測試客戶端...")
        threads = []
        for i in range(args.clients):
            thread = create_test_client(args.host, args.port, i+1)
            threads.append(thread)
        
        try:
            # 等待所有客戶端完成
            for thread in threads:
                thread.join()
        except KeyboardInterrupt:
            print("\n收到中斷信號，正在停止...")

if __name__ == '__main__':
    main()
