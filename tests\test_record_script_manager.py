import pytest
from unittest.mock import Mock, patch
from PySide2.QtCore import QObject
from app.models.record_script_manager import RecordScript<PERSON>anager, ScriptState
from app.models.record_manager import RecordManager

@pytest.fixture
def record_manager():
    """Create a mock record manager"""
    return Mock(spec=RecordManager)

@pytest.fixture
def script_manager(record_manager):
    """Create a script manager instance"""
    return RecordScriptManager(None, record_manager)

def test_initial_state(script_manager):
    """Test initial state of script manager"""
    status = script_manager.get_current_status()
    assert status['state'] == ScriptState.IDLE.value
    assert status['script'] is None
    assert status['is_running'] is False
    assert status['is_paused'] is False
    assert status['is_idle'] is True

def test_configure_standard_mode_valid(script_manager):
    """Test valid Standard Mode configuration"""
    success = script_manager.configure_standard_mode("Tool_001", 60.0)
    assert success is True
    assert script_manager.current_script is not None
    assert script_manager.current_script['mode'] == 'standard'
    assert len(script_manager.current_script['tools']) == 1
    assert script_manager.current_script['tools'][0]['name'] == 'Tool_001'
    assert script_manager.current_script['tools'][0]['duration'] == 60.0
    assert script_manager.script_name == "Standard_Tool_001_60.0s"

def test_configure_standard_mode_invalid(script_manager):
    """Test invalid Standard Mode configuration"""
    # Test empty tool name
    success = script_manager.configure_standard_mode("", 60.0)
    assert success is False
    # Test zero duration
    success = script_manager.configure_standard_mode("Tool_001", 0.0)
    assert success is False
    # Test negative duration
    success = script_manager.configure_standard_mode("Tool_001", -10.0)
    assert success is False

def test_validate_script_standard_mode(script_manager):
    """Test script validation for Standard Mode"""
    # Valid script
    valid_script = {
        'mode': 'standard',
        'tools': [{'name': 'Tool_001', 'duration': 60.0}]
    }
    is_valid, errors = script_manager.validate_script(valid_script)
    assert is_valid is True
    assert len(errors) == 0
    # Invalid script - wrong number of tools
    invalid_script = {
        'mode': 'standard',
        'tools': [
            {'name': 'Tool_001', 'duration': 60.0},
            {'name': 'Tool_002', 'duration': 30.0}
        ]
    }
    is_valid, errors = script_manager.validate_script(invalid_script)
    assert is_valid is False
    assert len(errors) > 0
    assert any("exactly one tool" in error for error in errors)
    # Invalid script - missing tool name
    invalid_script = {
        'mode': 'standard',
        'tools': [{'name': '', 'duration': 60.0}]
    }
    is_valid, errors = script_manager.validate_script(invalid_script)
    assert is_valid is False
    assert len(errors) > 0
    assert any("Tool name is required" in error for error in errors)
    # Invalid script - zero duration
    invalid_script = {
        'mode': 'standard',
        'tools': [{'name': 'Tool_001', 'duration': 0.0}]
    }
    is_valid, errors = script_manager.validate_script(invalid_script)
    assert is_valid is False
    assert len(errors) > 0
    assert any("Duration must be greater than 0" in error for error in errors)

def test_validate_script_invalid_mode(script_manager):
    """Test script validation with invalid mode"""
    invalid_script = {
        'mode': 'invalid_mode',
        'tools': [{'name': 'Tool_001', 'duration': 60.0}]
    }
    is_valid, errors = script_manager.validate_script(invalid_script)
    assert is_valid is False
    assert len(errors) > 0
    assert any("Invalid mode" in error for error in errors)

def test_validate_script_no_tools(script_manager):
    """Test script validation with no tools"""
    invalid_script = {
        'mode': 'standard',
        'tools': []
    }
    is_valid, errors = script_manager.validate_script(invalid_script)
    assert is_valid is False
    assert len(errors) > 0
    assert any("No tools specified" in error for error in errors)

def test_start_script_success(script_manager):
    """Test successful script start"""
    # Configure script
    script_manager.configure_standard_mode("Tool_001", 60.0)
    # Start script
    success = script_manager.start_script()
    assert success is True
    assert script_manager.current_state == ScriptState.RUNNING
    assert script_manager.script_worker is not None

def test_start_script_no_script_configured(script_manager):
    """Test starting script without configuration"""
    success = script_manager.start_script()
    assert success is False

def test_start_script_already_running(script_manager):
    """Test starting script when already running"""
    # Set state to running
    script_manager.current_state = ScriptState.RUNNING
    success = script_manager.start_script()
    assert success is False

def test_pause_resume_script(script_manager):
    """Test pause and resume functionality"""
    # Mock worker
    script_manager.script_worker = Mock()
    script_manager.current_state = ScriptState.RUNNING
    # Test pause
    success = script_manager.pause_script()
    assert success is True
    assert script_manager.current_state == ScriptState.PAUSED
    script_manager.script_worker.pause.assert_called_once()
    # Test resume
    success = script_manager.resume_script()
    assert success is True
    assert script_manager.current_state == ScriptState.RUNNING
    script_manager.script_worker.resume.assert_called_once()

def test_cancel_script(script_manager):
    """Test script cancellation"""
    # Mock worker
    script_manager.script_worker = Mock()
    script_manager.current_state = ScriptState.RUNNING
    # Test cancel
    success = script_manager.cancel_script()
    assert success is True
    assert script_manager.current_state == ScriptState.CANCELLED
    script_manager.script_worker.cancel.assert_called_once()

def test_reset_script(script_manager):
    """Test script reset"""
    # Configure and set running state
    script_manager.configure_standard_mode("Tool_001", 60.0)
    script_manager.current_state = ScriptState.RUNNING
    script_manager.script_worker = Mock()
    script_manager.script_worker.isRunning.return_value = True
    # Test reset
    success = script_manager.reset_script()
    assert success is True
    assert script_manager.current_state == ScriptState.IDLE
    assert script_manager.current_script is None
    assert script_manager.script_worker is None
    assert script_manager.script_name == ""

def test_config_loading_from_config_py(script_manager):
    """Test that configuration is loaded from config.py"""
    # Check that default config values are loaded
    config = script_manager.record_script_config
    assert config['connection_retry_attempts'] == 3
    assert config['connection_retry_interval'] == 0.5
    assert config['script_recordings_directory'] == 'script_recordings'
    assert config['action_on_error'] == 'ask'
    assert config['user_action_timeout'] == 30

def test_script_error_signal_emission_on_validation_failure(script_manager):
    """Test script_error signal emission when script validation fails"""
    # Mock the signal
    with patch.object(script_manager, 'script_error') as mock_signal:
        # Set invalid script
        script_manager.current_script = {'mode': 'invalid', 'tools': []}
        
        # Try to start script (should fail validation)
        script_manager.start_script()
        
        # Verify error signal was emitted
        mock_signal.emit.assert_called_once()

def test_script_error_signal_emission_on_startup_failure(script_manager):
    """Test script_error signal emission when script startup fails"""
    # Configure valid script
    script_manager.configure_standard_mode("Tool_001", 60.0)
    
    # Mock ScriptWorker to raise exception
    with patch('app.models.script_worker.ScriptWorker') as mock_script_worker_class:
        mock_script_worker_class.side_effect = Exception("ScriptWorker creation failed")
        
        # Mock the signal
        with patch.object(script_manager, 'script_error') as mock_signal:
            # Try to start script (should fail)
            script_manager.start_script()
            
            # Verify error signal was emitted
            mock_signal.emit.assert_called_once()

def test_script_worker_references(script_manager):
    """Test that decoder worker and record manager references are properly set"""
    # Create mock components
    mock_decoder_worker = Mock()
    mock_record_manager = Mock()
    
    # Create script manager with references
    script_manager_with_refs = RecordScriptManager(mock_decoder_worker, mock_record_manager)
    
    # Verify references are set
    assert script_manager_with_refs.decoder_worker == mock_decoder_worker
    assert script_manager_with_refs.record_manager == mock_record_manager

def test_script_worker_creation_with_references(script_manager):
    """Test that ScriptWorker is created with proper references"""
    # Configure script
    script_manager.configure_standard_mode("Tool_001", 60.0)
    
    # Mock ScriptWorker
    with patch('app.models.script_worker.ScriptWorker') as mock_script_worker_class:
        mock_worker = Mock()
        mock_script_worker_class.return_value = mock_worker
        
        # Start script
        success = script_manager.start_script()
        assert success is True
        
        # Verify ScriptWorker was created with config
        mock_script_worker_class.assert_called_once()
        call_args = mock_script_worker_class.call_args
        assert call_args[0][0] == script_manager.current_script  # script_data
        assert call_args[0][1] == script_manager.record_script_config  # record_script_config 