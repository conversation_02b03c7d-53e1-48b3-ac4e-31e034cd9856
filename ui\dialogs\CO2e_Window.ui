<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CO2e_Window</class>
 <widget class="QWidget" name="CO2e_Window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>450</width>
    <height>600</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>450</width>
    <height>600</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>450</width>
    <height>600</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#CO2e_Window{
background-color: #0C0C44;
border: 2px solid #5448B6;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>10</number>
   </property>
   <item>
    <widget class="QWidget" name="CO2_title1" native="true">
     <property name="styleSheet">
      <string notr="true">QWidget#CO2_title1{
background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));
border-radius:5px;
}
</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>3</number>
      </property>
      <property name="leftMargin">
       <number>2</number>
      </property>
      <property name="topMargin">
       <number>2</number>
      </property>
      <property name="rightMargin">
       <number>2</number>
      </property>
      <property name="bottomMargin">
       <number>2</number>
      </property>
      <item>
       <widget class="QLabel" name="CO2_title1_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>切削輸入參數</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_1">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="CO2_edit_group1" native="true">
     <layout class="QGridLayout" name="grid_toolinfo_area1" columnstretch="13,5,13,2,5,11">
      <property name="leftMargin">
       <number>9</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>9</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="5" column="4">
       <widget class="QLabel" name="CO2_ap_unit">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(mm)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="7" column="4">
       <widget class="QLabel" name="CO2_n_unit">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(rev/min)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="6" column="5">
       <spacer name="horizontalSpacer_7">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="CO2_Dc_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(D&lt;span style=&quot; vertical-align:sub;&quot;&gt;c&lt;/span&gt;)刀具直徑 :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="scaledContents">
         <bool>false</bool>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="6" column="4">
       <widget class="QLabel" name="CO2_ae_unit">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(mm)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="QLineEdit" name="CO2_Dc_lineEdit">
        <property name="readOnly">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="CO2_vc_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(v&lt;span style=&quot; vertical-align:sub;&quot;&gt;c&lt;/span&gt;)切削線速 :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="4" column="4">
       <widget class="QLabel" name="CO2_fz_unit">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(mm)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <spacer name="horizontalSpacer_12">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="1">
       <spacer name="horizontalSpacer_11">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="4" column="1">
       <spacer name="horizontalSpacer_14">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_10">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="5" column="1">
       <spacer name="horizontalSpacer_15">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="3" column="4">
       <widget class="QLabel" name="CO2_vc_unit">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(m/mm)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <spacer name="horizontalSpacer_13">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="4" column="5">
       <spacer name="horizontalSpacer_5">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="6" column="1">
       <spacer name="horizontalSpacer_16">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="8" column="1">
       <spacer name="horizontalSpacer_18">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="7" column="1">
       <spacer name="horizontalSpacer_17">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="3" column="2">
       <widget class="QLineEdit" name="CO2_vc_lineEdit">
        <property name="readOnly">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="CO2_K_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);
</string>
        </property>
        <property name="text">
         <string>(K)工件材質 :</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="1" column="2">
       <widget class="QLineEdit" name="CO2_zc_lineEdit"/>
      </item>
      <item row="6" column="2">
       <widget class="QLineEdit" name="CO2_ae_lineEdit">
        <property name="readOnly">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="8" column="4">
       <widget class="QLabel" name="CO2_vf_unit">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(mm/min)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="8" column="0">
       <widget class="QLabel" name="CO2_vf_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(v&lt;span style=&quot; vertical-align:sub;&quot;&gt;f&lt;/span&gt;)進給量 :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="2" column="4">
       <widget class="QLabel" name="CO2_Dc_unit">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(mm)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="6" column="0">
       <widget class="QLabel" name="CO2_ae_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(a&lt;span style=&quot; vertical-align:sub;&quot;&gt;e&lt;/span&gt;)切削寬度 :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="8" column="5">
       <spacer name="horizontalSpacer_9">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="7" column="2">
       <widget class="QLineEdit" name="CO2_n_lineEdit">
        <property name="readOnly">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="8" column="2">
       <widget class="QLineEdit" name="CO2_vf_lineEdit">
        <property name="readOnly">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="7" column="0">
       <widget class="QLabel" name="CO2_n_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(n)主軸轉速 :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="4" column="2">
       <widget class="QLineEdit" name="CO2_fz_lineEdit">
        <property name="readOnly">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="5" column="2">
       <widget class="QLineEdit" name="CO2_ap_lineEdit">
        <property name="readOnly">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="0" column="5">
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="5" column="0">
       <widget class="QLabel" name="CO2_ap_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(a&lt;span style=&quot; vertical-align:sub;&quot;&gt;p&lt;/span&gt;)切削深度 :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="7" column="5">
       <spacer name="horizontalSpacer_8">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="4" column="0">
       <widget class="QLabel" name="CO2_fz_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(f&lt;span style=&quot; vertical-align:sub;&quot;&gt;z&lt;/span&gt;)單刃切量 :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="1" column="5">
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="5" column="5">
       <spacer name="horizontalSpacer_6">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="2" column="5">
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="3" column="5">
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="CO2_zc_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(z&lt;span style=&quot; vertical-align:sub;&quot;&gt;c&lt;/span&gt;)刀刃數量 :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="0" column="2" colspan="3">
       <widget class="QComboBox" name="CO2_K_comboBox">
        <property name="styleSheet">
         <string notr="true">QComboBox {
    background-color: white;
    selection-background-color: #5A7EC7; 
    selection-color: white;
    padding: 2px;
    text-align: center; 
}

QComboBox QAbstractItemView {
    background-color: #0C0C44;
    color: white;
    selection-background-color: #5448B6; 
    selection-color: white; 
}

QComboBox::drop-down {
    border: none;
}

QComboBox QLineEdit {
    background-color: white;
    color: black;
    text-align: center; 
}
</string>
        </property>
       </widget>
      </item>
      <item row="2" column="3">
       <spacer name="horizontalSpacer_26">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="3" column="3">
       <spacer name="horizontalSpacer_27">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="4" column="3">
       <spacer name="horizontalSpacer_30">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="5" column="3">
       <spacer name="horizontalSpacer_31">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="6" column="3">
       <spacer name="horizontalSpacer_36">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="7" column="3">
       <spacer name="horizontalSpacer_37">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="8" column="3">
       <spacer name="horizontalSpacer_38">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="3" colspan="2">
       <spacer name="horizontalSpacer_23">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="CO2_title2" native="true">
     <property name="styleSheet">
      <string notr="true">QWidget#CO2_title2{
background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));
border-radius:5px;
}
</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>3</number>
      </property>
      <property name="leftMargin">
       <number>2</number>
      </property>
      <property name="topMargin">
       <number>2</number>
      </property>
      <property name="rightMargin">
       <number>2</number>
      </property>
      <property name="bottomMargin">
       <number>2</number>
      </property>
      <item>
       <widget class="QLabel" name="CO2_title2_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>切削輸出參數</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_19">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="CO2_edit_group2" native="true">
     <layout class="QGridLayout" name="grid_toolinfo_area1_2" columnstretch="13,5,13,2,5,13">
      <property name="leftMargin">
       <number>9</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>9</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="1" column="5">
       <spacer name="horizontalSpacer_28">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="CO2_Q_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(Q)材料去除率 :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="1" column="4">
       <widget class="QLabel" name="CO2_pc_unit">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(mm)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <spacer name="horizontalSpacer_33">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="4">
       <widget class="QLabel" name="CO2_Q_unit">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(m/mm)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_32">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="3">
       <spacer name="horizontalSpacer_39">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="5">
       <spacer name="horizontalSpacer_24">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="2">
       <widget class="QLineEdit" name="CO2_pc_lineEdit">
        <property name="readOnly">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="CO2_pc_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(p&lt;span style=&quot; vertical-align:sub;&quot;&gt;c&lt;/span&gt;)有效功率 :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="0" column="2">
       <widget class="QLineEdit" name="CO2_Q_lineEdit">
        <property name="readOnly">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="1" column="3">
       <spacer name="horizontalSpacer_40">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="CO2_title3" native="true">
     <property name="styleSheet">
      <string notr="true">QWidget#CO2_title3{
background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));
border-radius:5px;
}
</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <property name="spacing">
       <number>3</number>
      </property>
      <property name="leftMargin">
       <number>2</number>
      </property>
      <property name="topMargin">
       <number>2</number>
      </property>
      <property name="rightMargin">
       <number>2</number>
      </property>
      <property name="bottomMargin">
       <number>2</number>
      </property>
      <item>
       <widget class="QLabel" name="CO2_title3_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>機器參數</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_20">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="CO2_edit_group3" native="true">
     <layout class="QGridLayout" name="grid_toolinfo_area1_3" columnstretch="15,10,19,3,5,10">
      <property name="leftMargin">
       <number>9</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>9</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="1" column="0">
       <widget class="QLabel" name="CO2_EF_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;電力排放係數 :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="0" column="5">
       <spacer name="horizontalSpacer_25">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="0">
       <widget class="QLabel" name="CO2_Pb_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;基本功率負載 :&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_35">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="1">
       <spacer name="horizontalSpacer_34">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="2">
       <widget class="QLineEdit" name="CO2_Pb_lineEdit">
        <property name="readOnly">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item row="0" column="4">
       <widget class="QLabel" name="CO2_Pb_unit">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(kW)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="1" column="4">
       <widget class="QLabel" name="CO2_EF_unit">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;(kg CO2e/kWh)&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item row="1" column="5">
       <spacer name="horizontalSpacer_29">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="0" column="3">
       <spacer name="horizontalSpacer_41">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="3">
       <spacer name="horizontalSpacer_42">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="2">
       <widget class="QComboBox" name="CO2_EF_comboBox">
        <property name="styleSheet">
         <string notr="true">QComboBox {
    background-color: white;
    selection-background-color: #5A7EC7; 
    selection-color: white;
    padding: 2px;
    text-align: center; 
}

QComboBox QAbstractItemView {
    background-color: #0C0C44;
    color: white;
    selection-background-color: #5448B6; 
    selection-color: white; 
}

QComboBox::drop-down {
    border: none;
}

QComboBox QLineEdit {
    background-color: white;
    color: black;
    text-align: center; 
}
</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QWidget" name="CO2_save_close" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="2,4,2,1,2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>5</number>
      </property>
      <property name="topMargin">
       <number>5</number>
      </property>
      <property name="rightMargin">
       <number>8</number>
      </property>
      <property name="bottomMargin">
       <number>8</number>
      </property>
      <item>
       <widget class="QPushButton" name="CO2_auto_btn">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color: #0C0C44;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color: #0C0C44;
	border: 2px solid #7AFEC6;
    color: rgb(255, 255, 255);
	border-radius:5px;
	padding:2px;
}

QPushButton:pressed {
    background-color: gray;
	border: none;
}</string>
        </property>
        <property name="text">
         <string>自動</string>
        </property>
        <property name="iconSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_21">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="CO2_save_btn">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color:#5448B6;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color:#7AFEC6;
     color: rgb(255, 255, 255);
    border:none
}

QPushButton:pressed {
    background-color: gray;
	border:none
}</string>
        </property>
        <property name="text">
         <string>儲存</string>
        </property>
        <property name="iconSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_22">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="CO2_close_btn">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color: #0C0C44;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color: #0C0C44;
	border: 2px solid #7AFEC6;
    color: rgb(255, 255, 255);
	border-radius:5px;
	padding:2px;
}

QPushButton:pressed {
    background-color: gray;
	border: none;
}</string>
        </property>
        <property name="text">
         <string>關閉</string>
        </property>
        <property name="iconSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
