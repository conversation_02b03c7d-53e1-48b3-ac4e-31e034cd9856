import numpy as np
from app.models.co2_data import *


def _create_test_co2_data():
    return {
        'MS_CO2_K': 5,
        'MS_CO2_zc': 4, 
        'MS_CO2_Dc': 6.0, 
        'MS_CO2_vc': 100.0,
        'MS_CO2_fz': 0.05, 
        'MS_CO2_ap': 1.0, 
        'MS_CO2_ae': 3.0, 
        'MS_CO2_n': 5305.2,
        'MS_CO2_vf': 1061.04, 
        'MS_CO2_Q': 3.183, 
        'MS_CO2_Pc': 1.0, 
        'MS_CO2_Pb': 0.3, # unit: kW  
        'MS_CO2_EF': 0.5 , # unit: kgCO2/kWh
        'init_status' : 0
    }



def test_CO2Data_clear_accumulation_expect_set_accumulation_to_zero():
    co2_data = CO2Data()
    co2_data.CO2_accumulation = 100

    co2_data.clear_accumulation()

    assert co2_data.CO2_accumulation == 0.0, \
        f"[{__name__}] Failed: accumulation is {co2_data.CO2_accumulation} instead of zero"


def test_CO2Data_caculate_co2_g_idle_expect_using_base_power():
    co2_data = CO2Data()
    test_txt_ten = 0
    test_SampleRate = 10000
    test_co2_data = _create_test_co2_data()
    expect_result = test_co2_data["MS_CO2_Pb"] * test_co2_data["MS_CO2_EF"] /test_SampleRate /3.6
    
    result = co2_data.caculate_co2_g(test_txt_ten, test_SampleRate, test_co2_data)

    assert np.isclose(result, expect_result, rtol=0, atol=1e-10), \
        f"[{__name__}] Failed: result is {result}, expect {expect_result}"


def test_CO2Data_caculate_co2_g_cutting_expect_using_base_plus_consumed_power():
    co2_data = CO2Data()
    test_txt_ten = 10
    test_SampleRate = 10000
    test_co2_data = _create_test_co2_data()
    expect_result = (test_co2_data["MS_CO2_Pc"] + test_co2_data["MS_CO2_Pb"]) \
        * test_co2_data["MS_CO2_EF"] /test_SampleRate /3.6
    
    result = co2_data.caculate_co2_g(test_txt_ten, test_SampleRate, test_co2_data)

    assert np.isclose(result, expect_result, rtol=0, atol=1e-10), \
        f"[{__name__}] Failed: result is {result}, expect {expect_result}"