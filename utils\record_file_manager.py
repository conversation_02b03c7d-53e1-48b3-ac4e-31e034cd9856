import os
from . import logger

# TODO: 不要分那麼多模組，可能試著跟TextFileManager合併
class RecordFileManager:
    """在指定的目錄中創建文字檔案(唯一)，並提供檔案操作功能"""

    def __init__(self, directory, filename):
        """initialize file manager"""
        # directory
        self.directory = directory
        if not os.path.exists(self.directory):
            try:
                os.makedirs(self.directory)
            except Exception as e:
                logger.error(f"目錄 '{self.directory}' 創建失敗：{e}")
        
        # file name and handle
        self.filename = os.path.join(directory, filename)
        # create file
        try:
            # check if file exists (policy: not to overwrite existing file)
            if os.path.exists(self.filename):
                logger.error(f"檔案 '{self.filename}' 已存在")
                raise Exception(f"File already exists: {self.filename}")
            self.file_handle = open(self.filename, "w+", encoding="utf-8")
        except Exception as e:
            logger.error(f"檔案 '{filename}' 建立失敗")

    def add_text(self, text):
        """在文字檔中新增文字。"""
        try:
            self.file_handle.write(text)
            self.file_handle.flush()
            os.fsync(self.file_handle.fileno())
        except Exception as e:
            logger.error(f"新增文字到檔案 '{self.filename}' 失敗：{e}")

    def close_file(self):
        """關閉文字檔 (記得要呼叫)"""
        try:
            self.file_handle.close()
        except Exception as e:
            logger.error(f"關閉檔案 '{self.filename}' 失敗：{e}") 