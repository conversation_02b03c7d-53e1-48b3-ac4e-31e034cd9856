import pytest
import numpy as np
from app.models.tool_decoder_draft import <PERSON><PERSON><PERSON><PERSON>ode<PERSON>



def test_ToolDecoder_decode_hex16str_to_int():
    test_data = ["0", "00a", "b2"]
    expect_result = [0, 10, 178]
    tool_decoder = ToolDecoder()

    actual_result = []
    for item in test_data:
        actual_result.append(tool_decoder.decode_hex16str_to_int(item))
    
    assert len(actual_result) == len(expect_result), \
        f"[{__name__}] Failed: returned {len(actual_result)} items, expected {len(expect_result)} items."
    assert all([a == b for a, b in zip(actual_result, expect_result)]), \
        f"[{__name__}] Failed: returned {repr(actual_result)}, expected {repr(expect_result)}"


def test_ToolDecoder_decode_hex16str_to_float_defaultDivisor():
    tool_decoder = ToolDecoder()

    result = tool_decoder.decode_hex16str_to_float("0001")
    
    assert result == 1 / tool_decoder._HEX16_TO_FLOAT_DIVISOR, \
        f"{__name__} failed: does not use default divisor when no divisor given."


def test_ToolDecoder_decode_hex16str_to_float_divisor0():
    tool_decoder = ToolDecoder()

    try:
        result = tool_decoder.decode_hex16str_to_float("0001", 0)
    except Exception as e:
        pytest.fail(f"{__name__} raised {type(e).__name__}: {e} when divisor is 0. Should use 1.0 instead.")
    
    
    assert result == 1.0, \
        f"{__name__} failed: not using 1.0 instead when divisor is 0."


def test_ToolDecoder_decode_hex16str_to_float_positiveDivisor():
    test_data = ["0001", "0003", "0001"]
    test_divisor = [1, 2, 3]
    expect_result = [1, 1.5, 1/3.0]
    tool_decoder = ToolDecoder()

    actual_result = []
    for item, divisor in zip(test_data, test_divisor):
        actual_result.append(tool_decoder.decode_hex16str_to_float(item, divisor))

    assert len(actual_result) == len(expect_result), \
        f"[{__name__}] Failed: returned {len(actual_result)} items, expected {len(expect_result)} items."
    assert all([a == b for a, b in zip(actual_result, expect_result)]), \
        f"[{__name__}] Failed: returned {repr(actual_result)}, expected {repr(expect_result)}"


def test_ToolDecoder_parse_charging_and_sleeping_flags_1digitHex():
    # if last bit is 0, it is charging; if the bit before last bit is 1, it is sleeping.
    expected = [
        (False, False), # 0b0000 = 0
        (True,  False), # 0b0001 = 1
        (False, True),  # 0b0010 = 2
        (True,  True),  # 0b0011 = 3
        (False, False), # 0b0100 = 4
        (True,  False), # 0b0101 = 5
        (False, True),  # 0b0110 = 6
        (True,  True),  # 0b0111 = 7
        (False, False), # 0b1000 = 8
        (True,  False), # 0b1001 = 9
        (False, True),  # 0b1010 = 10
        (True,  True),  # 0b1011 = 11
        (False, False), # 0b1100 = 12
        (True,  False), # 0b1101 = 13
        (False, True),  # 0b1110 = 14
        (True,  True),  # 0b1111 = 15
    ]
    for code in range(16):
        result = ToolDecoder.parse_charging_and_sleeping_flags(code)
        assert result == expected[code], \
            f"{__name__} failed: status_code={code}: got {result}, expected {expected[code]}"


def test_ToolDecoder_decode_low_freq_tare_reference():
    hex_data = '0'*32 + "00010002000304050603c049ef68aa99"
    offset = 32

    ADXL_X, ADXL_Y, ADXL_Z, temperature, wifi_RSSI, isCharging, isSleeping, MAC = \
        ToolDecoder.decode_low_freq_tare_reference(hex_data, offset)

    assert ADXL_X == 1, f"{__name__} failed: ADXL_X={ADXL_X}, expected 1"  #X=0001
    assert ADXL_Y == 2, f"{__name__} failed: ADXL_Y={ADXL_Y}, expected 2"  #Y=0002
    assert ADXL_Z == 3, f"{__name__} failed: ADXL_Z={ADXL_Z}, expected 3"  #Z=0003
    assert temperature == 9, f"{__name__} failed: temperature={temperature}, expected 9"  #T=05+04=9
    assert wifi_RSSI == 6, f"{__name__} failed: wifi_RSSI={wifi_RSSI}, expected 6"  #RSSI=06
    assert isCharging is True and isSleeping is True, \
        f"{__name__} failed: isCharging={isCharging}, isSleeping={isSleeping}, expected True and True"  #flag=3
    assert MAC == "c049ef68aa99", f"{__name__} failed: MAC={MAC}, expected \"c049ef68aa99\""  #MAC


def test_ToolDecoder_decode_high_freq_tare_reference():
    hex_data = '0'*32 + "000100020003000a00b1"
    offset = 32
    divider = ToolDecoder._HEX16_TO_FLOAT_DIVISOR
    expect_x = 1 / divider
    expect_y = 2 / divider
    expect_xy = (expect_x**2 + expect_y**2) ** 0.5
    expect_tension = 3 / divider
    expect_torsion = 10 / divider
    expect_battery = 177 / divider

    result_x, result_y, result_xy, result_tension, result_torsion, result_battery = \
        ToolDecoder.decode_high_freq_tare_reference(hex_data, offset,)

    assert result_x == expect_x, f"{__name__} failed: x={result_x}, expected {expect_x}"
    assert result_y == expect_y, f"{__name__} failed: y={result_y}, expected {expect_y}"
    assert result_xy == expect_xy, f"{__name__} failed: xy={result_xy}, expected {expect_xy}"
    assert result_tension == expect_tension, f"{__name__} failed: tension={result_tension}, expected {expect_tension}"
    assert result_torsion == expect_torsion, f"{__name__} failed: torsion={result_torsion}, expected {expect_torsion}"
    assert result_battery == expect_battery, f"{__name__} failed: battery={result_battery}, expected {expect_battery}"

