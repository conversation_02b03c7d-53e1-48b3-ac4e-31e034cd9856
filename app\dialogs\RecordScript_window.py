# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'RecordScript_window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
from .. import logger

import system_image.system_image_rc

# ==============================================================================
# DELEGATE CLASSES
# ==============================================================================

class ButtonDelegate(QStyledItemDelegate):
    """
    A delegate that paints a custom-styled, square, rounded button.
    """
    sig_add_tool = Signal(int, str, int)

    def __init__(self, parent=None):
        super(ButtonDelegate, self).__init__(parent)
        self.pressed_index = None
        
    def paint(self, painter, option, index):
        painter.save()
        painter.setRenderHint(QPainter.Antialiasing)

        is_hovered = index.data(Qt.UserRole + 1)
        if is_hovered:
            painter.fillRect(option.rect, QColor("#5448B6"))

        if index.column() == 1:
            if self.pressed_index == index:
                bg_color = QColor("grey")
            elif option.state & QStyle.State_MouseOver:
                bg_color = QColor("#7AFEC6")
            else:
                bg_color = QColor("#5448B6")
            
            cell_rect = option.rect
            margin = 6
            size = min(cell_rect.width(), cell_rect.height()) - (margin * 2)
            button_rect = QRect(0, 0, size, size)
            button_rect.moveCenter(cell_rect.center())
            
            painter.setBrush(bg_color)
            painter.setPen(Qt.NoPen)
            corner_radius = 4
            painter.drawRoundedRect(button_rect, corner_radius, corner_radius)

            button_font = QFont("Arial", 13, QFont.Bold)
            painter.setFont(button_font)
            painter.setPen(QColor("white"))
            text = index.model().data(index, Qt.DisplayRole)
            painter.drawText(button_rect, Qt.AlignCenter, text)
        else:
            # ✅ FIX: Always use white text for the tool name column.
            painter.setPen(QColor("white"))
            text = index.data(Qt.DisplayRole)
            painter.setFont(option.font)
            text_rect = option.rect.adjusted(8, 0, 0, 0)
            painter.drawText(text_rect, Qt.AlignVCenter | Qt.AlignLeft, text)

        painter.restore()

    def editorEvent(self, event, model, option, index):
        if index.column() != 1:
            return super(ButtonDelegate, self).editorEvent(event, model, option, index)

        if not option.rect.contains(event.pos()):
            if self.pressed_index is not None:
                old_pressed_index = self.pressed_index
                self.pressed_index = None
                self.parent().update(old_pressed_index)
            return False

        if event.type() == QEvent.MouseButtonPress and event.button() == Qt.LeftButton:
            self.pressed_index = index
            self.parent().update(index)
            return True

        if event.type() == QEvent.MouseButtonRelease and event.button() == Qt.LeftButton:
            if self.pressed_index == index:
                tool_id = getattr(index.model().itemFromIndex(index), "tool_id", -1)
                toolname_index = index.model().index(index.row(), 0)
                tool_name = index.model().data(toolname_index, Qt.DisplayRole)
                self.sig_add_tool.emit(tool_id, tool_name, 30)
                self.pressed_index = None
                self.parent().update(index)
                return True

        return False

class PlanListDelegate(QStyledItemDelegate):
    """
    A delegate for the plan list table that handles:
    1. A QTimeEdit for the 'duration' column.
    2. Correct vertical alignment and display formatting for all columns.
    """
    sig_duration_changed = Signal()

    def paint(self, painter, option, index):
        painter.save()
        painter.setRenderHint(QPainter.Antialiasing)

        painter.fillRect(option.rect, QColor("black"))
        painter.setPen(QColor("white"))
        painter.setFont(option.font)

        if index.column() == 0:
            text = index.data(Qt.DisplayRole)
            text_rect = option.rect.adjusted(8, 0, 0, 0)
            painter.drawText(text_rect, Qt.AlignVCenter | Qt.AlignLeft, text)
        elif index.column() == 1:
            pass
        elif index.column() == 2:
            try:
                seconds = int(index.data(Qt.DisplayRole))
            except (ValueError, TypeError):
                seconds = 0
            
            h = seconds // 3600
            m = (seconds % 3600) // 60
            s = seconds % 60
            formatted_text = f"{h:02d}:{m:02d}:{s:02d}"
            painter.drawText(option.rect, Qt.AlignVCenter | Qt.AlignLeft, formatted_text)

        painter.restore()

    def createEditor(self, parent, option, index):
        if index.column() != 1:
            return super().createEditor(parent, option, index)
            
        editor = QTimeEdit(parent)
        editor.setDisplayFormat("hh:mm:ss")
        editor.setButtonSymbols(QTimeEdit.NoButtons)
        editor.setStyleSheet("""
            QTimeEdit {
                border: 0px;
                background-color: rgb(0, 0, 0);
                color: rgb(255, 255, 255);
            }
            QTimeEdit:focus {
                border: 1px solid #5448B6;
                background-color: #0C0C44;
            }
        """)
        model = self.parent().model()
        editor.timeChanged.connect(lambda time: self.setModelData(editor, model, index))
        return editor

    def setEditorData(self, editor, index):
        if index.column() != 1:
            return super().setEditorData(editor, index)
        total_seconds = index.model().data(index, Qt.EditRole)
        try:
            total_seconds = int(total_seconds)
        except (ValueError, TypeError):
            total_seconds = 0
        h, m, s = total_seconds // 3600, (total_seconds % 3600) // 60, total_seconds % 60
        editor.setTime(QTime(h, m, s))

    def setModelData(self, editor, model, index):
        if index.column() != 1:
            return super().setModelData(editor, model, index)
        time = editor.time()
        total_seconds = (time.hour() * 3600) + (time.minute() * 60) + time.second()
        old_value = model.data(index, Qt.EditRole)
        if str(old_value) != str(total_seconds):
            model.setData(index, total_seconds, Qt.EditRole)
            self.sig_duration_changed.emit()

    def updateEditorGeometry(self, editor, option, index):
        ideal_width = editor.sizeHint().width()
        cell_rect = option.rect
        editor_rect = QRect(0, 0, ideal_width, cell_rect.height())
        editor_rect.moveLeft(cell_rect.left())
        editor_rect.moveTop(cell_rect.top())
        editor.setGeometry(editor_rect)

# ==============================================================================
# AUTO-GENERATED UI CLASS (DO NOT EDIT MANUALLY)
# ==============================================================================

class Ui_RecordScript_window(object):
    def setupUi(self, RecordScript_window):
        if not RecordScript_window.objectName():
            RecordScript_window.setObjectName(u"RecordScript_window")
        RecordScript_window.setEnabled(True)
        RecordScript_window.resize(570, 596)
        font = QFont()
        font.setFamily(u"Arial Black")
        RecordScript_window.setFont(font)
        RecordScript_window.setAutoFillBackground(False)
        RecordScript_window.setStyleSheet(u"QWidget#RecordScript_window\n"
"{\n"
"background-color: #0C0C44;\n"
"border: 2px solid #5448B6;\n"
"}")
        RecordScript_window.setInputMethodHints(Qt.ImhNone)
        self.verticalLayout_3 = QVBoxLayout(RecordScript_window)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.record_script_window_title = QWidget(RecordScript_window)
        self.record_script_window_title.setObjectName(u"record_script_window_title")
        font1 = QFont()
        font1.setFamily(u"Arial Black")
        font1.setPointSize(12)
        self.record_script_window_title.setFont(font1)
        self.record_script_window_title.setStyleSheet(u"QWidget#record_script_window_title{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self._2 = QHBoxLayout(self.record_script_window_title)
        self._2.setObjectName(u"_2")
        self._2.setContentsMargins(-1, -1, 5, -1)
        self.title_label = QLabel(self.record_script_window_title)
        self.title_label.setObjectName(u"title_label")
        font2 = QFont()
        font2.setFamily(u"Arial Black")
        font2.setPointSize(12)
        font2.setBold(False)
        font2.setWeight(50)
        self.title_label.setFont(font2)
        self.title_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.title_label.setTextFormat(Qt.AutoText)
        self._2.addWidget(self.title_label)
        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        self._2.addItem(self.horizontalSpacer_5)
        self.close_button = QPushButton(self.record_script_window_title)
        self.close_button.setObjectName(u"close_button")
        self.close_button.setMinimumSize(QSize(20, 20))
        self.close_button.setMaximumSize(QSize(20, 20))
        self.close_button.setBaseSize(QSize(20, 20))
        font3 = QFont()
        font3.setPointSize(20)
        self.close_button.setFont(font3)
        self.close_button.setAutoFillBackground(False)
        self.close_button.setStyleSheet(u"QPushButton {\n"
"    border-image: url(:/btn_close/btn_close_0.png) no-repeat right center;\n"
"    border: none;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    border-image: url(:/btn_close/btn_close_1.png) no-repeat right center;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    border-image: url(:/btn_close/btn_close_2.png) no-repeat right center;\n"
"}")
        self.close_button.setIconSize(QSize(15, 15))
        self.close_button.setCheckable(False)
        self.close_button.setAutoDefault(False)
        self.close_button.setFlat(False)
        self._2.addWidget(self.close_button)
        self._2.setStretch(0, 1)
        self._2.setStretch(1, 100)
        self._2.setStretch(2, 1)
        self.verticalLayout_3.addWidget(self.record_script_window_title)
        self.mode_group = QWidget(RecordScript_window)
        self.mode_group.setObjectName(u"mode_group")
        self.gridLayout = QGridLayout(self.mode_group)
        self.gridLayout.setSpacing(20)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setContentsMargins(16, 16, -1, 12)
        self.timed_mode_record_check = QCheckBox(self.mode_group)
        self.timed_mode_record_check.setObjectName(u"timed_mode_record_check")
        font4 = QFont()
        font4.setFamily(u"Arial Black")
        font4.setPointSize(10)
        font4.setBold(False)
        font4.setWeight(50)
        self.timed_mode_record_check.setFont(font4)
        self.timed_mode_record_check.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"QCheckBox::indicator:unchecked{image: url(:/btn_check/btn_check_s0_0.png);}\n"
"QCheckBox::indicator:unchecked:hover {image: url(:/btn_check/btn_check_s0_1.png);}\n"
"QCheckBox::indicator:checked{image: url(:/btn_check/btn_check_s1_0.png);}\n"
"QCheckBox::indicator:checked:hover {image: url(:/btn_check/btn_check_s1_1.png);}\n"
"QCheckBox::indicator:disabled {image: url(:/btn_check/btn_check_s0_0.png);}")
        self.gridLayout.addWidget(self.timed_mode_record_check, 4, 1, 1, 1)
        self.horizontalSpacer_10 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.gridLayout.addItem(self.horizontalSpacer_10, 0, 1, 1, 1)
        self.machine_mode_record_check = QCheckBox(self.mode_group)
        self.machine_mode_record_check.setObjectName(u"machine_mode_record_check")
        self.machine_mode_record_check.setFont(font4)
        self.machine_mode_record_check.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"QCheckBox::indicator:unchecked{image: url(:/btn_check/btn_check_s0_0.png);}\n"
"QCheckBox::indicator:unchecked:hover {image: url(:/btn_check/btn_check_s0_1.png);}\n"
"QCheckBox::indicator:checked{image: url(:/btn_check/btn_check_s1_0.png);}\n"
"QCheckBox::indicator:checked:hover {image: url(:/btn_check/btn_check_s1_1.png);}\n"
"QCheckBox::indicator:disabled {image: url(:/btn_check/btn_check_s0_0.png);}")
        self.gridLayout.addWidget(self.machine_mode_record_check, 2, 1, 1, 1)
        self.standard_mode_radio = QRadioButton(self.mode_group)
        self.standard_mode_radio.setObjectName(u"standard_mode_radio")
        self.standard_mode_radio.setFont(font4)
        self.standard_mode_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
"QRadioButton::indicator:unchecked{image: url(:/btn_radio/btn_radio_s0_0.png);}\n"
"QRadioButton::indicator:unchecked:hover {image: url(:/btn_radio/btn_radio_s0_1.png);}\n"
"QRadioButton::indicator:checked{image: url(:/btn_radio/btn_radio_s1_0.png);}\n"
"QRadioButton::indicator:checked:hover {image: url(:/btn_radio/btn_radio_s1_1.png);}")
        self.gridLayout.addWidget(self.standard_mode_radio, 0, 0, 1, 1)
        self.machine_mode_radio = QRadioButton(self.mode_group)
        self.machine_mode_radio.setObjectName(u"machine_mode_radio")
        self.machine_mode_radio.setFont(font4)
        self.machine_mode_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
"QRadioButton::indicator:unchecked{image: url(:/btn_radio/btn_radio_s0_0.png);}\n"
"QRadioButton::indicator:unchecked:hover {image: url(:/btn_radio/btn_radio_s0_1.png);}\n"
"QRadioButton::indicator:checked{image: url(:/btn_radio/btn_radio_s1_0.png);}\n"
"QRadioButton::indicator:checked:hover {image: url(:/btn_radio/btn_radio_s1_1.png);}")
        self.gridLayout.addWidget(self.machine_mode_radio, 2, 0, 1, 1)
        self.timed_mode_radio = QRadioButton(self.mode_group)
        self.timed_mode_radio.setObjectName(u"timed_mode_radio")
        self.timed_mode_radio.setFont(font4)
        self.timed_mode_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
"QRadioButton::indicator:unchecked{image: url(:/btn_radio/btn_radio_s0_0.png);}\n"
"QRadioButton::indicator:unchecked:hover {image: url(:/btn_radio/btn_radio_s0_1.png);}\n"
"QRadioButton::indicator:checked{image: url(:/btn_radio/btn_radio_s1_0.png);}\n"
"QRadioButton::indicator:checked:hover {image: url(:/btn_radio/btn_radio_s1_1.png);}")
        self.gridLayout.addWidget(self.timed_mode_radio, 4, 0, 1, 1)
        self.verticalLayout_3.addWidget(self.mode_group)
        self.RecordScript_ToolList_group = QWidget(RecordScript_window)
        self.RecordScript_ToolList_group.setObjectName(u"RecordScript_ToolList_group")
        sizePolicy = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(3)
        sizePolicy.setHeightForWidth(self.RecordScript_ToolList_group.sizePolicy().hasHeightForWidth())
        self.RecordScript_ToolList_group.setSizePolicy(sizePolicy)
        self.RecordScript_ToolList_layout = QGridLayout(self.RecordScript_ToolList_group)
        self.RecordScript_ToolList_layout.setObjectName(u"RecordScript_ToolList_table")
        self.RecordScript_ToolList_layout.setContentsMargins(-1, 6, -1, 3)
        self.RecordScript_ToolList_table = QTableView(self.RecordScript_ToolList_group)
        self.RecordScript_ToolList_table.setObjectName(u"RecordScript_ToolList_Element_List_table")
        self.RecordScript_ToolList_table.setStyleSheet(u"background-color: rgb(0, 0, 0);\nborder:none")
        self.RecordScript_ToolList_layout.addWidget(self.RecordScript_ToolList_table, 0, 0, 1, 1)
        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.RecordScript_ToolList_layout.addItem(self.horizontalSpacer_4, 0, 1, 1, 1)
        self.verticalLayout_3.addWidget(self.RecordScript_ToolList_group)
        self.RecordScript_PlanList_group = QWidget(RecordScript_window)
        self.RecordScript_PlanList_group.setObjectName(u"RecordScript_PlanList_group")
        sizePolicy1 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(5)
        sizePolicy1.setHeightForWidth(self.RecordScript_PlanList_group.sizePolicy().hasHeightForWidth())
        self.RecordScript_PlanList_group.setSizePolicy(sizePolicy1)
        self.RecordScript_PlanList = QVBoxLayout(self.RecordScript_PlanList_group)
        self.RecordScript_PlanList.setObjectName(u"RecordScript_PlanList")
        self.RecordScript_PlanList.setContentsMargins(-1, 6, -1, 12)
        self.RecordScript_PlanList_title_group = QWidget(self.RecordScript_PlanList_group)
        self.RecordScript_PlanList_title_group.setObjectName(u"RecordScript_PlanList_title_group")
        self.RecordScript_PlanList_label = QGridLayout(self.RecordScript_PlanList_title_group)
        self.RecordScript_PlanList_label.setObjectName(u"RecordScript_PlanList_label")
        self.RecordScript_PlanList_label.setContentsMargins(3, -1, -1, 6)
        self.Tool_Name = QLabel(self.RecordScript_PlanList_title_group)
        self.Tool_Name.setObjectName(u"Tool_Name")
        self.Tool_Name.setFont(font4)
        self.Tool_Name.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.RecordScript_PlanList_label.addWidget(self.Tool_Name, 1, 0, 1, 1)
        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.RecordScript_PlanList_label.addItem(self.horizontalSpacer, 1, 1, 1, 1)
        self.time = QLabel(self.RecordScript_PlanList_title_group)
        self.time.setObjectName(u"time")
        self.time.setFont(font4)
        self.time.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.RecordScript_PlanList_label.addWidget(self.time, 1, 2, 1, 1)
        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.RecordScript_PlanList_label.addItem(self.horizontalSpacer_2, 1, 3, 1, 1)
        self.Totaltime = QLabel(self.RecordScript_PlanList_title_group)
        self.Totaltime.setObjectName(u"Totaltime")
        self.Totaltime.setFont(font4)
        self.Totaltime.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.RecordScript_PlanList_label.addWidget(self.Totaltime, 1, 4, 1, 1)
        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        self.RecordScript_PlanList_label.addItem(self.horizontalSpacer_3, 1, 5, 1, 1)
        self.RecordScript_PlanList_label.setColumnStretch(0, 2)
        self.RecordScript_PlanList_label.setColumnStretch(1, 3)
        self.RecordScript_PlanList_label.setColumnStretch(2, 2)
        self.RecordScript_PlanList_label.setColumnStretch(3, 2)
        self.RecordScript_PlanList_label.setColumnStretch(4, 2)
        self.RecordScript_PlanList_label.setColumnStretch(5, 4)
        self.RecordScript_PlanList.addWidget(self.RecordScript_PlanList_title_group)
        self.RecordScript_PlanList_table = QGridLayout()
        self.RecordScript_PlanList_table.setObjectName(u"RecordScript_PlanList_table")
        self.RecordScript_PlanList_table.setHorizontalSpacing(12)
        self.RecordScript_PlanList_Element_List_table = QTableView(self.RecordScript_PlanList_group)
        self.RecordScript_PlanList_Element_List_table.setObjectName(u"RecordScript_PlanList_Element_List_table")
        self.RecordScript_PlanList_Element_List_table.setStyleSheet(u"background-color: rgb(0, 0, 0);\nborder:none")
        self.RecordScript_PlanList_table.addWidget(self.RecordScript_PlanList_Element_List_table, 0, 0, 3, 1)
        self.reset_button = QPushButton(self.RecordScript_PlanList_group)
        self.reset_button.setObjectName(u"reset_button")
        self.reset_button.setFont(font4)
        self.reset_button.setStyleSheet(u"QPushButton {color: rgb(255, 255, 255);background-color: #0C0C44;border: 2px solid #5448B6;border-radius:5px;padding:2px;}\n"
"QPushButton:hover {background-color: #0C0C44;border: 2px solid #7AFEC6;color: rgb(255, 255, 255);border-radius:5px;padding:2px;}\n"
"QPushButton:pressed {background-color: gray;border: none;}")
        self.reset_button.setCheckable(True)
        self.RecordScript_PlanList_table.addWidget(self.reset_button, 0, 1, 1, 1)
        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)
        self.RecordScript_PlanList_table.addItem(self.verticalSpacer, 1, 1, 1, 1)
        self.save_button = QPushButton(self.RecordScript_PlanList_group)
        self.save_button.setObjectName(u"save_button")
        self.save_button.setFont(font4)
        self.save_button.setStyleSheet(u"QPushButton {color: rgb(255, 255, 255);background-color:#5448B6;border: 2px solid #5448B6;border-radius:5px;padding:2px;}\n"
"QPushButton:hover {background-color:#7AFEC6; color: rgb(255, 255, 255);border:none}\n"
"QPushButton:pressed {background-color: gray;border:none}")
        self.save_button.setCheckable(True)
        self.RecordScript_PlanList_table.addWidget(self.save_button, 2, 1, 1, 1)
        self.RecordScript_PlanList_table.setColumnStretch(0, 7)
        self.RecordScript_PlanList_table.setColumnStretch(1, 1)
        self.RecordScript_PlanList.addLayout(self.RecordScript_PlanList_table)
        self.verticalLayout_3.addWidget(self.RecordScript_PlanList_group)

        self.RecordScript_PlanList_Element_List_table.verticalScrollBar().setStyleSheet("""
            QScrollBar:vertical {
                background: #0C0C44;
                width: 5px;
                border: none;
                margin: 0px 0px 0px 0px;
            }
            QScrollBar::handle:vertical {
                background: #7AFEC6;
                min-height: 20px;
                border-radius: 2px;
            }
            QScrollBar::add-line:vertical, 
            QScrollBar::sub-line:vertical {
                background: none;
                height: 0px;
            }
            QScrollBar::sub-page:vertical, 
            QScrollBar::add-page:vertical {
                background: #0C0C44;
            }
        """)
        self.RecordScript_PlanList_Element_List_table.setVerticalScrollMode(QAbstractItemView.ScrollPerPixel)

        self.RecordScript_ToolList_table.verticalScrollBar().setStyleSheet("""
            QScrollBar:vertical {
                background: #0C0C44;
                width: 5px;
                border: none;
                margin: 0px 0px 0px 0px;
            }
            QScrollBar::handle:vertical {
                background: #7AFEC6;
                min-height: 20px;
                border-radius: 2px;
            }
            QScrollBar::add-line:vertical, 
            QScrollBar::sub-line:vertical {
                background: none;
                height: 0px;
            }
            QScrollBar::sub-page:vertical, 
            QScrollBar::add-page:vertical {
                background: #0C0C44;
            }
        """)
        self.RecordScript_ToolList_table.setVerticalScrollMode(QAbstractItemView.ScrollPerPixel)

        self.retranslateUi(RecordScript_window)
        self.close_button.setDefault(False)
        QMetaObject.connectSlotsByName(RecordScript_window)

    def retranslateUi(self, RecordScript_window):
        RecordScript_window.setWindowTitle(QCoreApplication.translate("RecordScript_window", u"Form", None))
        self.title_label.setText(QCoreApplication.translate("RecordScript_window", u"\u9304\u88fd\u8173\u672c", None))
        self.close_button.setText("")
        self.close_button.setShortcut("")
        self.timed_mode_record_check.setText(QCoreApplication.translate("RecordScript_window", u"\u9304\u88fd", None))
        self.machine_mode_record_check.setText(QCoreApplication.translate("RecordScript_window", u"\u9304\u88fd", None))
        self.standard_mode_radio.setText(QCoreApplication.translate("RecordScript_window", u"\u6a19\u6e96\u6a21\u5f0f", None))
        self.machine_mode_radio.setText(QCoreApplication.translate("RecordScript_window", u"\u6a5f\u5668\u901a\u8a0a\u6a21\u5f0f", None))
        self.timed_mode_radio.setText(QCoreApplication.translate("RecordScript_window", u"\u6642\u9593\u8173\u672c\u6a21\u5f0f", None))
        self.Tool_Name.setText(QCoreApplication.translate("RecordScript_window", u"\u5200\u628a\u540d\u7a31", None))
        self.time.setText(QCoreApplication.translate("RecordScript_window", u"\u671f\u9593", None))
        self.Totaltime.setText(QCoreApplication.translate("RecordScript_window", u"\u7e3d\u6642\u9593", None))
        self.reset_button.setText(QCoreApplication.translate("RecordScript_window", u"\u91cd\u7f6e", None))
        self.save_button.setText(QCoreApplication.translate("RecordScript_window", u"\u5132\u5b58", None))

# ==============================================================================
# MAIN APPLICATION CLASS
# ==============================================================================

class RecordScriptWindow_implementation(QWidget):

    sig_save_start_script = Signal(str, bool, list)
    sig_close_window = Signal()
    sig_show_remind_window = Signal(str)

    def __init__(self, parent, tool_list):
        super(RecordScriptWindow_implementation, self).__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_StyledBackground, True)
        # create ui
        self.ui = Ui_RecordScript_window()
        self.ui.setupUi(self)
        self.setupToolList(tool_list)
        self.setupPlanList()

        # Connect signals in ui
        self.btn_delegate.sig_add_tool.connect(self.add_planlist_item)
        self.plan_delegate.sig_duration_changed.connect(self.update_total_times)

        # 選擇預設標準模式
        self.ui.standard_mode_radio.setChecked(True)

        # 監聽 radio 狀態變化
        self.ui.mode_button_group = QButtonGroup()
        self.ui.mode_button_group.addButton(self.ui.standard_mode_radio, 0)
        self.ui.mode_button_group.addButton(self.ui.machine_mode_radio, 1)
        self.ui.mode_button_group.addButton(self.ui.timed_mode_radio, 2)
        self.ui.mode_button_group.buttonToggled.connect(self.update_mode_button_group)

        # 綁定按鈕的點擊事件
        self.ui.save_button.clicked.connect(self.save_script)
        self.ui.reset_button.clicked.connect(self.reset_script)
        self.ui.close_button.clicked.connect(self.sig_close_window)
        
        # 模式: 0: standard, 1: machine, 2: timed
        self.modes = ["standard", "machine", "timed"]
        self.mode_id = 0
        self.script_buffer = []

    def save_script(self):
        """save script data from ui and emit signal to start running the script"""
        try:
            logger.info("Saving script")
            
            # 取得模式
            self.mode_id = self.ui.mode_button_group.checkedId()
            mode = self.modes[self.mode_id]
            
            # 取得錄製 flag
            if self.mode_id != 0:
                checkbox = getattr(self.ui, f"{mode}_mode_record_check")
                record_enabled = checkbox.isChecked()
            else:
                record_enabled = False

            # 取得腳本資料
            script_buffer = []
            for row in range(self.planlist_model.rowCount()):
                toolname_item = self.planlist_model.item(row, 0)
                time = self.ui.RecordScript_PlanList_Element_List_table.indexWidget(self.planlist_model.index(row, 1)).time()

                tool_id = getattr(toolname_item, "tool_id", None)
                duration = (time.hour() * 3600) + (time.minute() * 60) + time.second()
                
                logger.info(f"Tool: {toolname_item.text()}, Duration: {duration}")
                script_buffer.append({"tool_id": tool_id, "duration": duration})
                self.acceptDrops
            logger.info(script_buffer)

            # 發送signal，開始執行腳本
            self.sig_save_start_script.emit(mode, record_enabled, list(script_buffer))

        except Exception as e:
            logger.error(f"Error saving script: {e}")
            # clear script
            self.reset_script()
            #show error message
            self.sig_show_remind_window.emit(f"無法儲存腳本：{e}")

        finally:
            # close window
            self.sig_close_window.emit()


    def reset_script(self):
        """重置腳本"""
        # 重置模式和錄製
        self.mode_id = 0
        self.ui.standard_mode_radio.setChecked(True)
        self.ui.machine_mode_radio.setChecked(False)
        self.ui.timed_mode_radio.setChecked(False)
        self.ui.machine_mode_record_check.setChecked(False)
        self.ui.timed_mode_record_check.setChecked(False)

        # 清空腳本
        self.planlist_model.removeRows(0, self.planlist_model.rowCount())
        self.total_time = 0
        self.script_buffer = []

    def update_mode_button_group(self):
        """disable/enable checkboxes in the mode button group"""
        machine_mode_checked =  self.ui.machine_mode_radio.isChecked()
        time_mode_checked = self.ui.timed_mode_radio.isChecked()
        self.ui.machine_mode_record_check.setEnabled(machine_mode_checked)
        self.ui.timed_mode_record_check.setEnabled(time_mode_checked)


    def setupToolList(self, tool_list):
        """Setup data for tool list"""
        # Create model and delegates, attaching them to the ui object for access
        self.toollist_model = QStandardItemModel(len(tool_list), 2)
        self.btn_delegate = ButtonDelegate(self.ui.RecordScript_ToolList_table)
        
        for row, tool in enumerate(tool_list):
            tool_id, tool_name = tool.get("id", -1), tool.get("toolname", "default_toolname")
            toolname_item, btn_item = QStandardItem(tool_name), QStandardItem("+")
            
            toolname_item.setTextAlignment(Qt.AlignVCenter)
            btn_item.setTextAlignment(Qt.AlignVCenter)
            toolname_item.setFlags(toolname_item.flags() & ~Qt.ItemIsEditable)
            btn_item.setFlags(btn_item.flags() & ~Qt.ItemIsEditable)
            setattr(btn_item, "tool_id", tool_id)
            
            self.toollist_model.setItem(row, 0, toolname_item)
            self.toollist_model.setItem(row, 1, btn_item)

        self.ui.RecordScript_ToolList_table.setModel(self.toollist_model)
        self.ui.RecordScript_ToolList_table.setItemDelegate(self.btn_delegate)
        self.ui.RecordScript_ToolList_table.setMouseTracking(True)
        
        # Install event filter on this class (RecordScriptWindow)
        self.hovered_row = -1
        self.ui.RecordScript_ToolList_table.viewport().installEventFilter(self)

        # Configure UI
        self.ui.RecordScript_ToolList_table.setStyleSheet(u"""
            QTableView {
                color: rgb(255, 255, 255); background-color: rgb(0, 0, 0);
                border: 0px; padding: 0px; outline: 0;
            }""")        
        self.ui.RecordScript_ToolList_table.setFocusPolicy(Qt.NoFocus)
        self.ui.RecordScript_ToolList_table.setShowGrid(False)
        self.ui.RecordScript_ToolList_table.setSelectionMode(QAbstractItemView.NoSelection)
        self.ui.RecordScript_ToolList_table.verticalHeader().setVisible(False)
        self.ui.RecordScript_ToolList_table.horizontalHeader().setVisible(False)
        font = QFont("Arial Black", 11, weight=50, bold=False)
        self.ui.RecordScript_ToolList_table.setFont(font)
        self.ui.RecordScript_ToolList_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.ui.RecordScript_ToolList_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Fixed)
        self.ui.RecordScript_ToolList_table.setColumnWidth(1, 30)

    def setupPlanList(self):
        """setup data for plan list (script)"""
        self.planlist_model = QStandardItemModel(0, 3)
        self.planlist_model.setHorizontalHeaderLabels(['刀把名稱', '期間', '總時間'])
        self.ui.RecordScript_PlanList_title_group.hide()

        self.ui.RecordScript_PlanList_Element_List_table.setModel(self.planlist_model)

        self.plan_delegate = PlanListDelegate(self.ui.RecordScript_PlanList_Element_List_table)
        self.ui.RecordScript_PlanList_Element_List_table.setItemDelegate(self.plan_delegate)

        self.ui.RecordScript_PlanList_Element_List_table.setStyleSheet(u"""
            QTableView {
                color: rgb(255, 255, 255); background-color: rgb(0, 0, 0);
                border: 0px; padding: 4px; outline: 0;
            }
            QTableView::item { padding: 10px; }""")        
        self.ui.RecordScript_PlanList_Element_List_table.setFocusPolicy(Qt.NoFocus)
        self.ui.RecordScript_PlanList_Element_List_table.setShowGrid(False)
        self.ui.RecordScript_PlanList_Element_List_table.setSelectionMode(QAbstractItemView.NoSelection)
        self.ui.RecordScript_PlanList_Element_List_table.verticalHeader().setVisible(False)
        font = QFont("Arial Black", 11, weight=50, bold=False)
        self.ui.RecordScript_PlanList_Element_List_table.setFont(font)
        self.ui.RecordScript_PlanList_Element_List_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.ui.RecordScript_PlanList_Element_List_table.horizontalHeader().setStyleSheet(u"""
            QHeaderView::section {
                color: rgb(255, 255, 255); background-color: rgb(0, 0, 0);
                border: 0px; padding: 1px 8px; outline: 0;
            }
            QHeaderView::section::hover { background-color: rgb(0, 0, 0); }""")
        self.ui.RecordScript_PlanList_Element_List_table.horizontalHeader().setFocusPolicy(Qt.NoFocus)
        self.ui.RecordScript_PlanList_Element_List_table.horizontalHeader().setSelectionMode(QAbstractItemView.NoSelection)
        self.ui.RecordScript_PlanList_Element_List_table.horizontalHeader().setFont(font)
        self.ui.RecordScript_PlanList_Element_List_table.horizontalHeader().setDefaultAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.ui.RecordScript_PlanList_Element_List_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Fixed)
        self.ui.RecordScript_PlanList_Element_List_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Fixed)
        self.ui.RecordScript_PlanList_Element_List_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Fixed)
        
        self.total_time = 0

    def add_planlist_item(self, tool_id, tool_name, duration):
        """create item for planlist table (script)"""
        self.total_time += int(duration)
        toolname_item = QStandardItem(tool_name)
        duration_item = QStandardItem(str(duration))
        total_time_item = QStandardItem(str(self.total_time))

        setattr(toolname_item, "tool_id", tool_id)

        toolname_item.setTextAlignment(Qt.AlignVCenter)
        duration_item.setTextAlignment(Qt.AlignVCenter)
        total_time_item.setTextAlignment(Qt.AlignVCenter)
        toolname_item.setFlags(toolname_item.flags() &~Qt.ItemIsEditable)
        total_time_item.setFlags(total_time_item.flags() &~Qt.ItemIsEditable)

        self.planlist_model.appendRow([toolname_item, duration_item, total_time_item])

        new_row_index = self.planlist_model.rowCount() - 1
        duration_index = self.planlist_model.index(new_row_index, 1)
        self.ui.RecordScript_PlanList_Element_List_table.openPersistentEditor(duration_index)

    def eventFilter(self, source, event):
        """eventFilter to detect hover event"""
        if source is self.ui.RecordScript_ToolList_table.viewport():
            if event.type() == QEvent.MouseMove:
                index = self.ui.RecordScript_ToolList_table.indexAt(event.pos())
                if index.isValid():
                    current_row = index.row()
                    if current_row != self.hovered_row:
                        if self.hovered_row != -1:
                            self.set_row_hover_property(self.hovered_row, False)
                        self.hovered_row = current_row
                        self.set_row_hover_property(self.hovered_row, True)
            elif event.type() == QEvent.Leave:
                if self.hovered_row != -1:
                    self.set_row_hover_property(self.hovered_row, False)
                    self.hovered_row = -1
        return super(RecordScriptWindow_implementation, self).eventFilter(source, event)

    def set_row_hover_property(self, row, value):
        for col in range(self.toollist_model.columnCount()):
            item = self.toollist_model.item(row, col)
            if item:
                item.setData(value, Qt.UserRole + 1)
        self.ui.RecordScript_ToolList_table.viewport().update()

    def update_total_times(self):
        cumulative_time = 0
        for row in range(self.planlist_model.rowCount()):
            duration_item = self.planlist_model.item(row, 1)
            duration_seconds = int(duration_item.text())
            cumulative_time += duration_seconds
            total_time_item = self.planlist_model.item(row, 2)
            if total_time_item:
                total_time_item.setText(str(cumulative_time))

    def set_table_column_width(self):
        table_width = self.ui.RecordScript_PlanList_Element_List_table.width() - 8
        duration_width = int(table_width * 0.25)
        total_time_width = int(table_width * 0.25)
        toolname_width = table_width - duration_width - total_time_width
        self.ui.RecordScript_PlanList_Element_List_table.setColumnWidth(0, toolname_width)
        self.ui.RecordScript_PlanList_Element_List_table.setColumnWidth(1, duration_width)
        self.ui.RecordScript_PlanList_Element_List_table.setColumnWidth(2, total_time_width)

    def showEvent(self, event):
        self.set_table_column_width()
        super(RecordScriptWindow_implementation, self).showEvent(event)

    def resizeEvent(self, event):
        self.set_table_column_width()
        super(RecordScriptWindow_implementation, self).resizeEvent(event)

if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    tool_list = [
        {"id": 420, "toolname": "tool1"},
        {"id": 69, "toolname": "tool2"},
        {"id": 18763, "toolname": "tool3"},
    ]
    window = RecordScriptWindow_implementation(None, tool_list)
    window.show()
    sys.exit(app.exec_())