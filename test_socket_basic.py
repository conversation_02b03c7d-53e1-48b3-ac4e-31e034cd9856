#!/usr/bin/env python3
"""
基本的 Socket Server 測試，不依賴 Qt
"""

import socket
import threading
import time
import sys

class BasicSocketServer:
    def __init__(self, host='0.0.0.0', port=1333, max_clients=5):
        self.host = host
        self.port = port
        self.max_clients = max_clients
        self.server_socket = None
        self.client_sockets = {}
        self.running = False
        self.active_client = None
        
    def start_server(self):
        """啟動 Server"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
            self.server_socket.settimeout(1.0)
            
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(self.max_clients)
            
            print(f"Server 已啟動，監聽 {self.host}:{self.port}，最大客戶端數: {self.max_clients}")
            
            self.running = True
            
            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    client_id = f"{client_address[0]}:{client_address[1]}"
                    
                    print(f"新客戶端連接: {client_id}")
                    
                    if len(self.client_sockets) >= self.max_clients:
                        print(f"達到最大客戶端數量 ({self.max_clients})，拒絕連接 {client_id}")
                        client_socket.close()
                        continue
                    
                    # 優化客戶端 socket
                    client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
                    client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                    
                    self.client_sockets[client_id] = client_socket
                    
                    if self.active_client is None:
                        self.active_client = client_id
                        print(f"設置活躍客戶端: {client_id}")
                    
                    # 創建客戶端處理線程
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket, client_id),
                        daemon=True
                    )
                    client_thread.start()
                    
                except socket.timeout:
                    continue
                except socket.error as e:
                    if self.running:
                        print(f"Server socket 錯誤: {e}")
                    break
                except Exception as e:
                    print(f"Server 運行時發生異常: {e}")
                    break
                    
        except Exception as e:
            print(f"Server 啟動失敗: {e}")
        finally:
            self.cleanup_server()
    
    def _handle_client(self, client_socket, client_id):
        """處理單個客戶端的數據接收"""
        print(f"開始處理客戶端 {client_id}")
        data_count = 0
        
        try:
            while self.running and client_id in self.client_sockets:
                try:
                    data = client_socket.recv(1024)
                    if not data:
                        print(f"客戶端 {client_id} 斷開連接")
                        break
                    
                    data_count += 1
                    if data_count % 100 == 0:
                        print(f"從客戶端 {client_id} 收到 {data_count} 個數據包")
                    
                except socket.timeout:
                    continue
                except socket.error as e:
                    print(f"客戶端 {client_id} 連接錯誤: {e}")
                    break
                except Exception as e:
                    print(f"處理客戶端 {client_id} 數據時發生錯誤: {e}")
                    break
                    
        except Exception as e:
            print(f"客戶端 {client_id} 處理線程異常: {e}")
        finally:
            self._remove_client(client_id)
            print(f"客戶端 {client_id} 處理完成，總共收到 {data_count} 個數據包")
    
    def _remove_client(self, client_id):
        """移除客戶端記錄"""
        if client_id in self.client_sockets:
            try:
                self.client_sockets[client_id].close()
            except:
                pass
            del self.client_sockets[client_id]
        
        if self.active_client == client_id:
            if self.client_sockets:
                self.active_client = next(iter(self.client_sockets.keys()))
                print(f"切換活躍客戶端到: {self.active_client}")
            else:
                self.active_client = None
                print("沒有活躍客戶端")
        
        print(f"客戶端 {client_id} 已移除")
    
    def stop_server(self):
        """停止 Server"""
        print("正在停止 Server...")
        self.running = False
        self.cleanup_server()
        print("Server 已停止")
    
    def cleanup_server(self):
        """清理 Server 資源"""
        for client_id in list(self.client_sockets.keys()):
            try:
                self.client_sockets[client_id].close()
            except:
                pass
        self.client_sockets.clear()
        
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
            self.server_socket = None
        
        self.active_client = None

def simple_client_test(host='127.0.0.1', port=1333, duration=10):
    """簡單的客戶端測試"""
    try:
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.connect((host, port))
        print(f"已連接到 {host}:{port}")
        
        start_time = time.time()
        packet_count = 0
        
        while (time.time() - start_time) < duration:
            test_data = f"TestData_{packet_count:06d}_" + "X" * 50
            client_socket.send(test_data.encode())
            packet_count += 1
            
            if packet_count % 100 == 0:
                print(f"已發送 {packet_count} 個數據包")
            
            time.sleep(0.01)  # 10ms 間隔
            
        print(f"完成數據發送，總共發送 {packet_count} 個數據包")
        
    except Exception as e:
        print(f"Client 錯誤: {e}")
    finally:
        try:
            client_socket.close()
        except:
            pass
        print("Client 已斷開")

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='基本 Socket 測試')
    parser.add_argument('--mode', choices=['server', 'client'], default='server',
                       help='運行模式')
    parser.add_argument('--host', default='127.0.0.1', help='服務器地址')
    parser.add_argument('--port', type=int, default=1333, help='服務器端口')
    parser.add_argument('--duration', type=int, default=10, help='客戶端發送持續時間（秒）')
    
    args = parser.parse_args()
    
    if args.mode == 'server':
        server = BasicSocketServer(host='0.0.0.0', port=args.port)
        try:
            server.start_server()
        except KeyboardInterrupt:
            print("\n收到中斷信號，正在停止...")
        finally:
            server.stop_server()
    else:
        simple_client_test(args.host, args.port, args.duration)

if __name__ == '__main__':
    main()
