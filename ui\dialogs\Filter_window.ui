<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Filter_Window</class>
 <widget class="QWidget" name="Filter_Window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>330</width>
    <height>450</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>330</width>
    <height>450</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>330</width>
    <height>450</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#Filter_Window{background-color: #0C0C44;
    border: 2px solid #5448B6;}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <property name="spacing">
    <number>15</number>
   </property>
   <item>
    <widget class="QWidget" name="filter_title" native="true">
     <property name="styleSheet">
      <string notr="true">QWidget#filter_title{
background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));
border-radius:5px;
}
</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>3</number>
      </property>
      <property name="leftMargin">
       <number>5</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel" name="filter_title_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>濾波器</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_1">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="Filter_Layout">
     <property name="spacing">
      <number>15</number>
     </property>
     <property name="leftMargin">
      <number>5</number>
     </property>
     <property name="rightMargin">
      <number>5</number>
     </property>
     <item>
      <widget class="QRadioButton" name="Nofilter_radio">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>10</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
       </property>
       <property name="text">
        <string>無濾波器</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="Meanfilter_radio">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>10</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
       </property>
       <property name="text">
        <string>均值濾波器</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="Medianfilter_radio">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>10</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
       </property>
       <property name="text">
        <string>中值濾波器</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="Gaussianfilter_radio">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>10</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
       </property>
       <property name="text">
        <string>高斯濾波器</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QRadioButton" name="MSfilter_radio">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>10</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
       </property>
       <property name="text">
        <string>Machsync濾波器</string>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <item>
        <widget class="QLabel" name="filter_label">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">color: rgb(255, 255, 255);</string>
         </property>
         <property name="text">
          <string>濾波值:1</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QSlider" name="filter_Slider">
         <property name="styleSheet">
          <string notr="true">QSlider::groove:horizontal { 
border: 1px solid #bbb; 
background: white; 
height: 10px; 
border-radius: 4px; 
} 
   
QSlider::sub-page:horizontal { 
background: qlineargradient(x1: 0, y1: 0.2, x2: 1, y2: 1, 
    stop: 0 #bbf, stop: 1 #55f); 
border: 1px solid #777; 
height: 10px; 
border-radius: 4px; 
} 
   
QSlider::add-page:horizontal { 
background: #fff; 
border: 1px solid #777; 
height: 10px; 
border-radius: 4px; 
} 
   
QSlider::handle:horizontal { 
background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
    stop:0 #eee, stop:1 #ccc); 
border: 1px solid #777; 
width: 13px; 
margin-top: -2px; 
margin-bottom: -2px; 
border-radius: 4px; 
} 
   
QSlider::handle:horizontal:hover { 
background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
    stop:0 #fff, stop:1 #ddd); 
border: 1px solid #444; 
border-radius: 4px; 
} 
   
QSlider::sub-page:horizontal:disabled { 
background: #bbb; 
border-color: #999; 
} 
   
QSlider::add-page:horizontal:disabled { 
background: #eee; 
border-color: #999; 
} 
   
QSlider::handle:horizontal:disabled { 
background: #eee; 
border: 1px solid #aaa; 
border-radius: 4px; 
} </string>
         </property>
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="Lowfilter_Layout">
     <property name="spacing">
      <number>15</number>
     </property>
     <property name="leftMargin">
      <number>5</number>
     </property>
     <property name="rightMargin">
      <number>5</number>
     </property>
     <item>
      <layout class="QHBoxLayout" name="LowfilterLayout" stretch="3,1,2">
       <item>
        <widget class="QRadioButton" name="Lowfilter_radio">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string>低通濾波器</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLineEdit" name="Lowfilter_lineEdit"/>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="Highfilter_Layout" stretch="3,1,2">
       <item>
        <widget class="QRadioButton" name="Highfilter_radio">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string>高通濾波器</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLineEdit" name="Highfilter_lineEdit"/>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="SGfilter_Layout" stretch="2,1,4">
       <item>
        <widget class="QRadioButton" name="SGfilter_radio">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string>Savitzky-Golay濾波器</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_4">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLineEdit" name="SGfilter_lineEdit"/>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="MAfilter_Layout" stretch="3,1,2">
       <item>
        <widget class="QRadioButton" name="MAfilter_radio">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string>簡單移動平均</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLineEdit" name="MAfilter_lineEdit"/>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QWidget" name="filter_save_close" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="8,3,1,3">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <spacer name="horizontalSpacer_10">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="filter_save_btn">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color:#5448B6;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color:#7AFEC6;
     color: rgb(255, 255, 255);
    border:none
}

QPushButton:pressed {
    background-color: gray;
	border:none
}</string>
        </property>
        <property name="text">
         <string>儲存</string>
        </property>
        <property name="iconSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_5">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="filter_close_btn">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color: #0C0C44;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color: #0C0C44;
	border: 2px solid #7AFEC6;
    color: rgb(255, 255, 255);
	border-radius:5px;
	padding:2px;
}

QPushButton:pressed {
    background-color: gray;
	border: none;
}</string>
        </property>
        <property name="text">
         <string>關閉</string>
        </property>
        <property name="iconSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../system_image/system_image.qrc"/>
 </resources>
 <connections/>
</ui>
