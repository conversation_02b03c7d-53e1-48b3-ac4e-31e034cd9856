# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'CO2e_window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *


class Ui_CO2e_Window(object):
    def setupUi(self, CO2e_Window):
        if not CO2e_Window.objectName():
            CO2e_Window.setObjectName(u"CO2e_Window")
        CO2e_Window.resize(450, 600)
        CO2e_Window.setMinimumSize(QSize(450, 600))
        CO2e_Window.setMaximumSize(QSize(450, 600))
        CO2e_Window.setStyleSheet(u"QWidget#CO2e_Window{\n"
"background-color: #0C0C44;\n"
"border: 2px solid #5448B6;\n"
"}")
        self.verticalLayout = QVBoxLayout(CO2e_Window)
        self.verticalLayout.setSpacing(10)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.CO2_title1 = QWidget(CO2e_Window)
        self.CO2_title1.setObjectName(u"CO2_title1")
        self.CO2_title1.setStyleSheet(u"QWidget#CO2_title1{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout = QHBoxLayout(self.CO2_title1)
        self.horizontalLayout.setSpacing(3)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(2, 2, 2, 2)
        self.CO2_title1_label = QLabel(self.CO2_title1)
        self.CO2_title1_label.setObjectName(u"CO2_title1_label")
        font = QFont()
        font.setFamily(u"Arial Black")
        font.setPointSize(12)
        self.CO2_title1_label.setFont(font)
        self.CO2_title1_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_title1_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_title1_label.setAlignment(Qt.AlignCenter)
        self.CO2_title1_label.setMargin(2)

        self.horizontalLayout.addWidget(self.CO2_title1_label)

        self.horizontalSpacer_1 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_1)


        self.verticalLayout.addWidget(self.CO2_title1)

        self.CO2_edit_group1 = QWidget(CO2e_Window)
        self.CO2_edit_group1.setObjectName(u"CO2_edit_group1")
        self.grid_toolinfo_area1 = QGridLayout(self.CO2_edit_group1)
        self.grid_toolinfo_area1.setSpacing(0)
        self.grid_toolinfo_area1.setObjectName(u"grid_toolinfo_area1")
        self.grid_toolinfo_area1.setContentsMargins(9, 0, 9, 0)
        self.CO2_ap_unit = QLabel(self.CO2_edit_group1)
        self.CO2_ap_unit.setObjectName(u"CO2_ap_unit")
        font1 = QFont()
        font1.setFamily(u"Arial Black")
        font1.setPointSize(10)
        self.CO2_ap_unit.setFont(font1)
        self.CO2_ap_unit.setLayoutDirection(Qt.LeftToRight)
        self.CO2_ap_unit.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_ap_unit.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_ap_unit.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_ap_unit, 5, 4, 1, 1)

        self.CO2_n_unit = QLabel(self.CO2_edit_group1)
        self.CO2_n_unit.setObjectName(u"CO2_n_unit")
        self.CO2_n_unit.setFont(font1)
        self.CO2_n_unit.setLayoutDirection(Qt.LeftToRight)
        self.CO2_n_unit.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_n_unit.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_n_unit.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_n_unit, 7, 4, 1, 1)

        self.horizontalSpacer_7 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_7, 6, 5, 1, 1)

        self.CO2_Dc_label = QLabel(self.CO2_edit_group1)
        self.CO2_Dc_label.setObjectName(u"CO2_Dc_label")
        self.CO2_Dc_label.setFont(font1)
        self.CO2_Dc_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_Dc_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_Dc_label.setScaledContents(False)
        self.CO2_Dc_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_Dc_label.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_Dc_label, 2, 0, 1, 1)

        self.CO2_ae_unit = QLabel(self.CO2_edit_group1)
        self.CO2_ae_unit.setObjectName(u"CO2_ae_unit")
        self.CO2_ae_unit.setFont(font1)
        self.CO2_ae_unit.setLayoutDirection(Qt.LeftToRight)
        self.CO2_ae_unit.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_ae_unit.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_ae_unit.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_ae_unit, 6, 4, 1, 1)

        self.CO2_Dc_lineEdit = QLineEdit(self.CO2_edit_group1)
        self.CO2_Dc_lineEdit.setObjectName(u"CO2_Dc_lineEdit")
        self.CO2_Dc_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1.addWidget(self.CO2_Dc_lineEdit, 2, 2, 1, 1)

        self.CO2_vc_label = QLabel(self.CO2_edit_group1)
        self.CO2_vc_label.setObjectName(u"CO2_vc_label")
        self.CO2_vc_label.setFont(font1)
        self.CO2_vc_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_vc_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_vc_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_vc_label.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_vc_label, 3, 0, 1, 1)

        self.CO2_fz_unit = QLabel(self.CO2_edit_group1)
        self.CO2_fz_unit.setObjectName(u"CO2_fz_unit")
        self.CO2_fz_unit.setFont(font1)
        self.CO2_fz_unit.setLayoutDirection(Qt.LeftToRight)
        self.CO2_fz_unit.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_fz_unit.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_fz_unit.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_fz_unit, 4, 4, 1, 1)

        self.horizontalSpacer_12 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_12, 2, 1, 1, 1)

        self.horizontalSpacer_11 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_11, 1, 1, 1, 1)

        self.horizontalSpacer_14 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_14, 4, 1, 1, 1)

        self.horizontalSpacer_10 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_10, 0, 1, 1, 1)

        self.horizontalSpacer_15 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_15, 5, 1, 1, 1)

        self.CO2_vc_unit = QLabel(self.CO2_edit_group1)
        self.CO2_vc_unit.setObjectName(u"CO2_vc_unit")
        self.CO2_vc_unit.setFont(font1)
        self.CO2_vc_unit.setLayoutDirection(Qt.LeftToRight)
        self.CO2_vc_unit.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_vc_unit.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_vc_unit.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_vc_unit, 3, 4, 1, 1)

        self.horizontalSpacer_13 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_13, 3, 1, 1, 1)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_5, 4, 5, 1, 1)

        self.horizontalSpacer_16 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_16, 6, 1, 1, 1)

        self.horizontalSpacer_18 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_18, 8, 1, 1, 1)

        self.horizontalSpacer_17 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_17, 7, 1, 1, 1)

        self.CO2_vc_lineEdit = QLineEdit(self.CO2_edit_group1)
        self.CO2_vc_lineEdit.setObjectName(u"CO2_vc_lineEdit")
        self.CO2_vc_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1.addWidget(self.CO2_vc_lineEdit, 3, 2, 1, 1)

        self.CO2_K_label = QLabel(self.CO2_edit_group1)
        self.CO2_K_label.setObjectName(u"CO2_K_label")
        self.CO2_K_label.setFont(font1)
        self.CO2_K_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_K_label.setStyleSheet(u"color: rgb(255, 255, 255);\n"
"")
        self.CO2_K_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_K_label.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_K_label, 0, 0, 1, 1)

        self.CO2_zc_lineEdit = QLineEdit(self.CO2_edit_group1)
        self.CO2_zc_lineEdit.setObjectName(u"CO2_zc_lineEdit")

        self.grid_toolinfo_area1.addWidget(self.CO2_zc_lineEdit, 1, 2, 1, 1)

        self.CO2_ae_lineEdit = QLineEdit(self.CO2_edit_group1)
        self.CO2_ae_lineEdit.setObjectName(u"CO2_ae_lineEdit")
        self.CO2_ae_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1.addWidget(self.CO2_ae_lineEdit, 6, 2, 1, 1)

        self.CO2_vf_unit = QLabel(self.CO2_edit_group1)
        self.CO2_vf_unit.setObjectName(u"CO2_vf_unit")
        self.CO2_vf_unit.setFont(font1)
        self.CO2_vf_unit.setLayoutDirection(Qt.LeftToRight)
        self.CO2_vf_unit.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_vf_unit.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_vf_unit.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_vf_unit, 8, 4, 1, 1)

        self.CO2_vf_label = QLabel(self.CO2_edit_group1)
        self.CO2_vf_label.setObjectName(u"CO2_vf_label")
        self.CO2_vf_label.setFont(font1)
        self.CO2_vf_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_vf_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_vf_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_vf_label.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_vf_label, 8, 0, 1, 1)

        self.CO2_Dc_unit = QLabel(self.CO2_edit_group1)
        self.CO2_Dc_unit.setObjectName(u"CO2_Dc_unit")
        self.CO2_Dc_unit.setFont(font1)
        self.CO2_Dc_unit.setLayoutDirection(Qt.LeftToRight)
        self.CO2_Dc_unit.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_Dc_unit.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_Dc_unit.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_Dc_unit, 2, 4, 1, 1)

        self.CO2_ae_label = QLabel(self.CO2_edit_group1)
        self.CO2_ae_label.setObjectName(u"CO2_ae_label")
        self.CO2_ae_label.setFont(font1)
        self.CO2_ae_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_ae_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_ae_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_ae_label.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_ae_label, 6, 0, 1, 1)

        self.horizontalSpacer_9 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_9, 8, 5, 1, 1)

        self.CO2_n_lineEdit = QLineEdit(self.CO2_edit_group1)
        self.CO2_n_lineEdit.setObjectName(u"CO2_n_lineEdit")
        self.CO2_n_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1.addWidget(self.CO2_n_lineEdit, 7, 2, 1, 1)

        self.CO2_vf_lineEdit = QLineEdit(self.CO2_edit_group1)
        self.CO2_vf_lineEdit.setObjectName(u"CO2_vf_lineEdit")
        self.CO2_vf_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1.addWidget(self.CO2_vf_lineEdit, 8, 2, 1, 1)

        self.CO2_n_label = QLabel(self.CO2_edit_group1)
        self.CO2_n_label.setObjectName(u"CO2_n_label")
        self.CO2_n_label.setFont(font1)
        self.CO2_n_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_n_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_n_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_n_label.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_n_label, 7, 0, 1, 1)

        self.CO2_fz_lineEdit = QLineEdit(self.CO2_edit_group1)
        self.CO2_fz_lineEdit.setObjectName(u"CO2_fz_lineEdit")
        self.CO2_fz_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1.addWidget(self.CO2_fz_lineEdit, 4, 2, 1, 1)

        self.CO2_ap_lineEdit = QLineEdit(self.CO2_edit_group1)
        self.CO2_ap_lineEdit.setObjectName(u"CO2_ap_lineEdit")
        self.CO2_ap_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1.addWidget(self.CO2_ap_lineEdit, 5, 2, 1, 1)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer, 0, 5, 1, 1)

        self.CO2_ap_label = QLabel(self.CO2_edit_group1)
        self.CO2_ap_label.setObjectName(u"CO2_ap_label")
        self.CO2_ap_label.setFont(font1)
        self.CO2_ap_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_ap_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_ap_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_ap_label.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_ap_label, 5, 0, 1, 1)

        self.horizontalSpacer_8 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_8, 7, 5, 1, 1)

        self.CO2_fz_label = QLabel(self.CO2_edit_group1)
        self.CO2_fz_label.setObjectName(u"CO2_fz_label")
        self.CO2_fz_label.setFont(font1)
        self.CO2_fz_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_fz_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_fz_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_fz_label.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_fz_label, 4, 0, 1, 1)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_2, 1, 5, 1, 1)

        self.horizontalSpacer_6 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_6, 5, 5, 1, 1)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_3, 2, 5, 1, 1)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_4, 3, 5, 1, 1)

        self.CO2_zc_label = QLabel(self.CO2_edit_group1)
        self.CO2_zc_label.setObjectName(u"CO2_zc_label")
        self.CO2_zc_label.setFont(font1)
        self.CO2_zc_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_zc_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_zc_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_zc_label.setMargin(2)

        self.grid_toolinfo_area1.addWidget(self.CO2_zc_label, 1, 0, 1, 1)

        self.CO2_K_comboBox = QComboBox(self.CO2_edit_group1)
        self.CO2_K_comboBox.setObjectName(u"CO2_K_comboBox")
        self.CO2_K_comboBox.setStyleSheet(u"QComboBox {\n"
"    background-color: white;\n"
"    selection-background-color: #5A7EC7; \n"
"    selection-color: white;\n"
"    padding: 2px;\n"
"    text-align: center; \n"
"}\n"
"\n"
"QComboBox QAbstractItemView {\n"
"    background-color: #0C0C44;\n"
"    color: white;\n"
"    selection-background-color: #5448B6; \n"
"    selection-color: white; \n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"}\n"
"\n"
"QComboBox QLineEdit {\n"
"    background-color: white;\n"
"    color: black;\n"
"    text-align: center; \n"
"}\n"
"")

        self.grid_toolinfo_area1.addWidget(self.CO2_K_comboBox, 0, 2, 1, 3)

        self.horizontalSpacer_26 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_26, 2, 3, 1, 1)

        self.horizontalSpacer_27 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_27, 3, 3, 1, 1)

        self.horizontalSpacer_30 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_30, 4, 3, 1, 1)

        self.horizontalSpacer_31 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_31, 5, 3, 1, 1)

        self.horizontalSpacer_36 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_36, 6, 3, 1, 1)

        self.horizontalSpacer_37 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_37, 7, 3, 1, 1)

        self.horizontalSpacer_38 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_38, 8, 3, 1, 1)

        self.horizontalSpacer_23 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1.addItem(self.horizontalSpacer_23, 1, 3, 1, 2)

        self.grid_toolinfo_area1.setColumnStretch(0, 13)
        self.grid_toolinfo_area1.setColumnStretch(1, 5)
        self.grid_toolinfo_area1.setColumnStretch(2, 13)
        self.grid_toolinfo_area1.setColumnStretch(3, 2)
        self.grid_toolinfo_area1.setColumnStretch(4, 5)
        self.grid_toolinfo_area1.setColumnStretch(5, 11)

        self.verticalLayout.addWidget(self.CO2_edit_group1)

        self.CO2_title2 = QWidget(CO2e_Window)
        self.CO2_title2.setObjectName(u"CO2_title2")
        self.CO2_title2.setStyleSheet(u"QWidget#CO2_title2{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout_2 = QHBoxLayout(self.CO2_title2)
        self.horizontalLayout_2.setSpacing(3)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalLayout_2.setContentsMargins(2, 2, 2, 2)
        self.CO2_title2_label = QLabel(self.CO2_title2)
        self.CO2_title2_label.setObjectName(u"CO2_title2_label")
        self.CO2_title2_label.setFont(font)
        self.CO2_title2_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_title2_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_title2_label.setAlignment(Qt.AlignCenter)
        self.CO2_title2_label.setMargin(2)

        self.horizontalLayout_2.addWidget(self.CO2_title2_label)

        self.horizontalSpacer_19 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_19)


        self.verticalLayout.addWidget(self.CO2_title2)

        self.CO2_edit_group2 = QWidget(CO2e_Window)
        self.CO2_edit_group2.setObjectName(u"CO2_edit_group2")
        self.grid_toolinfo_area1_2 = QGridLayout(self.CO2_edit_group2)
        self.grid_toolinfo_area1_2.setSpacing(0)
        self.grid_toolinfo_area1_2.setObjectName(u"grid_toolinfo_area1_2")
        self.grid_toolinfo_area1_2.setContentsMargins(9, 0, 9, 0)
        self.horizontalSpacer_28 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1_2.addItem(self.horizontalSpacer_28, 1, 5, 1, 1)

        self.CO2_Q_label = QLabel(self.CO2_edit_group2)
        self.CO2_Q_label.setObjectName(u"CO2_Q_label")
        self.CO2_Q_label.setFont(font1)
        self.CO2_Q_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_Q_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_Q_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_Q_label.setMargin(2)

        self.grid_toolinfo_area1_2.addWidget(self.CO2_Q_label, 0, 0, 1, 1)

        self.CO2_pc_unit = QLabel(self.CO2_edit_group2)
        self.CO2_pc_unit.setObjectName(u"CO2_pc_unit")
        self.CO2_pc_unit.setFont(font1)
        self.CO2_pc_unit.setLayoutDirection(Qt.LeftToRight)
        self.CO2_pc_unit.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_pc_unit.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_pc_unit.setMargin(2)

        self.grid_toolinfo_area1_2.addWidget(self.CO2_pc_unit, 1, 4, 1, 1)

        self.horizontalSpacer_33 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1_2.addItem(self.horizontalSpacer_33, 1, 1, 1, 1)

        self.CO2_Q_unit = QLabel(self.CO2_edit_group2)
        self.CO2_Q_unit.setObjectName(u"CO2_Q_unit")
        self.CO2_Q_unit.setFont(font1)
        self.CO2_Q_unit.setLayoutDirection(Qt.LeftToRight)
        self.CO2_Q_unit.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_Q_unit.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_Q_unit.setMargin(2)

        self.grid_toolinfo_area1_2.addWidget(self.CO2_Q_unit, 0, 4, 1, 1)

        self.horizontalSpacer_32 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1_2.addItem(self.horizontalSpacer_32, 0, 1, 1, 1)

        self.horizontalSpacer_39 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1_2.addItem(self.horizontalSpacer_39, 0, 3, 1, 1)

        self.horizontalSpacer_24 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1_2.addItem(self.horizontalSpacer_24, 0, 5, 1, 1)

        self.CO2_pc_lineEdit = QLineEdit(self.CO2_edit_group2)
        self.CO2_pc_lineEdit.setObjectName(u"CO2_pc_lineEdit")
        self.CO2_pc_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1_2.addWidget(self.CO2_pc_lineEdit, 1, 2, 1, 1)

        self.CO2_pc_label = QLabel(self.CO2_edit_group2)
        self.CO2_pc_label.setObjectName(u"CO2_pc_label")
        self.CO2_pc_label.setFont(font1)
        self.CO2_pc_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_pc_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_pc_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_pc_label.setMargin(2)

        self.grid_toolinfo_area1_2.addWidget(self.CO2_pc_label, 1, 0, 1, 1)

        self.CO2_Q_lineEdit = QLineEdit(self.CO2_edit_group2)
        self.CO2_Q_lineEdit.setObjectName(u"CO2_Q_lineEdit")
        self.CO2_Q_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1_2.addWidget(self.CO2_Q_lineEdit, 0, 2, 1, 1)

        self.horizontalSpacer_40 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1_2.addItem(self.horizontalSpacer_40, 1, 3, 1, 1)

        self.grid_toolinfo_area1_2.setColumnStretch(0, 13)
        self.grid_toolinfo_area1_2.setColumnStretch(1, 5)
        self.grid_toolinfo_area1_2.setColumnStretch(2, 13)
        self.grid_toolinfo_area1_2.setColumnStretch(3, 2)
        self.grid_toolinfo_area1_2.setColumnStretch(4, 5)
        self.grid_toolinfo_area1_2.setColumnStretch(5, 13)

        self.verticalLayout.addWidget(self.CO2_edit_group2)

        self.CO2_title3 = QWidget(CO2e_Window)
        self.CO2_title3.setObjectName(u"CO2_title3")
        self.CO2_title3.setStyleSheet(u"QWidget#CO2_title3{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout_3 = QHBoxLayout(self.CO2_title3)
        self.horizontalLayout_3.setSpacing(3)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_3.setContentsMargins(2, 2, 2, 2)
        self.CO2_title3_label = QLabel(self.CO2_title3)
        self.CO2_title3_label.setObjectName(u"CO2_title3_label")
        self.CO2_title3_label.setFont(font)
        self.CO2_title3_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_title3_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_title3_label.setAlignment(Qt.AlignCenter)
        self.CO2_title3_label.setMargin(2)

        self.horizontalLayout_3.addWidget(self.CO2_title3_label)

        self.horizontalSpacer_20 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_20)


        self.verticalLayout.addWidget(self.CO2_title3)

        self.CO2_edit_group3 = QWidget(CO2e_Window)
        self.CO2_edit_group3.setObjectName(u"CO2_edit_group3")
        self.grid_toolinfo_area1_3 = QGridLayout(self.CO2_edit_group3)
        self.grid_toolinfo_area1_3.setSpacing(0)
        self.grid_toolinfo_area1_3.setObjectName(u"grid_toolinfo_area1_3")
        self.grid_toolinfo_area1_3.setContentsMargins(9, 0, 9, 0)
        self.CO2_EF_label = QLabel(self.CO2_edit_group3)
        self.CO2_EF_label.setObjectName(u"CO2_EF_label")
        self.CO2_EF_label.setFont(font1)
        self.CO2_EF_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_EF_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_EF_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_EF_label.setMargin(2)

        self.grid_toolinfo_area1_3.addWidget(self.CO2_EF_label, 1, 0, 1, 1)

        self.horizontalSpacer_25 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1_3.addItem(self.horizontalSpacer_25, 0, 5, 1, 1)

        self.CO2_Pb_label = QLabel(self.CO2_edit_group3)
        self.CO2_Pb_label.setObjectName(u"CO2_Pb_label")
        self.CO2_Pb_label.setFont(font1)
        self.CO2_Pb_label.setLayoutDirection(Qt.LeftToRight)
        self.CO2_Pb_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_Pb_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_Pb_label.setMargin(2)

        self.grid_toolinfo_area1_3.addWidget(self.CO2_Pb_label, 0, 0, 1, 1)

        self.horizontalSpacer_35 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1_3.addItem(self.horizontalSpacer_35, 0, 1, 1, 1)

        self.horizontalSpacer_34 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1_3.addItem(self.horizontalSpacer_34, 1, 1, 1, 1)

        self.CO2_Pb_lineEdit = QLineEdit(self.CO2_edit_group3)
        self.CO2_Pb_lineEdit.setObjectName(u"CO2_Pb_lineEdit")
        self.CO2_Pb_lineEdit.setReadOnly(False)

        self.grid_toolinfo_area1_3.addWidget(self.CO2_Pb_lineEdit, 0, 2, 1, 1)

        self.CO2_Pb_unit = QLabel(self.CO2_edit_group3)
        self.CO2_Pb_unit.setObjectName(u"CO2_Pb_unit")
        self.CO2_Pb_unit.setFont(font1)
        self.CO2_Pb_unit.setLayoutDirection(Qt.LeftToRight)
        self.CO2_Pb_unit.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_Pb_unit.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_Pb_unit.setMargin(2)

        self.grid_toolinfo_area1_3.addWidget(self.CO2_Pb_unit, 0, 4, 1, 1)

        self.CO2_EF_unit = QLabel(self.CO2_edit_group3)
        self.CO2_EF_unit.setObjectName(u"CO2_EF_unit")
        self.CO2_EF_unit.setFont(font1)
        self.CO2_EF_unit.setLayoutDirection(Qt.LeftToRight)
        self.CO2_EF_unit.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.CO2_EF_unit.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CO2_EF_unit.setMargin(2)

        self.grid_toolinfo_area1_3.addWidget(self.CO2_EF_unit, 1, 4, 1, 1)

        self.horizontalSpacer_29 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1_3.addItem(self.horizontalSpacer_29, 1, 5, 1, 1)

        self.horizontalSpacer_41 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1_3.addItem(self.horizontalSpacer_41, 0, 3, 1, 1)

        self.horizontalSpacer_42 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.grid_toolinfo_area1_3.addItem(self.horizontalSpacer_42, 1, 3, 1, 1)

        self.CO2_EF_comboBox = QComboBox(self.CO2_edit_group3)
        self.CO2_EF_comboBox.setObjectName(u"CO2_EF_comboBox")
        self.CO2_EF_comboBox.setStyleSheet(u"QComboBox {\n"
"    background-color: white;\n"
"    selection-background-color: #5A7EC7; \n"
"    selection-color: white;\n"
"    padding: 2px;\n"
"    text-align: center; \n"
"}\n"
"\n"
"QComboBox QAbstractItemView {\n"
"    background-color: #0C0C44;\n"
"    color: white;\n"
"    selection-background-color: #5448B6; \n"
"    selection-color: white; \n"
"}\n"
"\n"
"QComboBox::drop-down {\n"
"    border: none;\n"
"}\n"
"\n"
"QComboBox QLineEdit {\n"
"    background-color: white;\n"
"    color: black;\n"
"    text-align: center; \n"
"}\n"
"")

        self.grid_toolinfo_area1_3.addWidget(self.CO2_EF_comboBox, 1, 2, 1, 1)

        self.grid_toolinfo_area1_3.setColumnStretch(0, 15)
        self.grid_toolinfo_area1_3.setColumnStretch(1, 10)
        self.grid_toolinfo_area1_3.setColumnStretch(2, 19)
        self.grid_toolinfo_area1_3.setColumnStretch(3, 3)
        self.grid_toolinfo_area1_3.setColumnStretch(4, 5)
        self.grid_toolinfo_area1_3.setColumnStretch(5, 10)

        self.verticalLayout.addWidget(self.CO2_edit_group3)

        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)

        self.verticalLayout.addItem(self.verticalSpacer)

        self.CO2_save_close = QWidget(CO2e_Window)
        self.CO2_save_close.setObjectName(u"CO2_save_close")
        self.horizontalLayout_9 = QHBoxLayout(self.CO2_save_close)
        self.horizontalLayout_9.setSpacing(0)
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.horizontalLayout_9.setContentsMargins(5, 5, 8, 8)
        self.CO2_auto_btn = QPushButton(self.CO2_save_close)
        self.CO2_auto_btn.setObjectName(u"CO2_auto_btn")
        self.CO2_auto_btn.setMinimumSize(QSize(10, 10))
        self.CO2_auto_btn.setFont(font1)
        self.CO2_auto_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: 2px solid #7AFEC6;\n"
"    color: rgb(255, 255, 255);\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")
        self.CO2_auto_btn.setIconSize(QSize(20, 20))
        self.CO2_auto_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.CO2_auto_btn)

        self.horizontalSpacer_21 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_21)

        self.CO2_save_btn = QPushButton(self.CO2_save_close)
        self.CO2_save_btn.setObjectName(u"CO2_save_btn")
        self.CO2_save_btn.setMinimumSize(QSize(10, 10))
        self.CO2_save_btn.setFont(font1)
        self.CO2_save_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color:#5448B6;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7AFEC6;\n"
"     color: rgb(255, 255, 255);\n"
"    border:none\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border:none\n"
"}")
        self.CO2_save_btn.setIconSize(QSize(20, 20))
        self.CO2_save_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.CO2_save_btn)

        self.horizontalSpacer_22 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_22)

        self.CO2_close_btn = QPushButton(self.CO2_save_close)
        self.CO2_close_btn.setObjectName(u"CO2_close_btn")
        self.CO2_close_btn.setMinimumSize(QSize(10, 10))
        self.CO2_close_btn.setFont(font1)
        self.CO2_close_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: 2px solid #7AFEC6;\n"
"    color: rgb(255, 255, 255);\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")
        self.CO2_close_btn.setIconSize(QSize(20, 20))
        self.CO2_close_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.CO2_close_btn)

        self.horizontalLayout_9.setStretch(0, 2)
        self.horizontalLayout_9.setStretch(1, 4)
        self.horizontalLayout_9.setStretch(2, 2)
        self.horizontalLayout_9.setStretch(3, 1)
        self.horizontalLayout_9.setStretch(4, 2)

        self.verticalLayout.addWidget(self.CO2_save_close)


        self.retranslateUi(CO2e_Window)

        QMetaObject.connectSlotsByName(CO2e_Window)
    # setupUi

    def retranslateUi(self, CO2e_Window):
        CO2e_Window.setWindowTitle(QCoreApplication.translate("CO2e_Window", u"Form", None))
        self.CO2_title1_label.setText(QCoreApplication.translate("CO2e_Window", u"\u5207\u524a\u8f38\u5165\u53c3\u6578", None))
        self.CO2_ap_unit.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(mm)</p></body></html>", None))
        self.CO2_n_unit.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(rev/min)</p></body></html>", None))
        self.CO2_Dc_label.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(D<span style=\" vertical-align:sub;\">c</span>)\u5200\u5177\u76f4\u5f91 :</p></body></html>", None))
        self.CO2_ae_unit.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(mm)</p></body></html>", None))
        self.CO2_vc_label.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(v<span style=\" vertical-align:sub;\">c</span>)\u5207\u524a\u7dda\u901f :</p></body></html>", None))
        self.CO2_fz_unit.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(mm)</p></body></html>", None))
        self.CO2_vc_unit.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(m/mm)</p></body></html>", None))
        self.CO2_K_label.setText(QCoreApplication.translate("CO2e_Window", u"(K)\u5de5\u4ef6\u6750\u8cea :", None))
        self.CO2_vf_unit.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(mm/min)</p></body></html>", None))
        self.CO2_vf_label.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(v<span style=\" vertical-align:sub;\">f</span>)\u9032\u7d66\u91cf :</p></body></html>", None))
        self.CO2_Dc_unit.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(mm)</p></body></html>", None))
        self.CO2_ae_label.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(a<span style=\" vertical-align:sub;\">e</span>)\u5207\u524a\u5bec\u5ea6 :</p></body></html>", None))
        self.CO2_n_label.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(n)\u4e3b\u8ef8\u8f49\u901f :</p></body></html>", None))
        self.CO2_ap_label.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(a<span style=\" vertical-align:sub;\">p</span>)\u5207\u524a\u6df1\u5ea6 :</p></body></html>", None))
        self.CO2_fz_label.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(f<span style=\" vertical-align:sub;\">z</span>)\u55ae\u5203\u5207\u91cf :</p></body></html>", None))
        self.CO2_zc_label.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(z<span style=\" vertical-align:sub;\">c</span>)\u5200\u5203\u6578\u91cf :</p></body></html>", None))
        self.CO2_title2_label.setText(QCoreApplication.translate("CO2e_Window", u"\u5207\u524a\u8f38\u51fa\u53c3\u6578", None))
        self.CO2_Q_label.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(Q)\u6750\u6599\u53bb\u9664\u7387 :</p></body></html>", None))
        self.CO2_pc_unit.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(kW)</p></body></html>", None))
        self.CO2_Q_unit.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(m/mm)</p></body></html>", None))
        self.CO2_pc_label.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(p<span style=\" vertical-align:sub;\">c</span>)\u6709\u6548\u529f\u7387 :</p></body></html>", None))
        self.CO2_title3_label.setText(QCoreApplication.translate("CO2e_Window", u"\u6a5f\u5668\u53c3\u6578", None))
        self.CO2_EF_label.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>\u96fb\u529b\u6392\u653e\u4fc2\u6578 :</p></body></html>", None))
        self.CO2_Pb_label.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>\u57fa\u672c\u529f\u7387\u8ca0\u8f09 :</p></body></html>", None))
        self.CO2_Pb_unit.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(kW)</p></body></html>", None))
        self.CO2_EF_unit.setText(QCoreApplication.translate("CO2e_Window", u"<html><head/><body><p>(kg CO2e/kWh)</p></body></html>", None))
        self.CO2_auto_btn.setText(QCoreApplication.translate("CO2e_Window", u"\u81ea\u52d5", None))
        self.CO2_save_btn.setText(QCoreApplication.translate("CO2e_Window", u"\u5132\u5b58", None))
        self.CO2_close_btn.setText(QCoreApplication.translate("CO2e_Window", u"\u95dc\u9589", None))
    # retranslateUi

