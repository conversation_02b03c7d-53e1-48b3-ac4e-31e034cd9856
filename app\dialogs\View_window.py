# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'View_Window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

import system_image.system_image_rc

class Ui_View_Window(object):
    def setupUi(self, View_Window):
        if not View_Window.objectName():
            View_Window.setObjectName(u"View_Window")
        View_Window.resize(500, 700)
        View_Window.setMinimumSize(QSize(500, 700))
        View_Window.setMaximumSize(QSize(500, 700))
        View_Window.setStyleSheet(u"QWidget#View_Window{\n"
"background-color: #0C0C44;\n"
"border: 2px solid #5448B6;\n"
"}")
        self.verticalLayout_3 = QVBoxLayout(View_Window)
        self.verticalLayout_3.setSpacing(10)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.view_title = QWidget(View_Window)
        self.view_title.setObjectName(u"view_title")
        self.view_title.setStyleSheet(u"QWidget#filter_title{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout = QHBoxLayout(self.view_title)
        self.horizontalLayout.setSpacing(3)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(5, 0, 0, 0)
        self.view_title_label = QLabel(self.view_title)
        self.view_title_label.setObjectName(u"view_title_label")
        font = QFont()
        font.setFamily(u"Arial Black")
        font.setPointSize(12)
        self.view_title_label.setFont(font)
        self.view_title_label.setLayoutDirection(Qt.LeftToRight)
        self.view_title_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.view_title_label.setAlignment(Qt.AlignCenter)
        self.view_title_label.setMargin(2)

        self.horizontalLayout.addWidget(self.view_title_label)

        self.horizontalSpacer_1 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_1)


        self.verticalLayout_3.addWidget(self.view_title)

        self.verticalLayout_2 = QVBoxLayout()
        self.verticalLayout_2.setSpacing(6)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(9, -1, 9, -1)
        self.CF = QGroupBox(View_Window)
        self.CF.setObjectName(u"CF")
        font1 = QFont()
        font1.setFamily(u"Arial Black")
        font1.setPointSize(11)
        self.CF.setFont(font1)
        self.CF.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.horizontalLayout_3 = QHBoxLayout(self.CF)
        self.horizontalLayout_3.setSpacing(0)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_3.setContentsMargins(9, 0, 9, 9)
        self.CF_Auto_checkBox = QCheckBox(self.CF)
        self.CF_Auto_checkBox.setObjectName(u"CF_Auto_checkBox")
        font2 = QFont()
        font2.setFamily(u"Arial Black")
        font2.setPointSize(10)
        self.CF_Auto_checkBox.setFont(font2)
        self.CF_Auto_checkBox.setStyleSheet(u"QCheckBox{color:#ABABAB;}\n"
"QCheckBox::indicator {\n"
"    width: 13px;  /* \u8a2d\u5b9a\u5bec\u5ea6 */\n"
"    height: 13px; /* \u8a2d\u5b9a\u9ad8\u5ea6 */\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked:hover {\n"
"    image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked:hover {\n"
"    image: url(:/btn_check/btn_check_s1_1.png);\n"
"}")
        self.CF_Auto_checkBox.setIconSize(QSize(8, 8))

        self.horizontalLayout_3.addWidget(self.CF_Auto_checkBox)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_3)

        self.CF_edit_group = QWidget(self.CF)
        self.CF_edit_group.setObjectName(u"CF_edit_group")
        self.grid_toolinfo_area1_2 = QGridLayout(self.CF_edit_group)
        self.grid_toolinfo_area1_2.setObjectName(u"grid_toolinfo_area1_2")
        self.grid_toolinfo_area1_2.setHorizontalSpacing(8)
        self.grid_toolinfo_area1_2.setVerticalSpacing(5)
        self.grid_toolinfo_area1_2.setContentsMargins(8, 0, 5, 0)
        self.CF_max_lineedit = QLineEdit(self.CF_edit_group)
        self.CF_max_lineedit.setObjectName(u"CF_max_lineedit")
        self.CF_max_lineedit.setStyleSheet(u"color:#000000;")
        self.CF_max_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_2.addWidget(self.CF_max_lineedit, 0, 1, 1, 1)

        self.CF_min_label = QLabel(self.CF_edit_group)
        self.CF_min_label.setObjectName(u"CF_min_label")
        self.CF_min_label.setFont(font2)
        self.CF_min_label.setLayoutDirection(Qt.LeftToRight)
        self.CF_min_label.setStyleSheet(u"color: #AAFFAA;")
        self.CF_min_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CF_min_label.setMargin(2)

        self.grid_toolinfo_area1_2.addWidget(self.CF_min_label, 0, 2, 1, 1)

        self.CF_alarm_label = QLabel(self.CF_edit_group)
        self.CF_alarm_label.setObjectName(u"CF_alarm_label")
        self.CF_alarm_label.setFont(font2)
        self.CF_alarm_label.setLayoutDirection(Qt.LeftToRight)
        self.CF_alarm_label.setStyleSheet(u"color:#FFAAAA;")
        self.CF_alarm_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CF_alarm_label.setMargin(2)

        self.grid_toolinfo_area1_2.addWidget(self.CF_alarm_label, 1, 2, 1, 1)

        self.CF_warning_lineedit = QLineEdit(self.CF_edit_group)
        self.CF_warning_lineedit.setObjectName(u"CF_warning_lineedit")
        self.CF_warning_lineedit.setStyleSheet(u"color:#000000;")
        self.CF_warning_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_2.addWidget(self.CF_warning_lineedit, 1, 1, 1, 1)

        self.CF_alarm_lineedit = QLineEdit(self.CF_edit_group)
        self.CF_alarm_lineedit.setObjectName(u"CF_alarm_lineedit")
        self.CF_alarm_lineedit.setStyleSheet(u"color:#000000;")
        self.CF_alarm_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_2.addWidget(self.CF_alarm_lineedit, 1, 3, 1, 1)

        self.CF_min_lineedit = QLineEdit(self.CF_edit_group)
        self.CF_min_lineedit.setObjectName(u"CF_min_lineedit")
        self.CF_min_lineedit.setStyleSheet(u"color:#000000;")
        self.CF_min_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_2.addWidget(self.CF_min_lineedit, 0, 3, 1, 1)

        self.CF_max_label = QLabel(self.CF_edit_group)
        self.CF_max_label.setObjectName(u"CF_max_label")
        self.CF_max_label.setFont(font2)
        self.CF_max_label.setLayoutDirection(Qt.LeftToRight)
        self.CF_max_label.setStyleSheet(u"color: #AAAAFF;")
        self.CF_max_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CF_max_label.setMargin(2)

        self.grid_toolinfo_area1_2.addWidget(self.CF_max_label, 0, 0, 1, 1)

        self.CF_warning_label = QLabel(self.CF_edit_group)
        self.CF_warning_label.setObjectName(u"CF_warning_label")
        self.CF_warning_label.setFont(font2)
        self.CF_warning_label.setLayoutDirection(Qt.LeftToRight)
        self.CF_warning_label.setStyleSheet(u"color: #FFFFAA;")
        self.CF_warning_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.CF_warning_label.setMargin(2)

        self.grid_toolinfo_area1_2.addWidget(self.CF_warning_label, 1, 0, 1, 1)


        self.horizontalLayout_3.addWidget(self.CF_edit_group)

        self.horizontalLayout_3.setStretch(0, 1)
        self.horizontalLayout_3.setStretch(2, 5)

        self.verticalLayout_2.addWidget(self.CF)

        self.FxFy = QGroupBox(View_Window)
        self.FxFy.setObjectName(u"FxFy")
        self.FxFy.setFont(font1)
        self.FxFy.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.horizontalLayout_5 = QHBoxLayout(self.FxFy)
        self.horizontalLayout_5.setSpacing(0)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.horizontalLayout_5.setContentsMargins(9, 0, 9, 9)
        self.FxFy_Auto_checkBox = QCheckBox(self.FxFy)
        self.FxFy_Auto_checkBox.setObjectName(u"FxFy_Auto_checkBox")
        self.FxFy_Auto_checkBox.setFont(font2)
        self.FxFy_Auto_checkBox.setStyleSheet(u"QCheckBox{color:#ABABAB;}\n"
"QCheckBox::indicator {\n"
"    width: 13px;  /* \u8a2d\u5b9a\u5bec\u5ea6 */\n"
"    height: 13px; /* \u8a2d\u5b9a\u9ad8\u5ea6 */\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked:hover {\n"
"    image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked:hover {\n"
"    image: url(:/btn_check/btn_check_s1_1.png);\n"
"}")
        self.FxFy_Auto_checkBox.setIconSize(QSize(8, 8))

        self.horizontalLayout_5.addWidget(self.FxFy_Auto_checkBox)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer_4)

        self.FxFy_edit_group = QWidget(self.FxFy)
        self.FxFy_edit_group.setObjectName(u"FxFy_edit_group")
        self.grid_toolinfo_area1_4 = QGridLayout(self.FxFy_edit_group)
        self.grid_toolinfo_area1_4.setObjectName(u"grid_toolinfo_area1_4")
        self.grid_toolinfo_area1_4.setHorizontalSpacing(8)
        self.grid_toolinfo_area1_4.setVerticalSpacing(5)
        self.grid_toolinfo_area1_4.setContentsMargins(8, 0, 5, 0)
        self.FxFy_pos_peak_lineedit = QLineEdit(self.FxFy_edit_group)
        self.FxFy_pos_peak_lineedit.setObjectName(u"FxFy_pos_peak_lineedit")
        self.FxFy_pos_peak_lineedit.setStyleSheet(u"color:#000000;")
        self.FxFy_pos_peak_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_4.addWidget(self.FxFy_pos_peak_lineedit, 0, 1, 1, 1)

        self.FxFy_neg_peak_label = QLabel(self.FxFy_edit_group)
        self.FxFy_neg_peak_label.setObjectName(u"FxFy_neg_peak_label")
        self.FxFy_neg_peak_label.setFont(font2)
        self.FxFy_neg_peak_label.setLayoutDirection(Qt.LeftToRight)
        self.FxFy_neg_peak_label.setStyleSheet(u"color: #AAAAFF;")
        self.FxFy_neg_peak_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.FxFy_neg_peak_label.setMargin(2)

        self.grid_toolinfo_area1_4.addWidget(self.FxFy_neg_peak_label, 0, 2, 1, 1)

        self.FxFy_neg_peak_lineedit = QLineEdit(self.FxFy_edit_group)
        self.FxFy_neg_peak_lineedit.setObjectName(u"FxFy_neg_peak_lineedit")
        self.FxFy_neg_peak_lineedit.setStyleSheet(u"color:#000000;")
        self.FxFy_neg_peak_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_4.addWidget(self.FxFy_neg_peak_lineedit, 0, 3, 1, 1)

        self.FxFy_pos_peak_label = QLabel(self.FxFy_edit_group)
        self.FxFy_pos_peak_label.setObjectName(u"FxFy_pos_peak_label")
        self.FxFy_pos_peak_label.setFont(font2)
        self.FxFy_pos_peak_label.setLayoutDirection(Qt.LeftToRight)
        self.FxFy_pos_peak_label.setStyleSheet(u"color: #AAAAFF;")
        self.FxFy_pos_peak_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.FxFy_pos_peak_label.setMargin(2)

        self.grid_toolinfo_area1_4.addWidget(self.FxFy_pos_peak_label, 0, 0, 1, 1)


        self.horizontalLayout_5.addWidget(self.FxFy_edit_group)

        self.horizontalLayout_5.setStretch(0, 1)
        self.horizontalLayout_5.setStretch(2, 5)

        self.verticalLayout_2.addWidget(self.FxFy)

        self.Fz = QGroupBox(View_Window)
        self.Fz.setObjectName(u"Fz")
        self.Fz.setFont(font1)
        self.Fz.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.horizontalLayout_6 = QHBoxLayout(self.Fz)
        self.horizontalLayout_6.setSpacing(0)
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.horizontalLayout_6.setContentsMargins(9, 0, 9, 9)
        self.Fz_Auto_checkBox = QCheckBox(self.Fz)
        self.Fz_Auto_checkBox.setObjectName(u"Fz_Auto_checkBox")
        self.Fz_Auto_checkBox.setFont(font2)
        self.Fz_Auto_checkBox.setStyleSheet(u"QCheckBox{color:#ABABAB;}\n"
"QCheckBox::indicator {\n"
"    width: 13px;  /* \u8a2d\u5b9a\u5bec\u5ea6 */\n"
"    height: 13px; /* \u8a2d\u5b9a\u9ad8\u5ea6 */\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked:hover {\n"
"    image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked:hover {\n"
"    image: url(:/btn_check/btn_check_s1_1.png);\n"
"}")
        self.Fz_Auto_checkBox.setIconSize(QSize(8, 8))

        self.horizontalLayout_6.addWidget(self.Fz_Auto_checkBox)

        self.horizontalSpacer_7 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_6.addItem(self.horizontalSpacer_7)

        self.Fz_edit_group = QWidget(self.Fz)
        self.Fz_edit_group.setObjectName(u"Fz_edit_group")
        self.grid_toolinfo_area1_5 = QGridLayout(self.Fz_edit_group)
        self.grid_toolinfo_area1_5.setObjectName(u"grid_toolinfo_area1_5")
        self.grid_toolinfo_area1_5.setHorizontalSpacing(8)
        self.grid_toolinfo_area1_5.setVerticalSpacing(5)
        self.grid_toolinfo_area1_5.setContentsMargins(8, 0, 5, 0)
        self.Fz_neg_alarm_label = QLabel(self.Fz_edit_group)
        self.Fz_neg_alarm_label.setObjectName(u"Fz_neg_alarm_label")
        self.Fz_neg_alarm_label.setFont(font2)
        self.Fz_neg_alarm_label.setLayoutDirection(Qt.LeftToRight)
        self.Fz_neg_alarm_label.setStyleSheet(u"color:#FFAAAA;")
        self.Fz_neg_alarm_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Fz_neg_alarm_label.setMargin(2)

        self.grid_toolinfo_area1_5.addWidget(self.Fz_neg_alarm_label, 1, 2, 1, 1)

        self.Fz_neg_alarm_lineedit = QLineEdit(self.Fz_edit_group)
        self.Fz_neg_alarm_lineedit.setObjectName(u"Fz_neg_alarm_lineedit")
        self.Fz_neg_alarm_lineedit.setStyleSheet(u"color:#000000;")
        self.Fz_neg_alarm_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_5.addWidget(self.Fz_neg_alarm_lineedit, 1, 3, 1, 1)

        self.Fz_pos_alarm_lineedit = QLineEdit(self.Fz_edit_group)
        self.Fz_pos_alarm_lineedit.setObjectName(u"Fz_pos_alarm_lineedit")
        self.Fz_pos_alarm_lineedit.setStyleSheet(u"color:#000000;")
        self.Fz_pos_alarm_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_5.addWidget(self.Fz_pos_alarm_lineedit, 1, 1, 1, 1)

        self.Fz_pos_alarm_label = QLabel(self.Fz_edit_group)
        self.Fz_pos_alarm_label.setObjectName(u"Fz_pos_alarm_label")
        self.Fz_pos_alarm_label.setFont(font2)
        self.Fz_pos_alarm_label.setLayoutDirection(Qt.LeftToRight)
        self.Fz_pos_alarm_label.setStyleSheet(u"color:#FFAAAA;")
        self.Fz_pos_alarm_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Fz_pos_alarm_label.setMargin(2)

        self.grid_toolinfo_area1_5.addWidget(self.Fz_pos_alarm_label, 1, 0, 1, 1)

        self.Fz_neg_peak_label = QLabel(self.Fz_edit_group)
        self.Fz_neg_peak_label.setObjectName(u"Fz_neg_peak_label")
        self.Fz_neg_peak_label.setFont(font2)
        self.Fz_neg_peak_label.setLayoutDirection(Qt.LeftToRight)
        self.Fz_neg_peak_label.setStyleSheet(u"color: #AAAAFF;")
        self.Fz_neg_peak_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Fz_neg_peak_label.setMargin(2)

        self.grid_toolinfo_area1_5.addWidget(self.Fz_neg_peak_label, 0, 2, 1, 1)

        self.Fz_neg_peak_lineedit = QLineEdit(self.Fz_edit_group)
        self.Fz_neg_peak_lineedit.setObjectName(u"Fz_neg_peak_lineedit")
        self.Fz_neg_peak_lineedit.setStyleSheet(u"color:#000000;")
        self.Fz_neg_peak_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_5.addWidget(self.Fz_neg_peak_lineedit, 0, 3, 1, 1)

        self.Fz_pos_peak_label = QLabel(self.Fz_edit_group)
        self.Fz_pos_peak_label.setObjectName(u"Fz_pos_peak_label")
        self.Fz_pos_peak_label.setFont(font2)
        self.Fz_pos_peak_label.setLayoutDirection(Qt.LeftToRight)
        self.Fz_pos_peak_label.setStyleSheet(u"color: #AAAAFF;")
        self.Fz_pos_peak_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Fz_pos_peak_label.setMargin(2)

        self.grid_toolinfo_area1_5.addWidget(self.Fz_pos_peak_label, 0, 0, 1, 1)

        self.Fz_pos_peak_lineedit = QLineEdit(self.Fz_edit_group)
        self.Fz_pos_peak_lineedit.setObjectName(u"Fz_pos_peak_lineedit")
        self.Fz_pos_peak_lineedit.setStyleSheet(u"color:#000000;")
        self.Fz_pos_peak_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_5.addWidget(self.Fz_pos_peak_lineedit, 0, 1, 1, 1)

        self.Fz_pos_warning_label = QLabel(self.Fz_edit_group)
        self.Fz_pos_warning_label.setObjectName(u"Fz_pos_warning_label")
        self.Fz_pos_warning_label.setFont(font2)
        self.Fz_pos_warning_label.setLayoutDirection(Qt.LeftToRight)
        self.Fz_pos_warning_label.setStyleSheet(u"color: #FFFFAA;")
        self.Fz_pos_warning_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Fz_pos_warning_label.setMargin(2)

        self.grid_toolinfo_area1_5.addWidget(self.Fz_pos_warning_label, 2, 0, 1, 1)

        self.Fz_pos_warning_lineedit = QLineEdit(self.Fz_edit_group)
        self.Fz_pos_warning_lineedit.setObjectName(u"Fz_pos_warning_lineedit")
        self.Fz_pos_warning_lineedit.setStyleSheet(u"color:#000000;")
        self.Fz_pos_warning_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_5.addWidget(self.Fz_pos_warning_lineedit, 2, 1, 1, 1)

        self.Fz_neg_warning_label = QLabel(self.Fz_edit_group)
        self.Fz_neg_warning_label.setObjectName(u"Fz_neg_warning_label")
        self.Fz_neg_warning_label.setFont(font2)
        self.Fz_neg_warning_label.setLayoutDirection(Qt.LeftToRight)
        self.Fz_neg_warning_label.setStyleSheet(u"color: #FFFFAA;")
        self.Fz_neg_warning_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Fz_neg_warning_label.setMargin(2)

        self.grid_toolinfo_area1_5.addWidget(self.Fz_neg_warning_label, 2, 2, 1, 1)

        self.Fz_neg_warning_lineedit = QLineEdit(self.Fz_edit_group)
        self.Fz_neg_warning_lineedit.setObjectName(u"Fz_neg_warning_lineedit")
        self.Fz_neg_warning_lineedit.setStyleSheet(u"color:#000000;")
        self.Fz_neg_warning_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_5.addWidget(self.Fz_neg_warning_lineedit, 2, 3, 1, 1)


        self.horizontalLayout_6.addWidget(self.Fz_edit_group)

        self.horizontalLayout_6.setStretch(0, 1)
        self.horizontalLayout_6.setStretch(2, 5)

        self.verticalLayout_2.addWidget(self.Fz)

        self.Torque = QGroupBox(View_Window)
        self.Torque.setObjectName(u"Torque")
        self.Torque.setFont(font1)
        self.Torque.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.horizontalLayout_7 = QHBoxLayout(self.Torque)
        self.horizontalLayout_7.setSpacing(0)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.horizontalLayout_7.setContentsMargins(9, 0, 9, 9)
        self.Torque_Auto_checkBox = QCheckBox(self.Torque)
        self.Torque_Auto_checkBox.setObjectName(u"Torque_Auto_checkBox")
        self.Torque_Auto_checkBox.setFont(font2)
        self.Torque_Auto_checkBox.setStyleSheet(u"QCheckBox{color:#ABABAB;}\n"
"QCheckBox::indicator {\n"
"    width: 13px;  /* \u8a2d\u5b9a\u5bec\u5ea6 */\n"
"    height: 13px; /* \u8a2d\u5b9a\u9ad8\u5ea6 */\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked:hover {\n"
"    image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked:hover {\n"
"    image: url(:/btn_check/btn_check_s1_1.png);\n"
"}")
        self.Torque_Auto_checkBox.setIconSize(QSize(8, 8))

        self.horizontalLayout_7.addWidget(self.Torque_Auto_checkBox)

        self.horizontalSpacer_8 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_7.addItem(self.horizontalSpacer_8)

        self.Torque_edit_group = QWidget(self.Torque)
        self.Torque_edit_group.setObjectName(u"Torque_edit_group")
        self.grid_toolinfo_area1_6 = QGridLayout(self.Torque_edit_group)
        self.grid_toolinfo_area1_6.setObjectName(u"grid_toolinfo_area1_6")
        self.grid_toolinfo_area1_6.setHorizontalSpacing(8)
        self.grid_toolinfo_area1_6.setVerticalSpacing(5)
        self.grid_toolinfo_area1_6.setContentsMargins(8, 0, 5, 0)
        self.Torque_neg_alarm_label = QLabel(self.Torque_edit_group)
        self.Torque_neg_alarm_label.setObjectName(u"Torque_neg_alarm_label")
        self.Torque_neg_alarm_label.setFont(font2)
        self.Torque_neg_alarm_label.setLayoutDirection(Qt.LeftToRight)
        self.Torque_neg_alarm_label.setStyleSheet(u"color:#FFAAAA;")
        self.Torque_neg_alarm_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Torque_neg_alarm_label.setMargin(2)

        self.grid_toolinfo_area1_6.addWidget(self.Torque_neg_alarm_label, 1, 2, 1, 1)

        self.Torque_neg_alarm_lineedit = QLineEdit(self.Torque_edit_group)
        self.Torque_neg_alarm_lineedit.setObjectName(u"Torque_neg_alarm_lineedit")
        self.Torque_neg_alarm_lineedit.setStyleSheet(u"color:#000000;")
        self.Torque_neg_alarm_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_6.addWidget(self.Torque_neg_alarm_lineedit, 1, 3, 1, 1)

        self.Torque_pos_alarm_lineedit = QLineEdit(self.Torque_edit_group)
        self.Torque_pos_alarm_lineedit.setObjectName(u"Torque_pos_alarm_lineedit")
        self.Torque_pos_alarm_lineedit.setStyleSheet(u"color:#000000;")
        self.Torque_pos_alarm_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_6.addWidget(self.Torque_pos_alarm_lineedit, 1, 1, 1, 1)

        self.Torque_pos_alarm_label = QLabel(self.Torque_edit_group)
        self.Torque_pos_alarm_label.setObjectName(u"Torque_pos_alarm_label")
        self.Torque_pos_alarm_label.setFont(font2)
        self.Torque_pos_alarm_label.setLayoutDirection(Qt.LeftToRight)
        self.Torque_pos_alarm_label.setStyleSheet(u"color:#FFAAAA;")
        self.Torque_pos_alarm_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Torque_pos_alarm_label.setMargin(2)

        self.grid_toolinfo_area1_6.addWidget(self.Torque_pos_alarm_label, 1, 0, 1, 1)

        self.Torque_neg_peak_label = QLabel(self.Torque_edit_group)
        self.Torque_neg_peak_label.setObjectName(u"Torque_neg_peak_label")
        self.Torque_neg_peak_label.setFont(font2)
        self.Torque_neg_peak_label.setLayoutDirection(Qt.LeftToRight)
        self.Torque_neg_peak_label.setStyleSheet(u"color: #AAAAFF;")
        self.Torque_neg_peak_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Torque_neg_peak_label.setMargin(2)

        self.grid_toolinfo_area1_6.addWidget(self.Torque_neg_peak_label, 0, 2, 1, 1)

        self.Torque_neg_peak_lineedit = QLineEdit(self.Torque_edit_group)
        self.Torque_neg_peak_lineedit.setObjectName(u"Torque_neg_peak_lineedit")
        self.Torque_neg_peak_lineedit.setStyleSheet(u"color:#000000;")
        self.Torque_neg_peak_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_6.addWidget(self.Torque_neg_peak_lineedit, 0, 3, 1, 1)

        self.Torque_pos_peak_label = QLabel(self.Torque_edit_group)
        self.Torque_pos_peak_label.setObjectName(u"Torque_pos_peak_label")
        self.Torque_pos_peak_label.setFont(font2)
        self.Torque_pos_peak_label.setLayoutDirection(Qt.LeftToRight)
        self.Torque_pos_peak_label.setStyleSheet(u"color: #AAAAFF;")
        self.Torque_pos_peak_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Torque_pos_peak_label.setMargin(2)

        self.grid_toolinfo_area1_6.addWidget(self.Torque_pos_peak_label, 0, 0, 1, 1)

        self.Torque_pos_peak_lineedit = QLineEdit(self.Torque_edit_group)
        self.Torque_pos_peak_lineedit.setObjectName(u"Torque_pos_peak_lineedit")
        self.Torque_pos_peak_lineedit.setStyleSheet(u"color:#000000;")
        self.Torque_pos_peak_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_6.addWidget(self.Torque_pos_peak_lineedit, 0, 1, 1, 1)

        self.Torque_pos_warning_label = QLabel(self.Torque_edit_group)
        self.Torque_pos_warning_label.setObjectName(u"Torque_pos_warning_label")
        self.Torque_pos_warning_label.setFont(font2)
        self.Torque_pos_warning_label.setLayoutDirection(Qt.LeftToRight)
        self.Torque_pos_warning_label.setStyleSheet(u"color: #FFFFAA;")
        self.Torque_pos_warning_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Torque_pos_warning_label.setMargin(2)

        self.grid_toolinfo_area1_6.addWidget(self.Torque_pos_warning_label, 2, 0, 1, 1)

        self.Torque_pos_warning_lineedit = QLineEdit(self.Torque_edit_group)
        self.Torque_pos_warning_lineedit.setObjectName(u"Torque_pos_warning_lineedit")
        self.Torque_pos_warning_lineedit.setStyleSheet(u"color:#000000;")
        self.Torque_pos_warning_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_6.addWidget(self.Torque_pos_warning_lineedit, 2, 1, 1, 1)

        self.Torque_neg_warning_label = QLabel(self.Torque_edit_group)
        self.Torque_neg_warning_label.setObjectName(u"Torque_neg_warning_label")
        self.Torque_neg_warning_label.setFont(font2)
        self.Torque_neg_warning_label.setLayoutDirection(Qt.LeftToRight)
        self.Torque_neg_warning_label.setStyleSheet(u"color: #FFFFAA;")
        self.Torque_neg_warning_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Torque_neg_warning_label.setMargin(2)

        self.grid_toolinfo_area1_6.addWidget(self.Torque_neg_warning_label, 2, 2, 1, 1)

        self.Torque_neg_warning_lineedit = QLineEdit(self.Torque_edit_group)
        self.Torque_neg_warning_lineedit.setObjectName(u"Torque_neg_warning_lineedit")
        self.Torque_neg_warning_lineedit.setStyleSheet(u"color:#000000;")
        self.Torque_neg_warning_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_6.addWidget(self.Torque_neg_warning_lineedit, 2, 3, 1, 1)


        self.horizontalLayout_7.addWidget(self.Torque_edit_group)

        self.horizontalLayout_7.setStretch(0, 1)
        self.horizontalLayout_7.setStretch(2, 5)

        self.verticalLayout_2.addWidget(self.Torque)

        self.Temperature = QGroupBox(View_Window)
        self.Temperature.setObjectName(u"Temperature")
        self.Temperature.setFont(font1)
        self.Temperature.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.horizontalLayout_8 = QHBoxLayout(self.Temperature)
        self.horizontalLayout_8.setSpacing(0)
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.horizontalLayout_8.setContentsMargins(9, 0, 9, 9)
        self.Temp_Auto_checkBox = QCheckBox(self.Temperature)
        self.Temp_Auto_checkBox.setObjectName(u"Temp_Auto_checkBox")
        self.Temp_Auto_checkBox.setFont(font2)
        self.Temp_Auto_checkBox.setStyleSheet(u"QCheckBox{color:#ABABAB;}\n"
"QCheckBox::indicator {\n"
"    width: 13px;  /* \u8a2d\u5b9a\u5bec\u5ea6 */\n"
"    height: 13px; /* \u8a2d\u5b9a\u9ad8\u5ea6 */\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked:hover {\n"
"    image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked:hover {\n"
"    image: url(:/btn_check/btn_check_s1_1.png);\n"
"}")
        self.Temp_Auto_checkBox.setIconSize(QSize(8, 8))

        self.horizontalLayout_8.addWidget(self.Temp_Auto_checkBox)

        self.horizontalSpacer_9 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_8.addItem(self.horizontalSpacer_9)

        self.Temp_edit_group = QWidget(self.Temperature)
        self.Temp_edit_group.setObjectName(u"Temp_edit_group")
        self.grid_toolinfo_area1_7 = QGridLayout(self.Temp_edit_group)
        self.grid_toolinfo_area1_7.setObjectName(u"grid_toolinfo_area1_7")
        self.grid_toolinfo_area1_7.setHorizontalSpacing(8)
        self.grid_toolinfo_area1_7.setVerticalSpacing(5)
        self.grid_toolinfo_area1_7.setContentsMargins(8, 0, 5, 0)
        self.Temp_max_lineedit = QLineEdit(self.Temp_edit_group)
        self.Temp_max_lineedit.setObjectName(u"Temp_max_lineedit")
        self.Temp_max_lineedit.setStyleSheet(u"color:#000000;")
        self.Temp_max_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_7.addWidget(self.Temp_max_lineedit, 0, 1, 1, 1)

        self.Temp_min_label = QLabel(self.Temp_edit_group)
        self.Temp_min_label.setObjectName(u"Temp_min_label")
        self.Temp_min_label.setFont(font2)
        self.Temp_min_label.setLayoutDirection(Qt.LeftToRight)
        self.Temp_min_label.setStyleSheet(u"color: #AAFFAA;")
        self.Temp_min_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Temp_min_label.setMargin(2)

        self.grid_toolinfo_area1_7.addWidget(self.Temp_min_label, 0, 2, 1, 1)

        self.Temp_alarm_label = QLabel(self.Temp_edit_group)
        self.Temp_alarm_label.setObjectName(u"Temp_alarm_label")
        self.Temp_alarm_label.setFont(font2)
        self.Temp_alarm_label.setLayoutDirection(Qt.LeftToRight)
        self.Temp_alarm_label.setStyleSheet(u"color:#FFAAAA;")
        self.Temp_alarm_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Temp_alarm_label.setMargin(2)

        self.grid_toolinfo_area1_7.addWidget(self.Temp_alarm_label, 1, 2, 1, 1)

        self.Temp_warning_lineedit = QLineEdit(self.Temp_edit_group)
        self.Temp_warning_lineedit.setObjectName(u"Temp_warning_lineedit")
        self.Temp_warning_lineedit.setStyleSheet(u"color:#000000;")
        self.Temp_warning_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_7.addWidget(self.Temp_warning_lineedit, 1, 1, 1, 1)

        self.Temp_alarm_lineedit = QLineEdit(self.Temp_edit_group)
        self.Temp_alarm_lineedit.setObjectName(u"Temp_alarm_lineedit")
        self.Temp_alarm_lineedit.setStyleSheet(u"color:#000000;")
        self.Temp_alarm_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_7.addWidget(self.Temp_alarm_lineedit, 1, 3, 1, 1)

        self.Temp_min_lineedit = QLineEdit(self.Temp_edit_group)
        self.Temp_min_lineedit.setObjectName(u"Temp_min_lineedit")
        self.Temp_min_lineedit.setStyleSheet(u"color:#000000;")
        self.Temp_min_lineedit.setReadOnly(True)

        self.grid_toolinfo_area1_7.addWidget(self.Temp_min_lineedit, 0, 3, 1, 1)

        self.Temp_max_label = QLabel(self.Temp_edit_group)
        self.Temp_max_label.setObjectName(u"Temp_max_label")
        self.Temp_max_label.setFont(font2)
        self.Temp_max_label.setLayoutDirection(Qt.LeftToRight)
        self.Temp_max_label.setStyleSheet(u"color: #AAAAFF;")
        self.Temp_max_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Temp_max_label.setMargin(2)

        self.grid_toolinfo_area1_7.addWidget(self.Temp_max_label, 0, 0, 1, 1)

        self.Temp_warning_label = QLabel(self.Temp_edit_group)
        self.Temp_warning_label.setObjectName(u"Temp_warning_label")
        self.Temp_warning_label.setFont(font2)
        self.Temp_warning_label.setLayoutDirection(Qt.LeftToRight)
        self.Temp_warning_label.setStyleSheet(u"color: #FFFFAA;")
        self.Temp_warning_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.Temp_warning_label.setMargin(2)

        self.grid_toolinfo_area1_7.addWidget(self.Temp_warning_label, 1, 0, 1, 1)


        self.horizontalLayout_8.addWidget(self.Temp_edit_group)

        self.horizontalLayout_8.setStretch(0, 1)
        self.horizontalLayout_8.setStretch(2, 5)

        self.verticalLayout_2.addWidget(self.Temperature)

        self.view1_save_close = QWidget(View_Window)
        self.view1_save_close.setObjectName(u"view1_save_close")
        self.horizontalLayout_10 = QHBoxLayout(self.view1_save_close)
        self.horizontalLayout_10.setSpacing(0)
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.horizontalLayout_10.setContentsMargins(0, 0, 0, 0)
        self.view_OpenAW_checkBox = QCheckBox(self.view1_save_close)
        self.view_OpenAW_checkBox.setObjectName(u"view_OpenAW_checkBox")
        self.view_OpenAW_checkBox.setFont(font2)
        self.view_OpenAW_checkBox.setStyleSheet(u"QCheckBox{color:#ABABAB;}\n"
"QCheckBox::indicator {\n"
"    width: 13px;  /* \u8a2d\u5b9a\u5bec\u5ea6 */\n"
"    height: 13px; /* \u8a2d\u5b9a\u9ad8\u5ea6 */\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked:hover {\n"
"    image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked:hover {\n"
"    image: url(:/btn_check/btn_check_s1_1.png);\n"
"}")
        self.view_OpenAW_checkBox.setIconSize(QSize(8, 8))

        self.horizontalLayout_10.addWidget(self.view_OpenAW_checkBox)

        self.horizontalSpacer_11 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_10.addItem(self.horizontalSpacer_11)

        self.view1_read_btn = QPushButton(self.view1_save_close)
        self.view1_read_btn.setObjectName(u"view1_read_btn")
        self.view1_read_btn.setMinimumSize(QSize(10, 10))
        self.view1_read_btn.setFont(font1)
        self.view1_read_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color:#5448B6;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7AFEC6;\n"
"     color: rgb(255, 255, 255);\n"
"    border:none\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border:none\n"
"}")
        self.view1_read_btn.setIconSize(QSize(20, 20))
        self.view1_read_btn.setCheckable(True)

        self.horizontalLayout_10.addWidget(self.view1_read_btn)

        self.horizontalSpacer_6 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_10.addItem(self.horizontalSpacer_6)

        self.view1_save_btn = QPushButton(self.view1_save_close)
        self.view1_save_btn.setObjectName(u"view1_save_btn")
        self.view1_save_btn.setMinimumSize(QSize(10, 10))
        self.view1_save_btn.setFont(font1)
        self.view1_save_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: 2px solid #7AFEC6;\n"
"    color: rgb(255, 255, 255);\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")
        self.view1_save_btn.setIconSize(QSize(20, 20))
        self.view1_save_btn.setCheckable(True)

        self.horizontalLayout_10.addWidget(self.view1_save_btn)

        self.horizontalLayout_10.setStretch(1, 8)
        self.horizontalLayout_10.setStretch(2, 3)
        self.horizontalLayout_10.setStretch(3, 1)
        self.horizontalLayout_10.setStretch(4, 3)

        self.verticalLayout_2.addWidget(self.view1_save_close)


        self.verticalLayout_3.addLayout(self.verticalLayout_2)

        self.view_title_2 = QWidget(View_Window)
        self.view_title_2.setObjectName(u"view_title_2")
        self.view_title_2.setStyleSheet(u"QWidget#filter_title_2{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout_2 = QHBoxLayout(self.view_title_2)
        self.horizontalLayout_2.setSpacing(3)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.view_title_label_2 = QLabel(self.view_title_2)
        self.view_title_label_2.setObjectName(u"view_title_label_2")
        self.view_title_label_2.setFont(font)
        self.view_title_label_2.setLayoutDirection(Qt.LeftToRight)
        self.view_title_label_2.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.view_title_label_2.setAlignment(Qt.AlignCenter)
        self.view_title_label_2.setMargin(2)

        self.horizontalLayout_2.addWidget(self.view_title_label_2)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_2)


        self.verticalLayout_3.addWidget(self.view_title_2)

        self.view2_layout = QVBoxLayout()
        self.view2_layout.setObjectName(u"view2_layout")
        self.view2_layout.setContentsMargins(9, -1, 9, -1)
        self.ColorSpectrum = QHBoxLayout()
        self.ColorSpectrum.setObjectName(u"ColorSpectrum")
        self.ColorSpectrum_radio = QRadioButton(View_Window)
        self.ColorSpectrum_radio.setObjectName(u"ColorSpectrum_radio")
        self.ColorSpectrum_radio.setFont(font2)
        self.ColorSpectrum_radio.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
"\n"
"\n"
"QRadioButton::indicator:unchecked{\n"
"image: url(:/btn_radio/btn_radio_s0_0.png);\n"
"}\n"
"QRadioButton::indicator:unchecked:hover {\n"
"image: url(:/btn_radio/btn_radio_s0_1.png);\n"
"}\n"
"QRadioButton::indicator:checked{\n"
"image: url(:/btn_radio/btn_radio_s1_0.png);\n"
"}\n"
"QRadioButton::indicator:checked:hover {\n"
"image: url(:/btn_radio/btn_radio_s1_1.png);\n"
"}\n"
"	")
        icon = QIcon()
        icon.addFile(u":/color_spectrum/color_spectrum_1.png", QSize(), QIcon.Normal, QIcon.Off)
        self.ColorSpectrum_radio.setIcon(icon)
        self.ColorSpectrum_radio.setIconSize(QSize(30, 30))

        self.ColorSpectrum.addWidget(self.ColorSpectrum_radio)

        self.ColorSpectrum_radio_2 = QRadioButton(View_Window)
        self.ColorSpectrum_radio_2.setObjectName(u"ColorSpectrum_radio_2")
        self.ColorSpectrum_radio_2.setFont(font2)
        self.ColorSpectrum_radio_2.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
"\n"
"\n"
"QRadioButton::indicator:unchecked{\n"
"image: url(:/btn_radio/btn_radio_s0_0.png);\n"
"}\n"
"QRadioButton::indicator:unchecked:hover {\n"
"image: url(:/btn_radio/btn_radio_s0_1.png);\n"
"}\n"
"QRadioButton::indicator:checked{\n"
"image: url(:/btn_radio/btn_radio_s1_0.png);\n"
"}\n"
"QRadioButton::indicator:checked:hover {\n"
"image: url(:/btn_radio/btn_radio_s1_1.png);\n"
"}\n"
"	")
        icon1 = QIcon()
        icon1.addFile(u":/color_spectrum/color_spectrum_2.png", QSize(), QIcon.Normal, QIcon.Off)
        self.ColorSpectrum_radio_2.setIcon(icon1)
        self.ColorSpectrum_radio_2.setIconSize(QSize(30, 30))

        self.ColorSpectrum.addWidget(self.ColorSpectrum_radio_2)

        self.ColorSpectrum_radio_3 = QRadioButton(View_Window)
        self.ColorSpectrum_radio_3.setObjectName(u"ColorSpectrum_radio_3")
        self.ColorSpectrum_radio_3.setFont(font2)
        self.ColorSpectrum_radio_3.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
"\n"
"\n"
"QRadioButton::indicator:unchecked{\n"
"image: url(:/btn_radio/btn_radio_s0_0.png);\n"
"}\n"
"QRadioButton::indicator:unchecked:hover {\n"
"image: url(:/btn_radio/btn_radio_s0_1.png);\n"
"}\n"
"QRadioButton::indicator:checked{\n"
"image: url(:/btn_radio/btn_radio_s1_0.png);\n"
"}\n"
"QRadioButton::indicator:checked:hover {\n"
"image: url(:/btn_radio/btn_radio_s1_1.png);\n"
"}\n"
"	")
        icon2 = QIcon()
        icon2.addFile(u":/color_spectrum/color_spectrum_3.png", QSize(), QIcon.Normal, QIcon.Off)
        self.ColorSpectrum_radio_3.setIcon(icon2)
        self.ColorSpectrum_radio_3.setIconSize(QSize(30, 30))

        self.ColorSpectrum.addWidget(self.ColorSpectrum_radio_3)

        self.ColorSpectrum_radio_4 = QRadioButton(View_Window)
        self.ColorSpectrum_radio_4.setObjectName(u"ColorSpectrum_radio_4")
        self.ColorSpectrum_radio_4.setFont(font2)
        self.ColorSpectrum_radio_4.setStyleSheet(u"QRadioButton{color: rgb(255, 255, 255);}\n"
"\n"
"\n"
"QRadioButton::indicator:unchecked{\n"
"image: url(:/btn_radio/btn_radio_s0_0.png);\n"
"}\n"
"QRadioButton::indicator:unchecked:hover {\n"
"image: url(:/btn_radio/btn_radio_s0_1.png);\n"
"}\n"
"QRadioButton::indicator:checked{\n"
"image: url(:/btn_radio/btn_radio_s1_0.png);\n"
"}\n"
"QRadioButton::indicator:checked:hover {\n"
"image: url(:/btn_radio/btn_radio_s1_1.png);\n"
"}\n"
"	")
        icon3 = QIcon()
        icon3.addFile(u":/color_spectrum/color_splitedge.png", QSize(), QIcon.Normal, QIcon.Off)
        self.ColorSpectrum_radio_4.setIcon(icon3)
        self.ColorSpectrum_radio_4.setIconSize(QSize(30, 30))

        self.ColorSpectrum.addWidget(self.ColorSpectrum_radio_4)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.ColorSpectrum.addItem(self.horizontalSpacer)


        self.view2_layout.addLayout(self.ColorSpectrum)

        self.view_HideRing_checkBox = QCheckBox(View_Window)
        self.view_HideRing_checkBox.setObjectName(u"view_HideRing_checkBox")
        self.view_HideRing_checkBox.setFont(font2)
        self.view_HideRing_checkBox.setStyleSheet(u"QCheckBox{color:#ABABAB;}\n"
"QCheckBox::indicator {\n"
"    width: 13px;  /* \u8a2d\u5b9a\u5bec\u5ea6 */\n"
"    height: 13px; /* \u8a2d\u5b9a\u9ad8\u5ea6 */\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked:hover {\n"
"    image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked:hover {\n"
"    image: url(:/btn_check/btn_check_s1_1.png);\n"
"}")
        self.view_HideRing_checkBox.setIconSize(QSize(8, 8))

        self.view2_layout.addWidget(self.view_HideRing_checkBox)

        self.view2_save_close = QWidget(View_Window)
        self.view2_save_close.setObjectName(u"view2_save_close")
        self.horizontalLayout_9 = QHBoxLayout(self.view2_save_close)
        self.horizontalLayout_9.setSpacing(0)
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.view2_HideScale_checkBox = QCheckBox(self.view2_save_close)
        self.view2_HideScale_checkBox.setObjectName(u"view2_HideScale_checkBox")
        self.view2_HideScale_checkBox.setFont(font2)
        self.view2_HideScale_checkBox.setStyleSheet(u"QCheckBox{color:#ABABAB;}\n"
"QCheckBox::indicator {\n"
"    width: 13px;  /* \u8a2d\u5b9a\u5bec\u5ea6 */\n"
"    height: 13px; /* \u8a2d\u5b9a\u9ad8\u5ea6 */\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked {\n"
"    image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:unchecked:hover {\n"
"    image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked {\n"
"    image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"\n"
"QCheckBox::indicator:checked:hover {\n"
"    image: url(:/btn_check/btn_check_s1_1.png);\n"
"}")
        self.view2_HideScale_checkBox.setIconSize(QSize(8, 8))

        self.horizontalLayout_9.addWidget(self.view2_HideScale_checkBox)

        self.horizontalSpacer_10 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_10)

        self.view2_save_btn = QPushButton(self.view2_save_close)
        self.view2_save_btn.setObjectName(u"view2_save_btn")
        self.view2_save_btn.setMinimumSize(QSize(10, 10))
        self.view2_save_btn.setFont(font1)
        self.view2_save_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color:#5448B6;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7AFEC6;\n"
"     color: rgb(255, 255, 255);\n"
"    border:none\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border:none\n"
"}")
        self.view2_save_btn.setIconSize(QSize(20, 20))
        self.view2_save_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.view2_save_btn)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_5)

        self.view2_close_btn = QPushButton(self.view2_save_close)
        self.view2_close_btn.setObjectName(u"view2_close_btn")
        self.view2_close_btn.setMinimumSize(QSize(10, 10))
        self.view2_close_btn.setFont(font1)
        self.view2_close_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: 2px solid #7AFEC6;\n"
"    color: rgb(255, 255, 255);\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")
        self.view2_close_btn.setIconSize(QSize(20, 20))
        self.view2_close_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.view2_close_btn)

        self.horizontalLayout_9.setStretch(1, 8)
        self.horizontalLayout_9.setStretch(2, 3)
        self.horizontalLayout_9.setStretch(3, 1)
        self.horizontalLayout_9.setStretch(4, 3)

        self.view2_layout.addWidget(self.view2_save_close)


        self.verticalLayout_3.addLayout(self.view2_layout)


        self.retranslateUi(View_Window)

        QMetaObject.connectSlotsByName(View_Window)
    # setupUi

    def retranslateUi(self, View_Window):
        View_Window.setWindowTitle(QCoreApplication.translate("View_Window", u"Form", None))
        self.view_title_label.setText(QCoreApplication.translate("View_Window", u"\u8b66\u793a/\u8b66\u544a\u986f\u793a\u95be\u503c", None))
        self.CF.setTitle(QCoreApplication.translate("View_Window", u"C.F.[Nm]", None))
        self.CF_Auto_checkBox.setText(QCoreApplication.translate("View_Window", u"\u81ea\u52d5", None))
        self.CF_min_label.setText(QCoreApplication.translate("View_Window", u"\u6700\u5c0f\u503c :", None))
        self.CF_alarm_label.setText(QCoreApplication.translate("View_Window", u"\u8b66\u793a :", None))
        self.CF_warning_lineedit.setText("")
        self.CF_max_label.setText(QCoreApplication.translate("View_Window", u"\u6700\u5927\u503c :", None))
        self.CF_warning_label.setText(QCoreApplication.translate("View_Window", u"\u8b66\u544a :", None))
        self.FxFy.setTitle(QCoreApplication.translate("View_Window", u"Fx,Fy[Nm]", None))
        self.FxFy_Auto_checkBox.setText(QCoreApplication.translate("View_Window", u"\u81ea\u52d5", None))
        self.FxFy_neg_peak_label.setText(QCoreApplication.translate("View_Window", u"(-)\u5cf0\u503c :", None))
        self.FxFy_pos_peak_label.setText(QCoreApplication.translate("View_Window", u"(+)\u5cf0\u503c :", None))
        self.Fz.setTitle(QCoreApplication.translate("View_Window", u"Fz[N]", None))
        self.Fz_Auto_checkBox.setText(QCoreApplication.translate("View_Window", u"\u81ea\u52d5", None))
        self.Fz_neg_alarm_label.setText(QCoreApplication.translate("View_Window", u"(-)\u8b66\u793a :", None))
        self.Fz_pos_alarm_lineedit.setText("")
        self.Fz_pos_alarm_label.setText(QCoreApplication.translate("View_Window", u"(+)\u8b66\u793a :", None))
        self.Fz_neg_peak_label.setText(QCoreApplication.translate("View_Window", u"(-)\u5cf0\u503c :", None))
        self.Fz_pos_peak_label.setText(QCoreApplication.translate("View_Window", u"(+)\u5cf0\u503c :", None))
        self.Fz_pos_warning_label.setText(QCoreApplication.translate("View_Window", u"(+)\u8b66\u544a :", None))
        self.Fz_neg_warning_label.setText(QCoreApplication.translate("View_Window", u"(-)\u8b66\u544a :", None))
        self.Torque.setTitle(QCoreApplication.translate("View_Window", u"Torque[Nm]", None))
        self.Torque_Auto_checkBox.setText(QCoreApplication.translate("View_Window", u"\u81ea\u52d5", None))
        self.Torque_neg_alarm_label.setText(QCoreApplication.translate("View_Window", u"(-)\u8b66\u793a :", None))
        self.Torque_pos_alarm_lineedit.setText("")
        self.Torque_pos_alarm_label.setText(QCoreApplication.translate("View_Window", u"(+)\u8b66\u793a :", None))
        self.Torque_neg_peak_label.setText(QCoreApplication.translate("View_Window", u"(-)\u5cf0\u503c :", None))
        self.Torque_pos_peak_label.setText(QCoreApplication.translate("View_Window", u"(+)\u5cf0\u503c :", None))
        self.Torque_pos_warning_label.setText(QCoreApplication.translate("View_Window", u"(+)\u8b66\u544a :", None))
        self.Torque_neg_warning_label.setText(QCoreApplication.translate("View_Window", u"(-)\u8b66\u544a :", None))
        self.Temperature.setTitle(QCoreApplication.translate("View_Window", u"\u6eab\u5ea6[\u00b0C]", None))
        self.Temp_Auto_checkBox.setText(QCoreApplication.translate("View_Window", u"\u81ea\u52d5", None))
        self.Temp_min_label.setText(QCoreApplication.translate("View_Window", u"\u6700\u5c0f\u503c :", None))
        self.Temp_alarm_label.setText(QCoreApplication.translate("View_Window", u"\u8b66\u793a :", None))
        self.Temp_warning_lineedit.setText("")
        self.Temp_max_label.setText(QCoreApplication.translate("View_Window", u"\u6700\u5927\u503c :", None))
        self.Temp_warning_label.setText(QCoreApplication.translate("View_Window", u"\u8b66\u544a :", None))
        self.view_OpenAW_checkBox.setText(QCoreApplication.translate("View_Window", u"\u986f\u793a\u8b66\u793a / \u8b66\u544a", None))
        self.view1_read_btn.setText(QCoreApplication.translate("View_Window", u"\u8b80\u53d6\u503c", None))
        self.view1_save_btn.setText(QCoreApplication.translate("View_Window", u"\u5132\u5b58\u503c", None))
        self.view_title_label_2.setText(QCoreApplication.translate("View_Window", u"\u5176\u5b83\u986f\u793a\u8a2d\u5b9a", None))
        self.ColorSpectrum_radio.setText("")
        self.ColorSpectrum_radio_2.setText("")
        self.ColorSpectrum_radio_3.setText("")
        self.ColorSpectrum_radio_4.setText("")
        self.view_HideRing_checkBox.setText(QCoreApplication.translate("View_Window", u"\u96b1\u85cf Fz / Torque", None))
        self.view2_HideScale_checkBox.setText(QCoreApplication.translate("View_Window", u"\u96b1\u85cf3D\u8f14\u52a9\u7dda", None))
        self.view2_save_btn.setText(QCoreApplication.translate("View_Window", u"\u5132\u5b58", None))
        self.view2_close_btn.setText(QCoreApplication.translate("View_Window", u"\u53d6\u6d88", None))
    # retranslateUi

