<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>500</width>
    <height>200</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>500</width>
    <height>200</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>500</width>
    <height>200</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Dialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="5,1">
   <property name="leftMargin">
    <number>9</number>
   </property>
   <property name="topMargin">
    <number>9</number>
   </property>
   <property name="rightMargin">
    <number>9</number>
   </property>
   <property name="bottomMargin">
    <number>9</number>
   </property>
   <item>
    <widget class="QLabel" name="message">
     <property name="font">
      <font>
       <family>Arial Black</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="layoutDirection">
      <enum>Qt::LeftToRight</enum>
     </property>
     <property name="text">
      <string>message</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="btn_yes_no" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="9,3,1,3">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <spacer name="horizontalSpacer_10">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="btv_yes_btn">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color:#5448B6;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color:#7AFEC6;
     color: rgb(255, 255, 255);
    border:none
}

QPushButton:pressed {
    background-color: gray;
	border:none
}</string>
        </property>
        <property name="text">
         <string>是</string>
        </property>
        <property name="iconSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_5">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="btn_no_btn">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
	background-color: #7AFEC6;
border: none;
}

QPushButton:pressed {
    background-color: gray;
	border: none;
}</string>
        </property>
        <property name="text">
         <string>否</string>
        </property>
        <property name="iconSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
