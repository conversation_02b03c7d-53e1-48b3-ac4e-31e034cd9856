from PySide2.QtCore import QObject, QRunnable, QThreadPool, Signal, Slot
import socket
import time
from PySide2.QtWidgets import QApplication

class WorkerSignals(QObject):
    """定義工作執行緒的信號"""
    status_updated = Signal(int,str, int, bool)  # (IP, Port, 狀態)
    finished = Signal(int, str, int)

class ConnectionTester(QRunnable):
    """連線測試工作執行緒"""
    
    def __init__(self,tool_id, device_ip, port):
        super().__init__()
        self.tool_id = tool_id
        self.device_ip = device_ip
        self.port = port
        self.is_running = True
        self.signals = WorkerSignals()

    @Slot()
    def run(self):
        while self.is_running:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex((self.device_ip, self.port))
                success = (result == 0)
                self.signals.status_updated.emit(self.tool_id ,self.device_ip, self.port, success)
                sock.close()
            except Exception:
                self.signals.status_updated.emit(self.tool_id ,self.device_ip, self.port, False)
            time.sleep(7)  # 每 7 秒測試一次
        self.signals.finished.emit(self.tool_id ,self.device_ip, self.port)

    def stop(self):
        self.is_running = False

class ConnectionMonitor(QObject):
    """管理多個裝置連線狀態"""
    status_updated = Signal(int, str, int, bool)  # (IP, Port, 狀態)
    device_stopped = Signal(str, int)  # (IP, Port)

    def __init__(self):
        super().__init__()
        self.threadpool = QThreadPool()
        self.threadpool.setMaxThreadCount(10)
        self.devices = {}  # 儲存執行緒: {(ip, port): tester}

    def start_testing(self, tool_id ,ip, port):
        
        """開始測試特定裝置"""
        device_key = (tool_id ,ip, port)
        if device_key not in self.devices:
            tester = ConnectionTester(tool_id ,ip, port)
            tester.signals.status_updated.connect(self.status_updated.emit)
            tester.signals.finished.connect(self.device_stopped.emit)
            self.devices[device_key] = tester
            self.threadpool.start(tester)

    def stop_testing(self, tool_id ,ip, port):
        """停止測試特定裝置"""
        device_key = (tool_id ,ip, port)
        if device_key in self.devices:
            self.devices[device_key].stop()
            del self.devices[device_key]

    def stop_all(self):
        """停止所有測試"""
        for tester in self.devices.values():
            tester.stop()
        self.devices.clear()

    def __del__(self):
        self.stop_all()

# if __name__ == "__main__":
#     app = QApplication([])
#     monitor = ConnectionMonitor()
#     monitor.status_updated.connect(lambda tool_id,ip, port, status: print(f"{tool_id}:{ip}:{port} - {'成功' if status else '失敗'}"))
    
#     # 測試多個裝置
#     device_list = [
#         (1,"*************", 1333),
#         (2,"*************", 1333),
#         (3,"*************", 1333),
#         (4,"*************", 1333),
#     ]
#     for tool_id,ip, port in device_list:
#         monitor.start_testing(tool_id,ip, port)
    
#     app.exec_()
