"""
Unit tests for ScriptStatus window integration
"""

import unittest
from unittest.mock import Mock, patch
from PySide2.QtWidgets import QApplication
import sys

from app.views.view import View
from app.models.record_script_manager import RecordScriptManager, ScriptState
from app.models.record_manager import RecordManager


class TestScriptStatusWindow(unittest.TestCase):
    """Test cases for ScriptStatus window integration"""
    
    @classmethod
    def setUpClass(cls):
        """Set up QApplication for testing"""
        cls.app = QApplication.instance()
        if cls.app is None:
            cls.app = QApplication(sys.argv)
    
    def setUp(self):
        """Set up test fixtures"""
        # Create record manager for testing
        self.record_manager = RecordManager()
        
        # Create record script manager with new constructor
        self.record_script_manager = RecordScriptManager(None, self.record_manager)
        
        # Create view
        self.view = View()
    
    def tearDown(self):
        """Clean up test fixtures"""
        if hasattr(self.view, 'ScriptStatus_window') and self.view.ScriptStatus_window:
            self.view.ScriptStatus_window.close()
            self.view.ScriptStatus_window = None
    
    def test_script_status_window_creation(self):
        """Test ScriptStatus window creation"""
        # Initially window should not exist
        self.assertIsNone(self.view.ScriptStatus_window)
        
        # Create window (no longer passes record_script_manager directly)
        self.view.show_ScriptStatus_window()
        
        # Window should now exist
        self.assertIsNotNone(self.view.ScriptStatus_window)
        self.assertIsNotNone(self.view.ui_ScriptStatus_window)
    
    def test_script_status_window_ui_elements(self):
        """Test that UI elements are properly created"""
        self.view.show_ScriptStatus_window()
        
        # Check that UI elements exist
        ui = self.view.ui_ScriptStatus_window
        self.assertIsNotNone(ui.title_label)
        self.assertIsNotNone(ui.status_label)
        self.assertIsNotNone(ui.pause_play_btn)
        self.assertIsNotNone(ui.cancel_btn)
        self.assertIsNotNone(ui.skip_btn)
        self.assertIsNotNone(ui.close_button)
    
    def test_script_status_window_button_connections(self):
        """Test that buttons are properly connected"""
        self.view.show_ScriptStatus_window()
        
        # Check that buttons exist and are properly created
        ui = self.view.ui_ScriptStatus_window
        self.assertIsNotNone(ui.pause_play_btn)
        self.assertIsNotNone(ui.cancel_btn)
        self.assertIsNotNone(ui.skip_btn)
        self.assertIsNotNone(ui.close_button)
        
        # Check that buttons are enabled and visible
        self.assertTrue(ui.pause_play_btn.isEnabled())
        self.assertTrue(ui.cancel_btn.isEnabled())
        self.assertTrue(ui.skip_btn.isEnabled())
        self.assertTrue(ui.close_button.isEnabled())
    
    def test_script_status_window_signal_emissions(self):
        """Test that View emits signals for script actions"""
        self.view.show_ScriptStatus_window()
        
        # Check that View has the script action signal
        self.assertTrue(hasattr(self.view, 'sigScriptAction'))
        
        # Test that buttons emit signals (this would be tested with a signal spy in real Qt testing)
        # For now, just verify the signal exists
        self.assertIsNotNone(self.view.sigScriptAction)
    
    def test_script_status_update(self):
        """Test status update functionality"""
        self.view.show_ScriptStatus_window()
        
        # Test status update
        test_status = "測試狀態"
        self.view.update_script_status(test_status)
        
        # Check that status label was updated
        self.assertEqual(self.view.ui_ScriptStatus_window.status_label.text(), test_status)
    
    def test_script_progress_update(self):
        """Test progress update functionality"""
        self.view.show_ScriptStatus_window()
        
        # Test progress update
        test_progress = 50.0
        self.view._update_script_progress(test_progress)
        
        # Check that status label was updated with progress
        expected_text = f"進度: {test_progress:.1f}%"
        self.assertEqual(self.view.ui_ScriptStatus_window.status_label.text(), expected_text)
    
    def test_script_error_display(self):
        """Test error display functionality"""
        self.view.show_ScriptStatus_window()
        
        # Test error display
        test_error = "測試錯誤"
        self.view.show_script_error(test_error, user_action_required=True)
        
        # Check that error is displayed
        expected_text = f"錯誤: {test_error}"
        self.assertEqual(self.view.ui_ScriptStatus_window.status_label.text(), expected_text)
        
        # Check that error button group is visible and normal button group is hidden
        self.assertTrue(self.view.ui_ScriptStatus_window.skip_cancel_btn_group.isVisible())
        self.assertFalse(self.view.ui_ScriptStatus_window.pause_play_cancel_btn_group.isVisible())
    
    def test_script_status_window_singleton_behavior(self):
        """Test that only one ScriptStatus window is created"""
        # Create first window
        self.view.show_ScriptStatus_window()
        first_window = self.view.ScriptStatus_window
        
        # Create second window (should reuse the first one)
        self.view.show_ScriptStatus_window()
        second_window = self.view.ScriptStatus_window
        
        # Should be the same window instance
        self.assertIs(first_window, second_window)
    
    def test_pause_play_button_functionality(self):
        """Test pause/play button functionality"""
        self.view.show_ScriptStatus_window()
        
        # Test initial state
        self.assertEqual(self.view.ui_ScriptStatus_window.pause_play_btn.text(), "暫停")
        self.assertFalse(self.view.ui_ScriptStatus_window.pause_play_btn.isChecked())
        
        # Test button click (this would emit signals in real usage)
        # For now, just verify the button exists and has the right text
        self.assertIsNotNone(self.view.ui_ScriptStatus_window.pause_play_btn)


if __name__ == '__main__':
    unittest.main() 