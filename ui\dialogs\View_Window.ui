<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>View_Window</class>
 <widget class="QWidget" name="View_Window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>500</width>
    <height>700</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>500</width>
    <height>700</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>500</width>
    <height>700</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#View_Window{
background-color: #0C0C44;
border: 2px solid #5448B6;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <property name="spacing">
    <number>10</number>
   </property>
   <item>
    <widget class="QWidget" name="view_title" native="true">
     <property name="styleSheet">
      <string notr="true">QWidget#filter_title{
background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));
border-radius:5px;
}
</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>3</number>
      </property>
      <property name="leftMargin">
       <number>5</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel" name="view_title_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>警示/警告顯示閾值</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_1">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <property name="spacing">
      <number>6</number>
     </property>
     <property name="leftMargin">
      <number>9</number>
     </property>
     <property name="rightMargin">
      <number>9</number>
     </property>
     <item>
      <widget class="QGroupBox" name="CF">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(255, 255, 255);</string>
       </property>
       <property name="title">
        <string>C.F.[Nm]</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="1,0,5">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>9</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>9</number>
        </property>
        <property name="bottomMargin">
         <number>9</number>
        </property>
        <item>
         <widget class="QCheckBox" name="CF_Auto_checkBox">
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QCheckBox{color:#ABABAB;}
QCheckBox::indicator {
    width: 13px;  /* 設定寬度 */
    height: 13px; /* 設定高度 */
}

QCheckBox::indicator:unchecked {
    image: url(:/btn_check/btn_check_s0_0.png);
}

QCheckBox::indicator:unchecked:hover {
    image: url(:/btn_check/btn_check_s0_1.png);
}

QCheckBox::indicator:checked {
    image: url(:/btn_check/btn_check_s1_0.png);
}

QCheckBox::indicator:checked:hover {
    image: url(:/btn_check/btn_check_s1_1.png);
}</string>
          </property>
          <property name="text">
           <string>自動</string>
          </property>
          <property name="iconSize">
           <size>
            <width>8</width>
            <height>8</height>
           </size>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QWidget" name="CF_edit_group" native="true">
          <layout class="QGridLayout" name="grid_toolinfo_area1_2">
           <property name="leftMargin">
            <number>8</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>5</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="horizontalSpacing">
            <number>8</number>
           </property>
           <property name="verticalSpacing">
            <number>5</number>
           </property>
           <item row="0" column="1">
            <widget class="QLineEdit" name="CF_max_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QLabel" name="CF_min_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #AAFFAA;</string>
             </property>
             <property name="text">
              <string>最小值 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QLabel" name="CF_alarm_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color:#FFAAAA;</string>
             </property>
             <property name="text">
              <string>警示 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="CF_warning_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="3">
            <widget class="QLineEdit" name="CF_alarm_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QLineEdit" name="CF_min_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="CF_max_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #AAAAFF;</string>
             </property>
             <property name="text">
              <string>最大值 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="CF_warning_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #FFFFAA;</string>
             </property>
             <property name="text">
              <string>警告 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="FxFy">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(255, 255, 255);</string>
       </property>
       <property name="title">
        <string>Fx,Fy[Nm]</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_5" stretch="1,0,5">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>9</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>9</number>
        </property>
        <property name="bottomMargin">
         <number>9</number>
        </property>
        <item>
         <widget class="QCheckBox" name="FxFy_Auto_checkBox">
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QCheckBox{color:#ABABAB;}
QCheckBox::indicator {
    width: 13px;  /* 設定寬度 */
    height: 13px; /* 設定高度 */
}

QCheckBox::indicator:unchecked {
    image: url(:/btn_check/btn_check_s0_0.png);
}

QCheckBox::indicator:unchecked:hover {
    image: url(:/btn_check/btn_check_s0_1.png);
}

QCheckBox::indicator:checked {
    image: url(:/btn_check/btn_check_s1_0.png);
}

QCheckBox::indicator:checked:hover {
    image: url(:/btn_check/btn_check_s1_1.png);
}</string>
          </property>
          <property name="text">
           <string>自動</string>
          </property>
          <property name="iconSize">
           <size>
            <width>8</width>
            <height>8</height>
           </size>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QWidget" name="FxFy_edit_group" native="true">
          <layout class="QGridLayout" name="grid_toolinfo_area1_4">
           <property name="leftMargin">
            <number>8</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>5</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="horizontalSpacing">
            <number>8</number>
           </property>
           <property name="verticalSpacing">
            <number>5</number>
           </property>
           <item row="0" column="1">
            <widget class="QLineEdit" name="FxFy_pos_peak_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QLabel" name="FxFy_neg_peak_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #AAAAFF;</string>
             </property>
             <property name="text">
              <string>(-)峰值 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QLineEdit" name="FxFy_neg_peak_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="FxFy_pos_peak_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #AAAAFF;</string>
             </property>
             <property name="text">
              <string>(+)峰值 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="Fz">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(255, 255, 255);</string>
       </property>
       <property name="title">
        <string>Fz[N]</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_6" stretch="1,0,5">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>9</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>9</number>
        </property>
        <property name="bottomMargin">
         <number>9</number>
        </property>
        <item>
         <widget class="QCheckBox" name="Fz_Auto_checkBox">
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QCheckBox{color:#ABABAB;}
QCheckBox::indicator {
    width: 13px;  /* 設定寬度 */
    height: 13px; /* 設定高度 */
}

QCheckBox::indicator:unchecked {
    image: url(:/btn_check/btn_check_s0_0.png);
}

QCheckBox::indicator:unchecked:hover {
    image: url(:/btn_check/btn_check_s0_1.png);
}

QCheckBox::indicator:checked {
    image: url(:/btn_check/btn_check_s1_0.png);
}

QCheckBox::indicator:checked:hover {
    image: url(:/btn_check/btn_check_s1_1.png);
}</string>
          </property>
          <property name="text">
           <string>自動</string>
          </property>
          <property name="iconSize">
           <size>
            <width>8</width>
            <height>8</height>
           </size>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_7">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QWidget" name="Fz_edit_group" native="true">
          <layout class="QGridLayout" name="grid_toolinfo_area1_5">
           <property name="leftMargin">
            <number>8</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>5</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="horizontalSpacing">
            <number>8</number>
           </property>
           <property name="verticalSpacing">
            <number>5</number>
           </property>
           <item row="1" column="2">
            <widget class="QLabel" name="Fz_neg_alarm_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color:#FFAAAA;</string>
             </property>
             <property name="text">
              <string>(-)警示 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="1" column="3">
            <widget class="QLineEdit" name="Fz_neg_alarm_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="Fz_pos_alarm_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="Fz_pos_alarm_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color:#FFAAAA;</string>
             </property>
             <property name="text">
              <string>(+)警示 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QLabel" name="Fz_neg_peak_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #AAAAFF;</string>
             </property>
             <property name="text">
              <string>(-)峰值 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QLineEdit" name="Fz_neg_peak_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="Fz_pos_peak_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #AAAAFF;</string>
             </property>
             <property name="text">
              <string>(+)峰值 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLineEdit" name="Fz_pos_peak_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="Fz_pos_warning_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #FFFFAA;</string>
             </property>
             <property name="text">
              <string>(+)警告 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="Fz_pos_warning_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="2">
            <widget class="QLabel" name="Fz_neg_warning_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #FFFFAA;</string>
             </property>
             <property name="text">
              <string>(-)警告 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="2" column="3">
            <widget class="QLineEdit" name="Fz_neg_warning_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="Torque">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(255, 255, 255);</string>
       </property>
       <property name="title">
        <string>Torque[Nm]</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="1,0,5">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>9</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>9</number>
        </property>
        <property name="bottomMargin">
         <number>9</number>
        </property>
        <item>
         <widget class="QCheckBox" name="Torque_Auto_checkBox">
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QCheckBox{color:#ABABAB;}
QCheckBox::indicator {
    width: 13px;  /* 設定寬度 */
    height: 13px; /* 設定高度 */
}

QCheckBox::indicator:unchecked {
    image: url(:/btn_check/btn_check_s0_0.png);
}

QCheckBox::indicator:unchecked:hover {
    image: url(:/btn_check/btn_check_s0_1.png);
}

QCheckBox::indicator:checked {
    image: url(:/btn_check/btn_check_s1_0.png);
}

QCheckBox::indicator:checked:hover {
    image: url(:/btn_check/btn_check_s1_1.png);
}</string>
          </property>
          <property name="text">
           <string>自動</string>
          </property>
          <property name="iconSize">
           <size>
            <width>8</width>
            <height>8</height>
           </size>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_8">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QWidget" name="Torque_edit_group" native="true">
          <layout class="QGridLayout" name="grid_toolinfo_area1_6">
           <property name="leftMargin">
            <number>8</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>5</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="horizontalSpacing">
            <number>8</number>
           </property>
           <property name="verticalSpacing">
            <number>5</number>
           </property>
           <item row="1" column="2">
            <widget class="QLabel" name="Torque_neg_alarm_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color:#FFAAAA;</string>
             </property>
             <property name="text">
              <string>(-)警示 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="1" column="3">
            <widget class="QLineEdit" name="Torque_neg_alarm_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="Torque_pos_alarm_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="Torque_pos_alarm_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color:#FFAAAA;</string>
             </property>
             <property name="text">
              <string>(+)警示 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QLabel" name="Torque_neg_peak_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #AAAAFF;</string>
             </property>
             <property name="text">
              <string>(-)峰值 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QLineEdit" name="Torque_neg_peak_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="Torque_pos_peak_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #AAAAFF;</string>
             </property>
             <property name="text">
              <string>(+)峰值 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="QLineEdit" name="Torque_pos_peak_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="0">
            <widget class="QLabel" name="Torque_pos_warning_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #FFFFAA;</string>
             </property>
             <property name="text">
              <string>(+)警告 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QLineEdit" name="Torque_pos_warning_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="2" column="2">
            <widget class="QLabel" name="Torque_neg_warning_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #FFFFAA;</string>
             </property>
             <property name="text">
              <string>(-)警告 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="2" column="3">
            <widget class="QLineEdit" name="Torque_neg_warning_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QGroupBox" name="Temperature">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>11</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(255, 255, 255);</string>
       </property>
       <property name="title">
        <string>溫度[°C]</string>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout_8" stretch="1,0,5">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>9</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>9</number>
        </property>
        <property name="bottomMargin">
         <number>9</number>
        </property>
        <item>
         <widget class="QCheckBox" name="Temp_Auto_checkBox">
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QCheckBox{color:#ABABAB;}
QCheckBox::indicator {
    width: 13px;  /* 設定寬度 */
    height: 13px; /* 設定高度 */
}

QCheckBox::indicator:unchecked {
    image: url(:/btn_check/btn_check_s0_0.png);
}

QCheckBox::indicator:unchecked:hover {
    image: url(:/btn_check/btn_check_s0_1.png);
}

QCheckBox::indicator:checked {
    image: url(:/btn_check/btn_check_s1_0.png);
}

QCheckBox::indicator:checked:hover {
    image: url(:/btn_check/btn_check_s1_1.png);
}</string>
          </property>
          <property name="text">
           <string>自動</string>
          </property>
          <property name="iconSize">
           <size>
            <width>8</width>
            <height>8</height>
           </size>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_9">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QWidget" name="Temp_edit_group" native="true">
          <layout class="QGridLayout" name="grid_toolinfo_area1_7">
           <property name="leftMargin">
            <number>8</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>5</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <property name="horizontalSpacing">
            <number>8</number>
           </property>
           <property name="verticalSpacing">
            <number>5</number>
           </property>
           <item row="0" column="1">
            <widget class="QLineEdit" name="Temp_max_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="2">
            <widget class="QLabel" name="Temp_min_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #AAFFAA;</string>
             </property>
             <property name="text">
              <string>最小值 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="1" column="2">
            <widget class="QLabel" name="Temp_alarm_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color:#FFAAAA;</string>
             </property>
             <property name="text">
              <string>警示 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="QLineEdit" name="Temp_warning_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="1" column="3">
            <widget class="QLineEdit" name="Temp_alarm_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="3">
            <widget class="QLineEdit" name="Temp_min_lineedit">
             <property name="styleSheet">
              <string notr="true">color: rgb(0, 0, 0);</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item row="0" column="0">
            <widget class="QLabel" name="Temp_max_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #AAAAFF;</string>
             </property>
             <property name="text">
              <string>最大值 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLabel" name="Temp_warning_label">
             <property name="font">
              <font>
               <family>Arial Black</family>
               <pointsize>10</pointsize>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::LeftToRight</enum>
             </property>
             <property name="styleSheet">
              <string notr="true">color: #FFFFAA;</string>
             </property>
             <property name="text">
              <string>警告 :</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="margin">
              <number>2</number>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="QWidget" name="view1_save_close" native="true">
       <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="0,8,3,1,3">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QCheckBox" name="view_OpenAW_checkBox">
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QCheckBox{color:#ABABAB;}
QCheckBox::indicator {
    width: 13px;  /* 設定寬度 */
    height: 13px; /* 設定高度 */
}

QCheckBox::indicator:unchecked {
    image: url(:/btn_check/btn_check_s0_0.png);
}

QCheckBox::indicator:unchecked:hover {
    image: url(:/btn_check/btn_check_s0_1.png);
}

QCheckBox::indicator:checked {
    image: url(:/btn_check/btn_check_s1_0.png);
}

QCheckBox::indicator:checked:hover {
    image: url(:/btn_check/btn_check_s1_1.png);
}</string>
          </property>
          <property name="text">
           <string>顯示警示 / 警告</string>
          </property>
          <property name="iconSize">
           <size>
            <width>8</width>
            <height>8</height>
           </size>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_11">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="view1_read_btn">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>11</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color:#5448B6;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color:#7AFEC6;
     color: rgb(255, 255, 255);
    border:none
}

QPushButton:pressed {
    background-color: gray;
	border:none
}</string>
          </property>
          <property name="text">
           <string>讀取值</string>
          </property>
          <property name="iconSize">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_6">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="view1_save_btn">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>11</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color: #0C0C44;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color: #0C0C44;
	border: 2px solid #7AFEC6;
    color: rgb(255, 255, 255);
	border-radius:5px;
	padding:2px;
}

QPushButton:pressed {
    background-color: gray;
	border: none;
}</string>
          </property>
          <property name="text">
           <string>儲存值</string>
          </property>
          <property name="iconSize">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QWidget" name="view_title_2" native="true">
     <property name="styleSheet">
      <string notr="true">QWidget#filter_title_2{
background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));
border-radius:5px;
}
</string>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>3</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel" name="view_title_label_2">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>其它顯示設定</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
        <property name="margin">
         <number>2</number>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="view2_layout">
     <property name="leftMargin">
      <number>9</number>
     </property>
     <property name="rightMargin">
      <number>9</number>
     </property>
     <item>
      <layout class="QHBoxLayout" name="ColorSpectrum">
       <item>
        <widget class="QRadioButton" name="ColorSpectrum_radio">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="../../system_image/system_image.qrc">
           <normaloff>:/color_spectrum/color_spectrum_1.png</normaloff>:/color_spectrum/color_spectrum_1.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>30</width>
           <height>30</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="ColorSpectrum_radio_2">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="../../system_image/system_image.qrc">
           <normaloff>:/color_spectrum/color_spectrum_2.png</normaloff>:/color_spectrum/color_spectrum_2.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>30</width>
           <height>30</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="ColorSpectrum_radio_3">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="../../system_image/system_image.qrc">
           <normaloff>:/color_spectrum/color_spectrum_3.png</normaloff>:/color_spectrum/color_spectrum_3.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>30</width>
           <height>30</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QRadioButton" name="ColorSpectrum_radio_4">
         <property name="font">
          <font>
           <family>Arial Black</family>
           <pointsize>10</pointsize>
          </font>
         </property>
         <property name="styleSheet">
          <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
         </property>
         <property name="text">
          <string/>
         </property>
         <property name="icon">
          <iconset resource="../../system_image/system_image.qrc">
           <normaloff>:/color_spectrum/color_splitedge.png</normaloff>:/color_spectrum/color_splitedge.png</iconset>
         </property>
         <property name="iconSize">
          <size>
           <width>30</width>
           <height>30</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QCheckBox" name="view_HideRing_checkBox">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>10</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QCheckBox{color:#ABABAB;}
QCheckBox::indicator {
    width: 13px;  /* 設定寬度 */
    height: 13px; /* 設定高度 */
}

QCheckBox::indicator:unchecked {
    image: url(:/btn_check/btn_check_s0_0.png);
}

QCheckBox::indicator:unchecked:hover {
    image: url(:/btn_check/btn_check_s0_1.png);
}

QCheckBox::indicator:checked {
    image: url(:/btn_check/btn_check_s1_0.png);
}

QCheckBox::indicator:checked:hover {
    image: url(:/btn_check/btn_check_s1_1.png);
}</string>
       </property>
       <property name="text">
        <string>隱藏 Fz / Torque</string>
       </property>
       <property name="iconSize">
        <size>
         <width>8</width>
         <height>8</height>
        </size>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QWidget" name="view2_save_close" native="true">
       <layout class="QHBoxLayout" name="horizontalLayout_9" stretch="0,8,3,1,3">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QCheckBox" name="view2_HideScale_checkBox">
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QCheckBox{color:#ABABAB;}
QCheckBox::indicator {
    width: 13px;  /* 設定寬度 */
    height: 13px; /* 設定高度 */
}

QCheckBox::indicator:unchecked {
    image: url(:/btn_check/btn_check_s0_0.png);
}

QCheckBox::indicator:unchecked:hover {
    image: url(:/btn_check/btn_check_s0_1.png);
}

QCheckBox::indicator:checked {
    image: url(:/btn_check/btn_check_s1_0.png);
}

QCheckBox::indicator:checked:hover {
    image: url(:/btn_check/btn_check_s1_1.png);
}</string>
          </property>
          <property name="text">
           <string>隱藏3D輔助線</string>
          </property>
          <property name="iconSize">
           <size>
            <width>8</width>
            <height>8</height>
           </size>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_10">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="view2_save_btn">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>11</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color:#5448B6;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color:#7AFEC6;
     color: rgb(255, 255, 255);
    border:none
}

QPushButton:pressed {
    background-color: gray;
	border:none
}</string>
          </property>
          <property name="text">
           <string>儲存</string>
          </property>
          <property name="iconSize">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="view2_close_btn">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>10</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>11</pointsize>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color: #0C0C44;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color: #0C0C44;
	border: 2px solid #7AFEC6;
    color: rgb(255, 255, 255);
	border-radius:5px;
	padding:2px;
}

QPushButton:pressed {
    background-color: gray;
	border: none;
}</string>
          </property>
          <property name="text">
           <string>取消</string>
          </property>
          <property name="iconSize">
           <size>
            <width>20</width>
            <height>20</height>
           </size>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../system_image/system_image.qrc"/>
 </resources>
 <connections/>
</ui>
