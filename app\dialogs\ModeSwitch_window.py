# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'ModeSwitch_Window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

import system_image.system_image_rc

class Ui_ModeSwitch_Window(object):
    def setupUi(self, ModeSwitch_Window):
        if not ModeSwitch_Window.objectName():
            ModeSwitch_Window.setObjectName(u"ModeSwitch_Window")
        ModeSwitch_Window.resize(210, 360)
        ModeSwitch_Window.setMinimumSize(QSize(210, 360))
        ModeSwitch_Window.setMaximumSize(QSize(210, 360))
        ModeSwitch_Window.setStyleSheet(u"QWidget#ModeSwitch_Window{\n"
"background-color: #0C0C44;\n"
"border: 2px solid #5448B6;\n"
"}")
        self.verticalLayout = QVBoxLayout(ModeSwitch_Window)
        self.verticalLayout.setSpacing(25)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(10, 15, 10, 20)
        self.modeswitch_title = QWidget(ModeSwitch_Window)
        self.modeswitch_title.setObjectName(u"modeswitch_title")
        self.mode_title = QHBoxLayout(self.modeswitch_title)
        self.mode_title.setSpacing(0)
        self.mode_title.setObjectName(u"mode_title")
        self.mode_title.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_7 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.mode_title.addItem(self.horizontalSpacer_7)

        self.mode_title_label = QLabel(self.modeswitch_title)
        self.mode_title_label.setObjectName(u"mode_title_label")
        font = QFont()
        font.setFamily(u"Arial Black")
        font.setPointSize(20)
        font.setBold(True)
        font.setWeight(75)
        self.mode_title_label.setFont(font)
        self.mode_title_label.setStyleSheet(u"color:#5448B6;")

        self.mode_title.addWidget(self.mode_title_label)

        self.horizontalSpacer_8 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.mode_title.addItem(self.horizontalSpacer_8)


        self.verticalLayout.addWidget(self.modeswitch_title)

        self.realtime_widget = QWidget(ModeSwitch_Window)
        self.realtime_widget.setObjectName(u"realtime_widget")
        self.horizontalLayout = QHBoxLayout(self.realtime_widget)
        self.horizontalLayout.setSpacing(5)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.realtime_btnicon = QPushButton(self.realtime_widget)
        self.realtime_btnicon.setObjectName(u"realtime_btnicon")
        self.realtime_btnicon.setStyleSheet(u"border:none;\n"
"")

        self.horizontalLayout.addWidget(self.realtime_btnicon)

        self.realtime_btn = QPushButton(self.realtime_widget)
        self.realtime_btn.setObjectName(u"realtime_btn")
        font1 = QFont()
        font1.setFamily(u"Arial Black")
        font1.setPointSize(12)
        self.realtime_btn.setFont(font1)
        self.realtime_btn.setStyleSheet(u"QPushButton {\n"
"    image: none;\n"
"border:none;\n"
"color: rgb(255, 255, 255);\n"
"}\n"
"\n"
"")

        self.horizontalLayout.addWidget(self.realtime_btn)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer)


        self.verticalLayout.addWidget(self.realtime_widget)

        self.readfile_widget = QWidget(ModeSwitch_Window)
        self.readfile_widget.setObjectName(u"readfile_widget")
        self.horizontalLayout_2 = QHBoxLayout(self.readfile_widget)
        self.horizontalLayout_2.setSpacing(5)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.readfile_btnicon = QPushButton(self.readfile_widget)
        self.readfile_btnicon.setObjectName(u"readfile_btnicon")
        self.readfile_btnicon.setStyleSheet(u"border:none;")

        self.horizontalLayout_2.addWidget(self.readfile_btnicon)

        self.readfile_btn = QPushButton(self.readfile_widget)
        self.readfile_btn.setObjectName(u"readfile_btn")
        self.readfile_btn.setFont(font1)
        self.readfile_btn.setStyleSheet(u"border:none;\n"
"color: rgb(255, 255, 255);")

        self.horizontalLayout_2.addWidget(self.readfile_btn)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer_2)


        self.verticalLayout.addWidget(self.readfile_widget)

        self.realtime_pro_widget = QWidget(ModeSwitch_Window)
        self.realtime_pro_widget.setObjectName(u"realtime_pro_widget")
        self.horizontalLayout_3 = QHBoxLayout(self.realtime_pro_widget)
        self.horizontalLayout_3.setSpacing(5)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.realtimepro_btnicon = QPushButton(self.realtime_pro_widget)
        self.realtimepro_btnicon.setObjectName(u"realtimepro_btnicon")
        self.realtimepro_btnicon.setStyleSheet(u"QPushButton {\n"
"    border:none;\n"
"	image: url(:/other/mode_dot_0.png);\n"
"}\n"
"\n"
"")

        self.horizontalLayout_3.addWidget(self.realtimepro_btnicon)

        self.realtimepro_btn = QPushButton(self.realtime_pro_widget)
        self.realtimepro_btn.setObjectName(u"realtimepro_btn")
        self.realtimepro_btn.setFont(font1)
        self.realtimepro_btn.setStyleSheet(u"border:none;\n"
"color: rgb(255, 255, 255);")

        self.horizontalLayout_3.addWidget(self.realtimepro_btn)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_3)


        self.verticalLayout.addWidget(self.realtime_pro_widget)

        self.readfile_pro_widget = QWidget(ModeSwitch_Window)
        self.readfile_pro_widget.setObjectName(u"readfile_pro_widget")
        self.horizontalLayout_4 = QHBoxLayout(self.readfile_pro_widget)
        self.horizontalLayout_4.setSpacing(5)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout_4.setContentsMargins(0, 0, 0, 0)
        self.readfilepro_btnicon = QPushButton(self.readfile_pro_widget)
        self.readfilepro_btnicon.setObjectName(u"readfilepro_btnicon")
        self.readfilepro_btnicon.setStyleSheet(u"border:none;")

        self.horizontalLayout_4.addWidget(self.readfilepro_btnicon)

        self.readfilepro_btn = QPushButton(self.readfile_pro_widget)
        self.readfilepro_btn.setObjectName(u"readfilepro_btn")
        self.readfilepro_btn.setFont(font1)
        self.readfilepro_btn.setStyleSheet(u"border:none;\n"
"color: rgb(255, 255, 255);")

        self.horizontalLayout_4.addWidget(self.readfilepro_btn)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_4.addItem(self.horizontalSpacer_4)


        self.verticalLayout.addWidget(self.readfile_pro_widget)

        self.homepage_widget = QWidget(ModeSwitch_Window)
        self.homepage_widget.setObjectName(u"homepage_widget")
        self.homepage_widget.setStyleSheet(u"border:none;")
        self.horizontalLayout_5 = QHBoxLayout(self.homepage_widget)
        self.horizontalLayout_5.setSpacing(5)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.horizontalLayout_5.setContentsMargins(0, 0, 0, 0)
        self.homepage_btnicon = QPushButton(self.homepage_widget)
        self.homepage_btnicon.setObjectName(u"homepage_btnicon")
        self.homepage_btnicon.setStyleSheet(u"border:none;")

        self.horizontalLayout_5.addWidget(self.homepage_btnicon)

        self.homepage_btn = QPushButton(self.homepage_widget)
        self.homepage_btn.setObjectName(u"homepage_btn")
        self.homepage_btn.setFont(font1)
        self.homepage_btn.setStyleSheet(u"border:none;\n"
"color: rgb(255, 255, 255);")

        self.horizontalLayout_5.addWidget(self.homepage_btn)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer_5)


        self.verticalLayout.addWidget(self.homepage_widget)

        self.analyzer_widget = QWidget(ModeSwitch_Window)
        self.analyzer_widget.setObjectName(u"analyzer_widget")
        self.horizontalLayout_6 = QHBoxLayout(self.analyzer_widget)
        self.horizontalLayout_6.setSpacing(5)
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.horizontalLayout_6.setContentsMargins(0, 0, 0, 0)
        self.analyzer_btnicon = QPushButton(self.analyzer_widget)
        self.analyzer_btnicon.setObjectName(u"analyzer_btnicon")
        self.analyzer_btnicon.setStyleSheet(u"border:none;")

        self.horizontalLayout_6.addWidget(self.analyzer_btnicon)

        self.analyzer_btn = QPushButton(self.analyzer_widget)
        self.analyzer_btn.setObjectName(u"analyzer_btn")
        self.analyzer_btn.setFont(font1)
        self.analyzer_btn.setStyleSheet(u"border:none;\n"
"color: rgb(255, 255, 255);")
        self.analyzer_btn.setLocale(QLocale(QLocale.Chinese, QLocale.China))

        self.horizontalLayout_6.addWidget(self.analyzer_btn)

        self.horizontalSpacer_6 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_6.addItem(self.horizontalSpacer_6)


        self.verticalLayout.addWidget(self.analyzer_widget)


        self.retranslateUi(ModeSwitch_Window)

        QMetaObject.connectSlotsByName(ModeSwitch_Window)
    # setupUi

    def retranslateUi(self, ModeSwitch_Window):
        ModeSwitch_Window.setWindowTitle(QCoreApplication.translate("ModeSwitch_Window", u"Form", None))
        self.mode_title_label.setText(QCoreApplication.translate("ModeSwitch_Window", u"\u6a21\u5f0f\u5207\u63db", None))
        self.realtime_btnicon.setText("")
        self.realtime_btn.setText(QCoreApplication.translate("ModeSwitch_Window", u"Real-time", None))
        self.readfile_btnicon.setText("")
        self.readfile_btn.setText(QCoreApplication.translate("ModeSwitch_Window", u"Read-file", None))
        self.realtimepro_btnicon.setText("")
        self.realtimepro_btn.setText(QCoreApplication.translate("ModeSwitch_Window", u"Real-time Pro", None))
        self.readfilepro_btnicon.setText("")
        self.readfilepro_btn.setText(QCoreApplication.translate("ModeSwitch_Window", u"Read-file Pro", None))
        self.homepage_btnicon.setText("")
        self.homepage_btn.setText(QCoreApplication.translate("ModeSwitch_Window", u"\u9996\u9801", None))
        self.analyzer_btnicon.setText("")
        self.analyzer_btn.setText(QCoreApplication.translate("ModeSwitch_Window", u"Analyzer", None))
    # retranslateUi

