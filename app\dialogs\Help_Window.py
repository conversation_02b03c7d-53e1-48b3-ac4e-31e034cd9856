# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'Help_Window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

import system_image.system_image_rc

class Ui_Help_Window(object):
    def setupUi(self, Help_Window):
        if not Help_Window.objectName():
            Help_Window.setObjectName(u"Help_Window")
        Help_Window.resize(700, 1000)
        font = QFont()
        font.setFamily(u"Arial")
        font.setBold(True)
        font.setWeight(75)
        Help_Window.setFont(font)
        Help_Window.setStyleSheet(u"background-color: #0C0C44;\n"
"border:none")
        self.verticalLayout = QVBoxLayout(Help_Window)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.main_layout = QVBoxLayout()
        self.main_layout.setObjectName(u"main_layout")
        self.main_layout.setSizeConstraint(QLayout.SetNoConstraint)
        self.title_widget = QWidget(Help_Window)
        self.title_widget.setObjectName(u"title_widget")
        self.horizontalLayout = QHBoxLayout(self.title_widget)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.title_layout = QHBoxLayout()
        self.title_layout.setObjectName(u"title_layout")
        self.title_layout.setContentsMargins(-1, -1, 5, -1)
        self.title_label = QLabel(self.title_widget)
        self.title_label.setObjectName(u"title_label")
        font1 = QFont()
        font1.setFamily(u"Arial Black")
        font1.setPointSize(18)
        font1.setBold(True)
        font1.setWeight(75)
        self.title_label.setFont(font1)
        self.title_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.title_label.setTextFormat(Qt.AutoText)

        self.title_layout.addWidget(self.title_label)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.title_layout.addItem(self.horizontalSpacer_5)

        self.close_button = QPushButton(self.title_widget)
        self.close_button.setObjectName(u"close_button")
        font2 = QFont()
        font2.setFamily(u"PMingLiU")
        font2.setPointSize(20)
        font2.setBold(False)
        font2.setWeight(50)
        self.close_button.setFont(font2)
        self.close_button.setAutoFillBackground(False)
        self.close_button.setStyleSheet(u"QPushButton {\n"
"    background: url(:/btn_close/btn_close_0.png) no-repeat right center;\n"
"    border: none;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background: url(:/btn_close/btn_close_1.png) no-repeat right center;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background: url(:/btn_close/btn_close_2.png) no-repeat right center;\n"
"}")
        self.close_button.setIconSize(QSize(30, 30))
        self.close_button.setCheckable(True)
        self.close_button.setAutoDefault(False)
        self.close_button.setFlat(False)

        self.title_layout.addWidget(self.close_button)

        self.title_layout.setStretch(1, 100)
        self.title_layout.setStretch(2, 1)

        self.horizontalLayout.addLayout(self.title_layout)


        self.main_layout.addWidget(self.title_widget)

        self.pdf_widget = QWidget(Help_Window)
        self.pdf_widget.setObjectName(u"pdf_widget")
        self.verticalLayout_3 = QVBoxLayout(self.pdf_widget)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.pdf_layout = QVBoxLayout()
        self.pdf_layout.setObjectName(u"pdf_layout")

        self.verticalLayout_3.addLayout(self.pdf_layout)


        self.main_layout.addWidget(self.pdf_widget)

        self.main_layout.setStretch(0, 5)
        self.main_layout.setStretch(1, 95)

        self.verticalLayout.addLayout(self.main_layout)


        self.retranslateUi(Help_Window)

        self.close_button.setDefault(False)


        QMetaObject.connectSlotsByName(Help_Window)
    # setupUi

    def retranslateUi(self, Help_Window):
        Help_Window.setWindowTitle(QCoreApplication.translate("Help_Window", u"Form", None))
        self.title_label.setText(QCoreApplication.translate("Help_Window", u"\u64cd\u4f5c\u8aaa\u660e", None))
        self.close_button.setText("")
#if QT_CONFIG(shortcut)
        self.close_button.setShortcut("")
#endif // QT_CONFIG(shortcut)
    # retranslateUi

