from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

class Ui_Loading_Window(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUi()
        
    def setupUi(self):
        self.setObjectName("loading_window")
        self.resize(400, 200)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 主佈局
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 背景框架
        self.background_frame = QFrame()
        self.background_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(0, 0, 0, 180);
                border-radius: 10px;
                border: 2px solid #555;
            }
        """)
        
        # 內容佈局
        self.content_layout = QVBoxLayout(self.background_frame)
        self.content_layout.setSpacing(20)
        self.content_layout.setContentsMargins(30, 30, 30, 30)
        
        # Logo 或標題
        self.title_label = QLabel("MachRadarPro")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        
        # 進度條
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #555;
                border-radius: 5px;
                text-align: center;
                background-color: #333;
                color: white;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 3px;
            }
        """)
        
        # 狀態標籤
        self.status_label = QLabel("正在初始化...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                color: #ccc;
                font-size: 12px;
            }
        """)
        
        # 添加到佈局
        self.content_layout.addWidget(self.title_label)
        self.content_layout.addWidget(self.progress_bar)
        self.content_layout.addWidget(self.status_label)
        
        self.main_layout.addWidget(self.background_frame)
        
    def update_progress(self, value, status_text=""):
        """更新進度條和狀態文字"""
        self.progress_bar.setValue(value)
        if status_text:
            self.status_label.setText(status_text)
            
    def center_on_screen(self):
        """將視窗置中顯示"""
        screen = QApplication.primaryScreen().geometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )