# -*- coding: utf-8 -*-
"""
Recording Manager
處理錄製功能
"""

from enum import auto
import time
from collections import deque
from PySide2.QtCore import QObject, Signal, QTimer
from utils.record_file_manager import RecordFileManager
from app.models.auto_record_manager import AutoRecordManager
from . import logger

class RecordManager(QObject):
    """管理錄製功能"""
    # signals for controller to invoke
    sig_stop_all_record = Signal()
    sig_start_manual_record = Signal()
    sig_stop_manual_record = Signal()
    sig_start_script_record = Signal()
    sig_stop_script_record = Signal()
    sig_disable_record = Signal(str)
    sig_enable_record = Signal(str)

    # signals to update View to reflect recording status
    sig_record_started = Signal()
    sig_record_stopped = Signal()

    # Error handling signals 
    sig_error_occurred = Signal(str, str)  # Emits error_type, error_message

    def __init__(self):
        super().__init__()
        # 刀具資訊
        self.tool_name = ""
        self.tool_info_text = ""
        self.tool_info_configured = False

        # 錄製資料
        self.record_data_lines = []
        self.raw_data_lines = []

        # 錄製目錄和檔案
        self.record_directory = ""
        self.manual_record_filename = ""
        self.auto_record_filename = ""
        self.script_record_filename = ""
        
        # 檔案管理
        self.record_file_manager_list = {}
        self.raw_data_file_manager_list = {}

        # misc 變數
        self.save_raw_data = False # 原始資料
        self.record_modes = ["manual", "auto", "script"]
        self.record_decimal_places = 6 # 錄製小數位數
        self.write_buffer_size = 2010 # 根據 sample_rate, sample_point etc 計算

        # 手動錄製
        self.manual_is_enabled = True
        self.manual_is_recording = False
        
        # 自動錄製
        self.auto_record_manager = AutoRecordManager()
        self.auto_record_manager.write_buffer_size = self.write_buffer_size
        self.auto_is_enabled = False
        self.auto_is_recording = False
        self.sample_rate = 10000
        self.auto_record_setting_fields = [
            'auto_record_enabled', 
            'auto_record_seconds', 
            'auto_pre_record_seconds', 
            'auto_max_record_count', 
            'auto_cf_enabled', 
            'auto_fz_enabled', 
            'auto_t_enabled', 
            'auto_cf_threshold', 
            'auto_fz_threshold', 
            'auto_t_threshold',
            'sample_rate'
        ]
        
        # 錄製腳本
        self.script_is_enabled = False
        self.script_is_recording = False
        self.max_record_time = 0 # for standard mode; if set to 0, = no max time
        self.record_timer = QTimer(self, singleShot=True)
        self.tmp_manual_is_enabled = False
        self.tmp_auto_is_enabled = False

        # connect signals
        self.record_timer.timeout.connect(self.on_max_timeout)
        self.sig_stop_all_record.connect(self.stop_all_record)
        self.sig_start_manual_record.connect(self.start_manual_record)
        self.sig_stop_manual_record.connect(self.stop_manual_record)
        self.sig_start_script_record.connect(self.start_script_record)
        self.sig_stop_script_record.connect(self.stop_script_record)
        self.sig_disable_record.connect(self.disable_record)
        self.sig_enable_record.connect(self.enable_record)
        self.auto_record_manager.sig_auto_record_triggered.connect(self.start_auto_record)
        self.auto_record_manager.sig_auto_record_stop.connect(self.stop_auto_record)

    # 設定
    def configure_record_path(self, type, filename):
        """configure directory and files for storing recordings"""
        # check type
        if type not in ["directory", "manual", "auto", "script"]:
            logger.error(f"Invalid record type: {type}")
            return
        
        # set file/directory
        if type == "directory":
            self.record_directory = filename
        elif type == "manual":
            self.manual_record_filename = filename
        elif type == "auto":
            self.auto_record_filename = filename
        elif type == "script":
            self.record_script_filename = filename

        logger.info(f"Record path configured: type={type}, filename={filename}")

    def configure_record_flag(self, flag, value: bool):
        """configure flag"""
        # check for flag existence
        if flag not in ["save_raw_data", "manual_is_enabled", "auto_is_enabled", "script_is_enabled"]:
            logger.error(f"Invalid flag: {flag}")
            return
        
        # set flag
        setattr(self, flag, value)

    def configure_record_variable(self, key, value):
        """configure variable"""
        # check key
        if key not in ["record_decimal_places"]:
            logger.error(f"Invalid key: {key}")
            return
        
        # set variable
        setattr(self, key, value)

    def configure_tool_info(self, tool_data, co2_data):
        """configure tool info"""
        # save name
        self.tool_name = tool_data.get('toolname', '')

        # generate tool info text
        self.tool_info_text = f"!,{tool_data['sample_rate']},{round(tool_data['tare_xv'],self.record_decimal_places)},{round(tool_data['tare_yv'],self.record_decimal_places)},{round(tool_data['tare_zv'],self.record_decimal_places)},{round(tool_data['tare_tv'],self.record_decimal_places)},{tool_data['tare_gx']},{tool_data['tare_gy']},{tool_data['tare_gz']},{(co2_data or {}).get('MS_CO2_K', 0)},{(co2_data or {}).get('MS_CO2_zc', 0)},{(co2_data or {}).get('MS_CO2_Dc', 0)},{(co2_data or {}).get('MS_CO2_vc', 0)},{(co2_data or {}).get('MS_CO2_fz', 0)},{(co2_data or {}).get('MS_CO2_ap', 0)},{(co2_data or {}).get('MS_CO2_ae', 0)},{(co2_data or {}).get('MS_CO2_n', 0)},{(co2_data or {}).get('MS_CO2_vf', 0)},{(co2_data or {}).get('MS_CO2_Q', 0)},{(co2_data or {}).get('MS_CO2_Pc', 0)},{(co2_data or {}).get('MS_CO2_Pb', 0)},{(co2_data or {}).get('MS_CO2_EF', -1)},{tool_data['Lc']},{tool_data['Hl']},{tool_data['Kl']},{tool_data['Bx_COMP']},{tool_data['By_COMP']},{tool_data['Bz_COMP']},{tool_data['Bt_COMP']}\n"

        # configure auto record
        self.auto_is_enabled = tool_data.get('auto_record_enabled', False)
        auto_config = {
            'auto_record_enabled': tool_data.get('auto_record_enabled', False),
            'auto_record_seconds': tool_data.get('auto_record_seconds', 10),
            'auto_pre_record_seconds': tool_data.get('auto_pre_record_seconds', 0),
            'auto_max_record_count': tool_data.get('auto_max_record_count', 100),
            'auto_cf_enabled': tool_data.get('auto_cf_enabled', False),
            'auto_fz_enabled': tool_data.get('auto_fz_enabled', False),
            'auto_t_enabled': tool_data.get('auto_t_enabled', False),
            'auto_cf_threshold': tool_data.get('auto_cf_threshold', 0),
            'auto_fz_threshold': tool_data.get('auto_fz_threshold', 0),
            'auto_t_threshold': tool_data.get('auto_t_threshold', 0),
            'sample_rate': self.sample_rate
        }
        self.configure_auto_recording(auto_config)

        self.tool_info_configured = True
        logger.info(f"Tool info configured: tool_name={self.tool_name}")

    def configure_auto_recording(self, auto_config):
        """Configure auto recording settings"""
        # check if auto_config is valid
        if not isinstance(auto_config, dict):
            logger.error("Invalid auto recording configuration")
            return
        
        # check if all required fields are present
        for field in self.auto_record_setting_fields:
            if field not in auto_config:   
                logger.error(f"Missing required field: {field}")
                return

        # configure auto recording manager
        self.auto_record_manager.configure(auto_config)
        
        # configure auto record flag
        self.auto_is_enabled = self.auto_record_manager.auto_record_enabled
        
    def configure_script_recording(self, script_config):
        """Configure script recording settings"""
        self.script_is_enabled = script_config.get('enabled', False)
        self.script_directory = script_config.get('directory', '')
        self.script_tool_info = script_config.get('tool_info', '')
        
        # Calculate target data points based on duration and sample rate
        duration = script_config.get('duration', 60.0)
        self.script_target_data_points = int(duration * self.sample_rate)
        
        logger.info(f"Script recording configured: enabled={self.script_is_enabled}, "
                   f"directory={self.script_directory}, duration={duration}s, "
                   f"target_points={self.script_target_data_points}")

    def enable_record(self, type: str):
        self.configure_record_flag(f"{type}_is_enabled", True)

    def disable_record(self, type: str):
        self.stop_all_record()
        self.configure_record_flag(f"{type}_is_enabled", False)
        
    # getters
    def get_record_path(self, type):
        """Get record path"""
        if type == "directory":
            return self.record_directory
        elif type == "manual":
            return self.manual_record_filename
        elif type == "auto":
            return self.auto_record_filename
        elif type == "script":
            return self.record_script_filename
        else:
            logger.error(f"Invalid record type: {type}")
            return None
        
    def get_record_flag(self, flag):
        """Get record flag"""
        if flag == "save_raw_data":
            return self.save_raw_data
        elif flag == "auto_record_enabled":
            return self.auto_is_enabled
        elif flag == "script_record_enabled":
            return self.script_is_enabled
        else:
            logger.error(f"Invalid flag: {flag}")
            return None
        
    # important functions
    def run(self):
        """run record manager"""
        logger.debug("Record manager started")

    # called every time sensor data is received in decoder_worker (each of the 10k data points per second)
    def process_data(self, sensor_data_batch, is_raw_data=False):
        """Process sensor data for all recording types"""
        # check if any recording is enabled
        if not self.manual_is_enabled and not self.auto_is_enabled and not self.script_is_enabled: # and not self.machine_record_enabled
            return
        
        # check if raw data is enabled
        if not self.save_raw_data and is_raw_data:
            return
        
        # generate text from sensor data
        for idx, sensor_data in enumerate(sensor_data_batch):
            is_ADXL_data = (idx % 201 == 0)
            self.generate_record_text(sensor_data, is_raw_data, is_ADXL_data)
        
            # auto recording: check for triggers
            if self.auto_is_enabled and not self.auto_is_recording and not is_ADXL_data:
                self.auto_record_manager.check_triggers(sensor_data)

        # write data to file 
        if len(self.record_data_lines) >= self.write_buffer_size:
            # auto recording: save data for pre-record buffer if needed
            self.auto_record_manager.keep_pre_record_data(self.record_data_lines) # TODO: raw
            # write data
            self.write_record_data()
        
    def generate_record_text(self, sensor_data, is_raw_data=False, is_ADXL_data=False):
        """Generate record text from sensor data"""
        # there should be 5 types of data in both cases
        if len(sensor_data) != 5:
            logger.error(f"sensor_data length is not 5: {len(sensor_data)}")
            return

        # generate strings
        # TODO: use correct decimal places (currently hardcoded to 3)
        strings = [] 
        if is_ADXL_data: # 6 strings in one line
            # 加速規資料
            strings.append("*")
            strings.append(str('{:.3f}'.format(sensor_data[0]))) # gx
            strings.append(str('{:.3f}'.format(sensor_data[1]))) # gy
            strings.append(str('{:.3f}'.format(sensor_data[2]))) # gz
            strings.append(str('{:.2f}'.format(sensor_data[3]))) # temperature
            strings.append(str(sensor_data[4])) # wifi_rssi
        else: # 5 strings in one line
            # 受力資料
            strings.append(str('{:.3f}'.format(sensor_data[0]))) # bx
            strings.append(str('{:.3f}'.format(sensor_data[1]))) # by
            strings.append(str('{:.3f}'.format(sensor_data[2]))) # bz (tension)
            strings.append(str('{:.3f}'.format(sensor_data[3]))) # torque
            strings.append(str('{:.3f}'.format(sensor_data[4]))) # battery

        # join strings
        line = ','.join(strings) + '\n'

        # add to list of lines to write
        if not is_raw_data:
            self.record_data_lines.append(line)
        if self.save_raw_data and is_raw_data:
            self.raw_data_lines.append(line)

    def write_record_data(self):
        """write record data to file"""
        # make text
        if len(self.record_file_manager_list) > 0:
            record_text = ''.join(self.record_data_lines)
            # write to all files
            for record_file_manager in self.record_file_manager_list.values():
                record_file_manager.add_text(record_text)
        # clear buffer    
        self.record_data_lines = []

        # raw data
        if self.save_raw_data:
            # make text
            if len(self.raw_data_file_manager_list) > 0:
                raw_data_text = ''.join(self.raw_data_lines)
                # write to all files
                for raw_data_file_manager in self.raw_data_file_manager_list.values():
                    raw_data_file_manager.add_text(raw_data_text)
            # clear buffer
            self.raw_data_lines = []
    
    def _start_record(self, mode: str, filename: str):
        """start recording"""
        # write data, clear buffer
        self.write_record_data()

        # create file
        try:
            file_handles = self.create_file(self.record_directory, filename)
            if not file_handles or len(file_handles) == 0:
                logger.error(f"Failed to create recording file: {filename}")
                return False
        except:
            logger.error(f"Failed to create recording file: {filename}")
            return False

        # insert tool info text to first line
        try:
            for file_handle in file_handles:
                file_handle.add_text(self.tool_info_text)
        except:
            logger.error(f"Failed to insert tool info text to first line of recording file: {filename}")
            return False

        # emit signal to update View to reflect recording status
        self.sig_record_started.emit()
        logger.debug(f"Recording _started: {filename}")

        return True
    
    def _stop_record(self, filename: str):
        """stop recording"""
        # write remaining data to file
        self.write_record_data()

        # release file
        self.release_file(filename)

        # emit signal to update View to reflect recording status
        # self.sig_record_stopped.emit()

        logger.debug(f"Recording _stopped: {filename}")

    def stop_all_record(self):
        """stop all recording"""
        self.stop_manual_record()
        self.stop_auto_record()
        self.stop_script_record()
        
    # file manipulation
    def create_file(self, directory, filename):
        """create file"""
        try:
            # create file and add to file manager lists
            # record file
            record_file_manager = RecordFileManager(directory, filename)
            self.record_file_manager_list[filename] = record_file_manager
            
            # raw data file
            if self.save_raw_data:
                raw_data_file_manager = RecordFileManager(directory, f"raw_data_{filename}")
                self.raw_data_file_manager_list[f"raw_data_{filename}"] = raw_data_file_manager

            # return file handles
            file_handles = []
            file_handles.append(record_file_manager)
            if self.save_raw_data:
                file_handles.append(raw_data_file_manager)
            return file_handles

        except Exception as e:
            logger.error(f"Failed to create file: {filename} in directory: {directory}")
            logger.error(f"Error: {e}")
            return None
    
    def release_file(self, filename):
        """release file (recording ended) (stop holding reference to it)"""
        if filename in self.record_file_manager_list:
            # important: make sure to close the file
            self.record_file_manager_list[filename].close_file()
            del self.record_file_manager_list[filename]

            # release raw data file, as it is always created together with record file
            if f"raw_data_{filename}" in self.raw_data_file_manager_list:
                self.raw_data_file_manager_list[f"raw_data_{filename}"].close_file()
                del self.raw_data_file_manager_list[f"raw_data_{filename}"]

    # manual record
    def start_manual_record(self):
        """start manual recording"""
        # check if ready to start
        if not self.ready_to_start("manual"):
            return
        
        # generate filename
        filename = self.generate_filename(prefix="manual")
        self.manual_record_filename = filename
        
        # (for standard mode of record script)
        if self.max_record_time > 0:
            self.start_max_timer()

        # start recording, update state
        self.manual_is_recording = self._start_record("manual", filename)
    
    def stop_manual_record(self):
        """stop manual recording"""
        # check if ready to stop
        if not self.ready_to_stop("manual"):
            return
        
        # (for standard mode of record script)
        if self.max_record_time > 0:
            self.stop_max_timer()

        # stop recording, update state
        self._stop_record(self.manual_record_filename)
        self.manual_is_recording = False

        # clear manual record filename
        self.manual_record_filename = None

        # emit signal to update View to reflect recording status
        self.sig_record_stopped.emit()

    # auto record
    def start_auto_record(self, trigger_reason: str):
        """start auto recording"""
        # check if ready to start
        if not self.ready_to_start("auto"):
            return
        
        # generate filename
        filename = self.generate_filename(prefix=f"auto_{trigger_reason}")
        self.auto_record_filename = filename

        # set auto recording state
        self.auto_is_recording = self._start_record("auto", self.auto_record_filename)

        # write pre-recorded data to auto record file
        self.write_pre_record_data(self.auto_record_filename)

    def stop_auto_record(self):
        """stop auto recording"""
        # check if ready to stop
        if not self.ready_to_stop("auto"):
            return
        
        # stop recording, update state
        self._stop_record(self.auto_record_filename)
        self.auto_is_recording = False

        # clear auto record filename
        self.auto_record_filename = None

    def write_pre_record_data(self, filename):
        """write pre-recorded data to auto record file"""
        # chekc if pre record is enabled
        if self.auto_record_manager.auto_pre_record_seconds <= 0:
            return

        # check if pre record buffer is initialized
        if self.auto_record_manager.pre_record_buffer is None:
            return

        # check if there is data to write
        if len(self.auto_record_manager.pre_record_buffer) == 0:
            return
        
        # make record text
        record_text = ''.join(self.auto_record_manager.get_pre_record_data())
        
        # write to file
        self.record_file_manager_list[filename].add_text(record_text)
    
    # script record
    def set_max_record_time(self, max_record_time):
        """set max record time"""
        self.max_record_time = max_record_time

    def start_max_timer(self):
        """start timer for max record time for standard mode"""
        if self.max_record_time > 0:
            self.record_timer.setInterval(self.max_record_time * 1000 + 200)
            self.record_timer.start()
        else:
            logger.error("max record time is not set")

    def stop_max_timer(self):
        """stop timer for max record time"""
        if self.record_timer.isActive():
            self.record_timer.stop()
            # reset max record timer to 0 (current policy: max record time for standard mode is only one shot)
            self.max_record_time = 0

    def on_max_timeout(self):
        """on max record time timeout (manual)"""
        self.stop_manual_record()
        # reset max record timer to 0 (current policy: max record time for standard mode is only one shot)
        self.max_record_time = 0

    def start_script_record(self):
        """start script recording"""
        # check if ready to start
        if not self.ready_to_start("script"):
            return

        # stop other recording
        if self.manual_is_recording:
            self.stop_manual_record()
        if self.auto_is_recording:
            self.stop_auto_record()

        # keep flags in tmp variables
        self.tmp_manual_is_enabled = self.manual_is_enabled
        self.tmp_auto_is_enabled = self.auto_is_enabled

        # disable other recording
        self.manual_is_enabled = False
        self.auto_is_enabled = False
        
        # generate filename
        filename = self.generate_filename(prefix="script")
        self.script_record_filename = filename

        # start recording
        self.script_is_recording = self._start_record("script", self.script_record_filename)

    def stop_script_record(self):
        """stop script recording"""
        # check if ready to stop
        if not self.ready_to_stop("script"):
            return

        # stop recording, update state
        self._stop_record(self.script_record_filename)
        self.script_is_recording = False

        # clear script record filename
        self.script_record_filename = None

        # restore flags
        self.manual_is_enabled = self.tmp_manual_is_enabled
        self.auto_is_enabled = self.tmp_auto_is_enabled

    # utility functions 
    # TODO: support custom filename
    def generate_filename(self, prefix):
        """generate filename"""
        timestamp = time.strftime("%Y%m%d_%H%M%S", time.localtime())
        filename = self.make_filename_safe(f"{prefix}_{self.tool_name}_{timestamp}.msra")
        return filename

    def make_filename_safe(self, text):
        """Make text safe for filenames"""
        # Replace problematic characters
        replacements = {
            ' ': '_',
            '(': '',
            ')': '',
            '>=': 'gte',
            '<=': 'lte',
            '>': 'gt',
            '<': 'lt',
            '+': 'plus',
            '/': '_',
            '\\': '_',
            ':': '_',
            '*': '_',
            '?': '_',
            '"': '_',
            '|': '_'
        }
        
        result = text
        for old, new in replacements.items():
            result = result.replace(old, new)
        
        return result

    def ready_to_start(self, mode: str):
        """check if ready to start recording"""
        # check if mode is valid
        if mode not in self.record_modes:
            return False
        
        # check if tool info is configured
        if not self.tool_info_configured:
            return False

        # check if is enabled
        is_enabled = getattr(self, f"{mode}_is_enabled")
        if not is_enabled:
            return False

        # check if is recording
        is_recording = getattr(self, f"{mode}_is_recording")
        if is_recording:
            return False

        if mode != "script" and self.script_is_recording:
            return False

        return True

    def ready_to_stop(self, mode: str):
        """check if ready to stop recording"""
        # check if mode is valid
        if mode not in self.record_modes:
            return False
        
        # check if is enabled
        is_enabled = getattr(self, f"{mode}_is_enabled")
        if not is_enabled:
            return False

        # check if is recording
        is_recording = getattr(self, f"{mode}_is_recording")
        if not is_recording:
            return False
        
        return True