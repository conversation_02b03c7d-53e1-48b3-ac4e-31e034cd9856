<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Event_window</class>
 <widget class="QWidget" name="Event_window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#Event_window{
background-color: #0C0C44;
border: 2px solid #5448B6;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QLabel" name="label">
       <property name="font">
        <font>
         <family>Arial Black</family>
         <pointsize>20</pointsize>
         <weight>75</weight>
         <bold>true</bold>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">color: rgb(255, 255, 255);</string>
       </property>
       <property name="text">
        <string>事件記錄</string>
       </property>
      </widget>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="pushButton">
       <property name="font">
        <font>
         <pointsize>20</pointsize>
        </font>
       </property>
       <property name="styleSheet">
        <string notr="true">QPushButton {
image: url(:/btn_close/btn_close_0.png);
border:none;
}
QPushButton:hover{
image: url(:/btn_close/btn_close_1.png);
}
QPushButton:pressed {
image: url(:/btn_close/btn_close_2.png);
}</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="iconSize">
        <size>
         <width>30</width>
         <height>30</height>
        </size>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QTableWidget" name="tableWidget">
     <property name="styleSheet">
      <string notr="true">QWidget {
background-color: #0C0C44;
}
QTableWidget {
background-color:  #0C0C44;
color: white;
font-size: 18px;
font-weight: bold;
border: none;
gridline-color: transparent;
}
QHeaderView::section {
background-color:  #0C0C44;
color: white;
font-size: 18px;
font-weight: bold;
border: none;
padding: 5px;
gridline-color: transparent;
}
QTableWidget::item {
background-color: #0C0C44;
padding: 10px;
border: none;
gridline-color: transparent;
}
QTableWidget::item:selected {
background-color:  #0C0C44;
gridline-color: transparent;
}</string>
     </property>
     <row>
      <property name="text">
       <string>1</string>
      </property>
     </row>
     <row>
      <property name="text">
       <string>2</string>
      </property>
     </row>
     <row>
      <property name="text">
       <string>3</string>
      </property>
     </row>
     <row>
      <property name="text">
       <string>4</string>
      </property>
     </row>
     <row>
      <property name="text">
       <string>5</string>
      </property>
     </row>
     <column>
      <property name="text">
       <string>時間</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>資訊</string>
      </property>
     </column>
     <column>
      <property name="text">
       <string>刀把名稱</string>
      </property>
     </column>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../system_image/system_image.qrc"/>
 </resources>
 <connections/>
</ui>
