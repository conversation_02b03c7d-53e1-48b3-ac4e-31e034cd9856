# socket_client.py
import socket
import threading
import time
import queue
import math
from . import logger  # 從同一個包導入 logger

'''
2025/02/04 Josh
未來優化方向 
抽象化 class 
實際後再測試看看
'''

class SocketClient:
    def __init__(self, host='*************', port=1333):
    # def __init__(self, host='localhost', port=3000):
        # self.logger = logger
        self.host = host
        self.port = port
        self.client_socket = None
        self.running = False

        self.sample_index = 160800 // 16  # 資料每秒最多筆數
        self.count_second = 0  # 計算第幾秒(次)，每次都可以讓UI接受cancel的機會
        self.collect_data = ""
        self.temp_data = []
        self.is_sleep_on = False # 預設False

    def start_client(self):
        # print("start_client 連線中")
        logger.info(f"start_client {self.host} 連線中")
        try:
            self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.client_socket.connect((self.host, self.port))
            self.running = True
        except socket.error as e:
            # print(f"Error starting client: {e}")
            logger.error(f"Error starting client: {e}")
            self.stop_client()
    
    # 手動呼叫取得資料的方法
    def get_data(self):
        try:
            if self.running and self.client_socket:
                response = self.client_socket.recv(1024).decode()
                return response
            else:
                raise Exception("Client is not running.")
        except socket.error as e:
            # print(f"Socket error: {e}")
            logger.error(f"Socket error: {e}")
            self.stop_client()
            raise

    # 刀把資料收集-持續接收資料，直到接收完整的一段資料後回傳
    def receive_chunk(self, sample_N , sample_byte):
        # print("client 取資料中...")
        logger.debug("client 取資料中...")
        count_get = 0
        # sample_byte = int(sample_N * 16)
        collect_data = ""
        self.temp_data = []

        try:
            if not self.running or not self.client_socket:
                raise Exception("Client is not running or socket is invalid.")
            
            while count_get < sample_N:
                data_recv = self.client_socket.recv(sample_byte).hex()
                if not data_recv:
                    raise Exception("Connection closed by the server.")
                collect_data += data_recv

                if self.temp_data:
                    data_recv = self.temp_data + data_recv

                N = len(data_recv) // 32
                count_get += N

                if len(data_recv) % 32 > 0:
                    self.temp_data = data_recv[N*32:]
                else:
                    self.temp_data = []

                sample_byte = ( ( sample_N - count_get ) * 32 - len(self.temp_data) ) // 2

            # print("client 取資料完...")
            logger.debug("client 取資料完...")
            return collect_data

        except socket.error as e:
            # print(f"Socket error: {e}")
            logger.error(f"Socket error: {e}")
            self.stop_client()
            raise

    # Tare Tool Holder
    def getToolHolderTare(self, sample_N):
         # print("client 取資料中...")
        logger.debug("ToolHolderTare 取資料中...")
        countGet = 0
        sample_byte = sample_N
        collect_data = ""
        temp_data = []

        try:
            if not self.running or not self.client_socket:
                raise Exception("Client is not running or socket is invalid.")
            
            while countGet<1004:
                data_recv = self.client_socket.recv(sample_byte).hex()
                if not data_recv:
                    raise Exception("Connection closed by the server.")
                collect_data += data_recv

                if temp_data:
                    data_recv = temp_data + data_recv

                N = len(data_recv) // 32
                countGet += N

                if len(data_recv) % 32 > 0:
                    temp_data = data_recv[N*32:]
                else:
                    temp_data = []

                sample_byte = ( ( 1005-countGet)*32 - len(temp_data) ) //2 

            # print("client 取資料完...")
            logger.debug("client 取資料完...")
            return collect_data

        except socket.error as e:
            # print(f"Socket error: {e}")
            logger.error(f"Socket error: {e}")
            self.stop_client()
            raise

    def send_heartbeat(self):
        while self.running:
            try:
                self.client_socket.sendall(b"heartbeat")
                time.sleep(5)  # 每5秒發送一次心跳
            except socket.error as e:
                # print(f"Heartbeat error: {e}")
                logger.error(f"Heartbeat error: {e}")
                self.running = False

    def send_sleepmode(self,is_sleep_on):
        if not is_sleep_on: #睡眠未開啟
            self.client_socket.send("sleep_on\n".encode())
            logger.error("sleep_on")
        else: #睡眠開啟
            self.client_socket.send("sleep_off\n".encode())
            logger.error("sleep_off")

    def stop_client(self):
        self.running = False
        if self.client_socket:
            try:
                self.client_socket.close()
            except socket.error as e:
                # print(f"Error closing socket: {e}")
                logger.error(f"Error closing socket: {e}")
            finally:
                self.client_socket = None
        # print("Client connection closed.")
        logger.info("Client connection closed.")

    def getSocketStatu(self):
        # 取得連線狀態
        return self.running

