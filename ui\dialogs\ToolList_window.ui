<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ToolList_Window</class>
 <widget class="QWidget" name="ToolList_Window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>350</width>
    <height>270</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="MinimumExpanding" vsizetype="MinimumExpanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="maximumSize">
   <size>
    <width>350</width>
    <height>270</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QScrollArea" name="scrollArea">
     <property name="minimumSize">
      <size>
       <width>340</width>
       <height>20</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>Arial Black</family>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QScrollArea { 
    background-color: #0C0C44;
    border: 2px solid #5448B6;
}

/* 垂直滾動條 */
QScrollBar:vertical {
    background: #0C0C44;
    width: 8px;
    border: none;
    margin: 0px 0px 0px 0px;
}
QScrollBar::handle:vertical {
    background: #7AFEC6;
    min-height: 20px;
    border-radius: 3px;
}
QScrollBar::add-line:vertical, 
QScrollBar::sub-line:vertical {
    background: none;
    height: 0px;
}
QScrollBar::sub-page:vertical, 
QScrollBar::add-page:vertical {
    background: #0C0C44;
}

/* 水平滾動條 */
QScrollBar:horizontal {
    background: #0C0C44;
    height: 2px;
    border: none;
    margin: 0px 0px 0px 0px;
}
QScrollBar::handle:horizontal {
    background: #7AFEC6;
    min-width: 20px;
    border-radius: 3px;
}
QScrollBar::add-line:horizontal, 
QScrollBar::sub-line:horizontal {
    background: none;
    width: 0px;
}
QScrollBar::sub-page:horizontal, 
QScrollBar::add-page:horizontal {
    background: #0C0C44;
}
</string>
     </property>
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>346</width>
        <height>266</height>
       </rect>
      </property>
      <property name="sizePolicy">
       <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
        <horstretch>0</horstretch>
        <verstretch>0</verstretch>
       </sizepolicy>
      </property>
      <property name="styleSheet">
       <string notr="true">QWidget#scrollAreaWidgetContents {
background-color: #0C0C44;
border:none;
}</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <property name="leftMargin">
        <number>1</number>
       </property>
       <property name="topMargin">
        <number>1</number>
       </property>
       <property name="rightMargin">
        <number>1</number>
       </property>
       <property name="bottomMargin">
        <number>1</number>
       </property>
       <item>
        <widget class="QWidget" name="widget" native="true">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="font">
          <font>
           <family>Arial Black</family>
          </font>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout" stretch="0,0">
          <property name="spacing">
           <number>10</number>
          </property>
          <property name="sizeConstraint">
           <enum>QLayout::SetDefaultConstraint</enum>
          </property>
          <property name="leftMargin">
           <number>2</number>
          </property>
          <property name="topMargin">
           <number>5</number>
          </property>
          <property name="rightMargin">
           <number>2</number>
          </property>
          <property name="bottomMargin">
           <number>1</number>
          </property>
          <item>
           <widget class="QWidget" name="Toolitem_window_1" native="true">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="maximumSize">
             <size>
              <width>340</width>
              <height>30</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Arial Black</family>
             </font>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="5,2,2,2">
             <property name="spacing">
              <number>5</number>
             </property>
             <property name="sizeConstraint">
              <enum>QLayout::SetDefaultConstraint</enum>
             </property>
             <property name="leftMargin">
              <number>1</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>1</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="toolname_label_1">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(255, 255, 255);</string>
               </property>
               <property name="text">
                <string>IP</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="Tool_link_icon_1">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">border:none;</string>
               </property>
               <property name="text">
                <string notr="true"/>
               </property>
               <property name="icon">
                <iconset resource="../../system_image/system_image.qrc">
                 <normaloff>:/link_ico/offline.png</normaloff>
                 <normalon>:/link_ico/online.png</normalon>:/link_ico/offline.png</iconset>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="Tool_setting_Btn_1">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>10</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color: #0C0C44;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color: #0C0C44;
	border: 2px solid #7AFEC6;
    color: rgb(255, 255, 255);
	border-radius:5px;
	padding:2px;
}

QPushButton:pressed {
    background-color: gray;
	border: none;
}</string>
               </property>
               <property name="text">
                <string>設定</string>
               </property>
               <property name="iconSize">
                <size>
                 <width>20</width>
                 <height>20</height>
                </size>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="Tool_link_Btn_1">
               <property name="font">
                <font>
                 <family>Arial Black</family>
                 <pointsize>10</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color:#5448B6;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color:#7AFEC6;
     color: rgb(255, 255, 255);
    border:none
}

QPushButton:pressed {
    background-color: gray;
	border:none
}</string>
               </property>
               <property name="text">
                <string notr="true">連接</string>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer">
            <property name="font">
             <font>
              <family>Arial Black</family>
             </font>
            </property>
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../system_image/system_image.qrc"/>
 </resources>
 <connections/>
</ui>
