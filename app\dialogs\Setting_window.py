# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'Setting_window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

import system_image.system_image_rc

class Ui_Setting_Window(object):
    def setupUi(self, Setting_Window):
        if not Setting_Window.objectName():
            Setting_Window.setObjectName(u"Setting_Window")
        Setting_Window.resize(500, 165)
        sizePolicy = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(Setting_Window.sizePolicy().hasHeightForWidth())
        Setting_Window.setSizePolicy(sizePolicy)
        Setting_Window.setMinimumSize(QSize(500, 165))
        Setting_Window.setMaximumSize(QSize(500, 170))
        Setting_Window.setStyleSheet(u"QWidget#Setting_Window\n"
"{\n"
"background-color: #0C0C44;\n"
"border: 2px solid #5448B6;\n"
"}")
        self.verticalLayout = QVBoxLayout(Setting_Window)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.setting_title = QWidget(Setting_Window)
        self.setting_title.setObjectName(u"setting_title")
        self.setting_title.setStyleSheet(u"QWidget#setting_title{\n"
"background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));\n"
"border-radius:5px;\n"
"}\n"
"")
        self.horizontalLayout = QHBoxLayout(self.setting_title)
        self.horizontalLayout.setSpacing(0)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(0, 0, 0, 0)
        self.Setting_label = QLabel(self.setting_title)
        self.Setting_label.setObjectName(u"Setting_label")
        font = QFont()
        font.setFamily(u"Arial Black")
        font.setPointSize(12)
        self.Setting_label.setFont(font)
        self.Setting_label.setLayoutDirection(Qt.LeftToRight)
        self.Setting_label.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.Setting_label.setAlignment(Qt.AlignCenter)
        self.Setting_label.setMargin(5)
        self.Setting_label.setIndent(-1)

        self.horizontalLayout.addWidget(self.Setting_label)

        self.horizontalSpacer_1 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer_1)


        self.verticalLayout.addWidget(self.setting_title)

        self.gridLayout = QGridLayout()
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setHorizontalSpacing(8)
        self.gridLayout.setVerticalSpacing(6)
        self.gridLayout.setContentsMargins(-1, -1, 8, -1)
        self.FolderPath_lineEdit = QLineEdit(Setting_Window)
        self.FolderPath_lineEdit.setObjectName(u"FolderPath_lineEdit")

        self.gridLayout.addWidget(self.FolderPath_lineEdit, 0, 2, 1, 1)

        self.txtname_lable = QLabel(Setting_Window)
        self.txtname_lable.setObjectName(u"txtname_lable")
        font1 = QFont()
        font1.setFamily(u"Arial Black")
        font1.setPointSize(11)
        self.txtname_lable.setFont(font1)
        self.txtname_lable.setLayoutDirection(Qt.LeftToRight)
        self.txtname_lable.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.txtname_lable.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.txtname_lable.setMargin(2)

        self.gridLayout.addWidget(self.txtname_lable, 1, 1, 1, 1)

        self.txtname_lineEdit = QLineEdit(Setting_Window)
        self.txtname_lineEdit.setObjectName(u"txtname_lineEdit")

        self.gridLayout.addWidget(self.txtname_lineEdit, 1, 2, 1, 1)

        self.FolderPath_lable = QLabel(Setting_Window)
        self.FolderPath_lable.setObjectName(u"FolderPath_lable")
        self.FolderPath_lable.setFont(font1)
        self.FolderPath_lable.setLayoutDirection(Qt.LeftToRight)
        self.FolderPath_lable.setStyleSheet(u"color: rgb(255, 255, 255);")
        self.FolderPath_lable.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.FolderPath_lable.setMargin(2)

        self.gridLayout.addWidget(self.FolderPath_lable, 0, 1, 1, 1)

        self.Setting_AutoFileName_btn = QPushButton(Setting_Window)
        self.Setting_AutoFileName_btn.setObjectName(u"Setting_AutoFileName_btn")
        self.Setting_AutoFileName_btn.setStyleSheet(u"border:none;")
        icon = QIcon()
        icon.addFile(u":/btn_update/btn_update.png", QSize(), QIcon.Normal, QIcon.Off)
        self.Setting_AutoFileName_btn.setIcon(icon)
        self.Setting_AutoFileName_btn.setIconSize(QSize(18, 18))

        self.gridLayout.addWidget(self.Setting_AutoFileName_btn, 1, 3, 1, 1)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.gridLayout.addItem(self.horizontalSpacer, 0, 0, 1, 1)

        self.gridLayout.setColumnStretch(0, 1)
        self.gridLayout.setColumnStretch(1, 2)
        self.gridLayout.setColumnStretch(2, 15)
        self.gridLayout.setColumnStretch(3, 1)

        self.verticalLayout.addLayout(self.gridLayout)

        self.Setting_save_close = QWidget(Setting_Window)
        self.Setting_save_close.setObjectName(u"Setting_save_close")
        self.horizontalLayout_9 = QHBoxLayout(self.Setting_save_close)
        self.horizontalLayout_9.setSpacing(0)
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.horizontalLayout_9.setContentsMargins(16, 8, 8, 8)
        self.save_rawdata_checkBox = QCheckBox(self.Setting_save_close)
        self.save_rawdata_checkBox.setObjectName(u"save_rawdata_checkBox")
        font2 = QFont()
        font2.setFamily(u"Arial Black")
        font2.setPointSize(10)
        self.save_rawdata_checkBox.setFont(font2)
        self.save_rawdata_checkBox.setStyleSheet(u"QCheckBox{color: rgb(255, 255, 255);}\n"
"\n"
"QCheckBox::indicator:unchecked{\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}\n"
"QCheckBox::indicator:unchecked:hover {\n"
"image: url(:/btn_check/btn_check_s0_1.png);\n"
"}\n"
"QCheckBox::indicator:checked{\n"
"image: url(:/btn_check/btn_check_s1_0.png);\n"
"}\n"
"QCheckBox::indicator:checked:hover {\n"
"image: url(:/btn_check/btn_check_s1_1.png);\n"
"}\n"
"QCheckBox::indicator:disabled {\n"
"image: url(:/btn_check/btn_check_s0_0.png);\n"
"}")

        self.horizontalLayout_9.addWidget(self.save_rawdata_checkBox)

        self.horizontalSpacer_10 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_10)

        self.Setting_save_btn = QPushButton(self.Setting_save_close)
        self.Setting_save_btn.setObjectName(u"Setting_save_btn")
        self.Setting_save_btn.setMinimumSize(QSize(10, 10))
        self.Setting_save_btn.setFont(font2)
        self.Setting_save_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color:#5448B6;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7AFEC6;\n"
"     color: rgb(255, 255, 255);\n"
"    border:none\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border:none\n"
"}")
        self.Setting_save_btn.setIconSize(QSize(20, 20))
        self.Setting_save_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.Setting_save_btn)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_5)

        self.Setting_close_btn = QPushButton(self.Setting_save_close)
        self.Setting_close_btn.setObjectName(u"Setting_close_btn")
        self.Setting_close_btn.setMinimumSize(QSize(10, 10))
        self.Setting_close_btn.setFont(font2)
        self.Setting_close_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: 2px solid #7AFEC6;\n"
"    color: rgb(255, 255, 255);\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")
        self.Setting_close_btn.setIconSize(QSize(20, 20))
        self.Setting_close_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.Setting_close_btn)

        self.horizontalLayout_9.setStretch(1, 8)
        self.horizontalLayout_9.setStretch(2, 3)
        self.horizontalLayout_9.setStretch(3, 1)
        self.horizontalLayout_9.setStretch(4, 3)

        self.verticalLayout.addWidget(self.Setting_save_close)


        self.retranslateUi(Setting_Window)

        QMetaObject.connectSlotsByName(Setting_Window)
    # setupUi

    def retranslateUi(self, Setting_Window):
        Setting_Window.setWindowTitle(QCoreApplication.translate("Setting_Window", u"Form", None))
        self.Setting_label.setText(QCoreApplication.translate("Setting_Window", u"\u6a94\u6848\u8def\u5f91", None))
        self.txtname_lable.setText(QCoreApplication.translate("Setting_Window", u"msra\u6a94\u540d :", None))
        self.FolderPath_lable.setText(QCoreApplication.translate("Setting_Window", u"\u8cc7\u6599\u593e\u8def\u5f91 :", None))
        self.Setting_AutoFileName_btn.setText("")
        self.save_rawdata_checkBox.setText(QCoreApplication.translate("Setting_Window", u"\u5132\u5b58\u539f\u59cb\u6578\u64da (raw data)", None))
        self.Setting_save_btn.setText(QCoreApplication.translate("Setting_Window", u"\u5132\u5b58", None))
        self.Setting_close_btn.setText(QCoreApplication.translate("Setting_Window", u"\u95dc\u9589", None))
    # retranslateUi

