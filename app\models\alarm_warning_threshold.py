from . import logger

class AlarmWarningThresholds:
    """
    Rule: upper_bound >= warning_upper >= alarm_upper >= alarm_lower >= warning_lower >= lower_bound.
    Upon rule violation, priority is warning, alarm, bounding.
    """

    def __init__(self, target_name:str, 
        is_auto:bool=True, 
        upper_bound:float=10.0, lower_bound:float=-10.0, 
        warning_upper:float=10.0, warning_lower:float=-10.0, 
        alarm_upper:float=10.0, alarm_lower:float=-10.0, 
    ):
        self.target_name = target_name
        self.is_auto = is_auto
        if warning_upper < warning_lower:
            warning_upper, warning_lower = warning_lower, warning_upper
        self.warning_upper = warning_upper
        self.warning_lower = warning_lower
        if alarm_upper < alarm_lower:
            alarm_upper, alarm_lower = alarm_lower, alarm_upper        
        self.alarm_upper = alarm_upper
        self.alarm_lower = alarm_lower
        if upper_bound < lower_bound:
            upper_bound, lower_bound = lower_bound, upper_bound
        self.upper_bound = upper_bound
        self.lower_bound = lower_bound

    @property
    def warning_upper(self):
        return self._warning_upper
    @warning_upper.setter
    def warning_upper(self, value):
        self._warning_upper = max(getattr(self, '_warning_lower', value), value)
        if getattr(self, '_upper_bound', value) < value:
            self.upper_bound = value
        if getattr(self, '_alarm_upper', value) > value:
            self.alarm_upper = value
        logger.info(f"{self.target_name}: warning_upper set to {self._warning_upper}")

    @property
    def warning_lower(self):
        return self._warning_lower
    @warning_lower.setter
    def warning_lower(self, value):
        self._warning_lower = min(getattr(self, '_warning_upper', value), value)
        if getattr(self, '_lower_bound', value) > value:
            self.lower_bound = value
        if getattr(self, '_alarm_lower', value) < value:
            self.alarm_lower = value
        logger.info(f"{self.target_name}: warning_lower set to {self._warning_lower}")

    @property
    def alarm_upper(self):
        return self._alarm_upper
    @alarm_upper.setter
    def alarm_upper(self, value):
        value = max(getattr(self, '_alarm_lower', value), value)
        self._alarm_upper = min(getattr(self, '_warning_upper', value), value)
        logger.info(f"{self.target_name}: alarm_upper set to {self._alarm_upper}")

    @property
    def alarm_lower(self):
        return self._alarm_lower
    @alarm_lower.setter
    def alarm_lower(self, value):
        value = max(getattr(self, '_alarm_upper', value), value)
        self._alarm_lower = min(getattr(self, '_warning_lower', value), value)
        logger.info(f"{self.target_name}: alarm_lower set to {self._alarm_lower}")
    
    @property
    def upper_bound(self):
        return self._upper_bound
    @upper_bound.setter
    def upper_bound(self, value):
        self._upper_bound = max(value, getattr(self, '_warning_upper', value))
        logger.info(f"{self.target_name}: upper_bound set to {self._upper_bound}")

    @property
    def lower_bound(self):
        return self._lower_bound
    @lower_bound.setter
    def lower_bound(self, value):
        self._lower_bound = max(value, getattr(self, '_warning_lower', value))
        logger.info(f"{self.target_name}: lower_bound set to {self._lower_bound}")
    
    def __repr__(self):
        return (
            f"{self.target_name}: is_auto={self.is_auto}; "
            f"bounds = ({self.upper_bound}, {self.lower_bound}); "
            f"warning threshold = ({self.warning_upper}, {self.warning_lower}); "
            f"alarm threshold = ({self.alarm_upper}, {self.alarm_lower})"
        )