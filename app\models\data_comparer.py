from . import logger  # 導入 logger
import copy
class DataComparer:
    def __init__(self, original_data=None):
        """
        初始化比較器，可選擇性地提供原始數據
        """
        self.original_data = original_data or {}
        self.temp_data = None
    
    def begin_edit(self):
        """
        開始編輯，創建臨時數據的複本
        """
        # self.temp_data = self.original_data.copy()
        self.temp_data = copy.deepcopy(self.original_data)  # 使用深拷貝
        return self.temp_data
    
    def has_differences(self):
        """
        檢查臨時數據與原始數據是否有差異
        只要有任何差異就返回True，否則返回False
        """
        # logger.warning("original_data: %s", self.original_data)
        # logger.warning("temp_data: %s", self.temp_data)
        if self.temp_data is None:
            return False
            
        # 檢查長度差異
        if len(self.original_data) != len(self.temp_data):
            return True
            
        # 檢查每個鍵值對
        for key, value in self.original_data.items():
            if key not in self.temp_data or self.temp_data[key] != value:
                return True
                
        # 檢查temp_data中是否有原字典沒有的鍵
        for key in self.temp_data:
            if key not in self.original_data:
                return True
                
        # 如果上述檢查都通過，表示沒有差異
        return False
    
    def commit(self):
        """
        確認變更並儲存到原始數據
        返回是否進行了儲存（有差異時才儲存）
        """
        if self.temp_data is not None and self.has_differences():
            self.original_data = self.temp_data.copy()
            self.temp_data = None
            return True
        
        self.temp_data = None
        return False
    
    def discard(self):
        """
        放棄變更
        """
        self.temp_data = None
    
    def get_original(self):
        """
        獲取原始數據
        """
        return self.original_data
    
    def update_temp(self, new_data):
        """
        更新臨時數據
        """
        # logger.debug(f"Updating temp_data: {new_data}")
        self.temp_data = new_data

# 使用範例
# filter_comparer = DataComparer({
#     "filter_type": "Nofilter_radio",
#     "filter_values": 1,
#     "filter_edit": 1
# })

# # 開始編輯
# temp = filter_comparer.begin_edit()
# temp["filter_values"] = 2  # 修改數值

# # 檢查是否有差異
# if filter_comparer.has_differences():
#     print("發現差異")
    
#     # 儲存變更
#     if filter_comparer.commit():
#         print("變更已儲存")
# else:
#     print("沒有差異，不需要儲存")
#     filter_comparer.discard()

# # 確認原始數據已更新
# updated_data = filter_comparer.get_original()
# print("更新後的數據:", updated_data)