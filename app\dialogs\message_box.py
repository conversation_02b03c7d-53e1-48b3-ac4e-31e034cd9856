# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'message_box.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *


class Ui_Dialog(object):
    def setupUi(self, Dialog):
        if not Dialog.objectName():
            Dialog.setObjectName(u"Dialog")
        Dialog.resize(500, 200)
        Dialog.setMinimumSize(QSize(500, 200))
        Dialog.setMaximumSize(QSize(500, 200))
        self.verticalLayout = QVBoxLayout(Dialog)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(9, 9, 9, 9)
        self.message = QLabel(Dialog)
        self.message.setObjectName(u"message")
        font = QFont()
        font.setFamily(u"Arial Black")
        font.setPointSize(12)
        self.message.setFont(font)
        self.message.setLayoutDirection(Qt.LeftToRight)
        self.message.setAlignment(Qt.AlignCenter)

        self.verticalLayout.addWidget(self.message)

        self.btn_yes_no = QWidget(Dialog)
        self.btn_yes_no.setObjectName(u"btn_yes_no")
        self.horizontalLayout_9 = QHBoxLayout(self.btn_yes_no)
        self.horizontalLayout_9.setSpacing(0)
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.horizontalLayout_9.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer_10 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_10)

        self.btv_yes_btn = QPushButton(self.btn_yes_no)
        self.btv_yes_btn.setObjectName(u"btv_yes_btn")
        self.btv_yes_btn.setMinimumSize(QSize(10, 10))
        font1 = QFont()
        font1.setFamily(u"Arial Black")
        font1.setPointSize(10)
        self.btv_yes_btn.setFont(font1)
        self.btv_yes_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color:#5448B6;\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color:#7AFEC6;\n"
"     color: rgb(255, 255, 255);\n"
"    border:none\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border:none\n"
"}")
        self.btv_yes_btn.setIconSize(QSize(20, 20))
        self.btv_yes_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.btv_yes_btn)

        self.horizontalSpacer_5 = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)

        self.horizontalLayout_9.addItem(self.horizontalSpacer_5)

        self.btn_no_btn = QPushButton(self.btn_yes_no)
        self.btn_no_btn.setObjectName(u"btn_no_btn")
        self.btn_no_btn.setMinimumSize(QSize(10, 10))
        self.btn_no_btn.setFont(font1)
        self.btn_no_btn.setStyleSheet(u"QPushButton {\n"
"    border: 2px solid #5448B6;\n"
"	border-radius:5px;\n"
"	padding:2px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"	background-color: #7AFEC6;\n"
"border: none;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    background-color: gray;\n"
"	border: none;\n"
"}")
        self.btn_no_btn.setIconSize(QSize(20, 20))
        self.btn_no_btn.setCheckable(True)

        self.horizontalLayout_9.addWidget(self.btn_no_btn)

        self.horizontalLayout_9.setStretch(0, 9)
        self.horizontalLayout_9.setStretch(1, 3)
        self.horizontalLayout_9.setStretch(2, 1)
        self.horizontalLayout_9.setStretch(3, 3)

        self.verticalLayout.addWidget(self.btn_yes_no)

        self.verticalLayout.setStretch(0, 5)
        self.verticalLayout.setStretch(1, 1)

        self.retranslateUi(Dialog)

        QMetaObject.connectSlotsByName(Dialog)
    # setupUi

    def retranslateUi(self, Dialog):
        Dialog.setWindowTitle(QCoreApplication.translate("Dialog", u"Dialog", None))
        self.message.setText(QCoreApplication.translate("Dialog", u"message", None))
        self.btv_yes_btn.setText(QCoreApplication.translate("Dialog", u"\u662f", None))
        self.btn_no_btn.setText(QCoreApplication.translate("Dialog", u"\u5426", None))
    # retranslateUi

