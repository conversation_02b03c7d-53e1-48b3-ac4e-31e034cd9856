<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RecordScript_window</class>
 <widget class="QWidget" name="RecordScript_window">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>570</width>
    <height>596</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <family>Arial Black</family>
   </font>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#RecordScript_window
{
background-color: #0C0C44;
border: 2px solid #5448B6;
}</string>
  </property>
  <property name="inputMethodHints">
   <set>Qt::ImhNone</set>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <widget class="QWidget" name="record_script_window_title" native="true">
     <property name="font">
      <font>
       <family>Arial Black</family>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QWidget#record_script_window_title{
background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));
border-radius:5px;
}
</string>
     </property>
     <layout class="QHBoxLayout" name="_2" stretch="1,100,1">
      <property name="rightMargin">
       <number>5</number>
      </property>
      <item>
       <widget class="QLabel" name="title_label">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>12</pointsize>
          <weight>50</weight>
          <bold>false</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);</string>
        </property>
        <property name="text">
         <string>錄製腳本</string>
        </property>
        <property name="textFormat">
         <enum>Qt::AutoText</enum>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_5">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="close_button">
        <property name="minimumSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="baseSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="font">
         <font>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="autoFillBackground">
         <bool>false</bool>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    border-image: url(:/btn_close/btn_close_0.png) no-repeat right center;
    border: none;
}

QPushButton:hover {
    border-image: url(:/btn_close/btn_close_1.png) no-repeat right center;
}

QPushButton:pressed {
    border-image: url(:/btn_close/btn_close_2.png) no-repeat right center;
}</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="iconSize">
         <size>
          <width>15</width>
          <height>15</height>
         </size>
        </property>
        <property name="shortcut">
         <string/>
        </property>
        <property name="checkable">
         <bool>false</bool>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
        <property name="default">
         <bool>false</bool>
        </property>
        <property name="flat">
         <bool>false</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="mode_group" native="true">
     <layout class="QGridLayout" name="gridLayout">
      <property name="leftMargin">
       <number>16</number>
      </property>
      <property name="topMargin">
       <number>16</number>
      </property>
      <property name="bottomMargin">
       <number>12</number>
      </property>
      <property name="spacing">
       <number>20</number>
      </property>
      <item row="4" column="1">
       <widget class="QCheckBox" name="time_mode_record_check">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
          <weight>50</weight>
          <bold>false</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
QCheckBox::indicator:disabled {
image: url(:/btn_check/btn_check_s0_0.png);
}</string>
        </property>
        <property name="text">
         <string>錄製</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_10">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="2" column="1">
       <widget class="QCheckBox" name="machine_mode_record_check">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
          <weight>50</weight>
          <bold>false</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
QCheckBox::indicator:disabled {
image: url(:/btn_check/btn_check_s0_0.png);
}</string>
        </property>
        <property name="text">
         <string>錄製</string>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <widget class="QRadioButton" name="standard_mode_radio">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
          <weight>50</weight>
          <bold>false</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
        </property>
        <property name="text">
         <string>標準模式</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QRadioButton" name="machine_mode_radio">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
          <weight>50</weight>
          <bold>false</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
        </property>
        <property name="text">
         <string>機器通訊模式</string>
        </property>
       </widget>
      </item>
      <item row="4" column="0">
       <widget class="QRadioButton" name="time_mode_radio">
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
          <weight>50</weight>
          <bold>false</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QRadioButton{color: rgb(255, 255, 255);}


QRadioButton::indicator:unchecked{
image: url(:/btn_radio/btn_radio_s0_0.png);
}
QRadioButton::indicator:unchecked:hover {
image: url(:/btn_radio/btn_radio_s0_1.png);
}
QRadioButton::indicator:checked{
image: url(:/btn_radio/btn_radio_s1_0.png);
}
QRadioButton::indicator:checked:hover {
image: url(:/btn_radio/btn_radio_s1_1.png);
}
	</string>
        </property>
        <property name="text">
         <string>時間腳本模式</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="RecordScript_ToolList_group" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>3</verstretch>
      </sizepolicy>
     </property>
     <layout class="QGridLayout" name="RecordScript_ToolList_layout">
      <property name="topMargin">
       <number>6</number>
      </property>
      <property name="bottomMargin">
       <number>3</number>
      </property>
      <item row="0" column="0">
       <widget class="QTableView" name="RecordScript_ToolList_Element_List_table">
        <property name="styleSheet">
         <string notr="true">background-color: rgb(0, 0, 0);
border:none</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="RecordScript_PlanList_group" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>5</verstretch>
      </sizepolicy>
     </property>
     <layout class="QVBoxLayout" name="RecordScript_PlanList">
      <property name="topMargin">
       <number>6</number>
      </property>
      <property name="rightMargin">
       <number>9</number>
      </property>
      <property name="bottomMargin">
       <number>12</number>
      </property>
      <item>
       <widget class="QWidget" name="RecordScript_PlanList_title_group" native="true">
        <layout class="QGridLayout" name="RecordScript_PlanList_label" columnstretch="2,3,2,2,2,4">
         <property name="leftMargin">
          <number>3</number>
         </property>
         <property name="bottomMargin">
          <number>6</number>
         </property>
         <item row="1" column="0">
          <widget class="QLabel" name="Tool_Name">
           <property name="font">
            <font>
             <family>Arial Black</family>
             <pointsize>10</pointsize>
             <weight>50</weight>
             <bold>false</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color: rgb(255, 255, 255);</string>
           </property>
           <property name="text">
            <string>刀把名稱</string>
           </property>
          </widget>
         </item>
         <item row="1" column="1">
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item row="1" column="2">
          <widget class="QLabel" name="time">
           <property name="font">
            <font>
             <family>Arial Black</family>
             <pointsize>10</pointsize>
             <weight>50</weight>
             <bold>false</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color: rgb(255, 255, 255);</string>
           </property>
           <property name="text">
            <string>期間</string>
           </property>
          </widget>
         </item>
         <item row="1" column="3">
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item row="1" column="4">
          <widget class="QLabel" name="Totaltime">
           <property name="font">
            <font>
             <family>Arial Black</family>
             <pointsize>10</pointsize>
             <weight>50</weight>
             <bold>false</bold>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">color: rgb(255, 255, 255);</string>
           </property>
           <property name="text">
            <string>總時間</string>
           </property>
          </widget>
         </item>
         <item row="1" column="5">
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <layout class="QGridLayout" name="RecordScript_PlanList_table" columnstretch="7,1">
        <property name="horizontalSpacing">
         <number>12</number>
        </property>
        <item row="0" column="0" rowspan="3">
         <widget class="QTableView" name="RecordScript_PlanList_Element_List_table">
          <property name="styleSheet">
           <string notr="true">background-color: rgb(0, 0, 0);
border:none</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QPushButton" name="reset_button">
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>10</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color: #0C0C44;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color: #0C0C44;
	border: 2px solid #7AFEC6;
    color: rgb(255, 255, 255);
	border-radius:5px;
	padding:2px;
}

QPushButton:pressed {
    background-color: gray;
	border: none;
}</string>
          </property>
          <property name="text">
           <string>重置</string>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="2" column="1">
         <widget class="QPushButton" name="save_button">
          <property name="font">
           <font>
            <family>Arial Black</family>
            <pointsize>10</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color:#5448B6;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color:#7AFEC6;
     color: rgb(255, 255, 255);
    border:none
}

QPushButton:pressed {
    background-color: gray;
	border:none
}</string>
          </property>
          <property name="text">
           <string>儲存</string>
          </property>
          <property name="checkable">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../system_image/system_image.qrc"/>
 </resources>
 <connections/>
</ui>
