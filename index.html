<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>簡單網頁排版</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        header {
            background-color: #333;
            color: white;
            padding: 10px 0;
            text-align: center;
        }
        .container {
            width: 80%;
            margin: 20px auto;
            background-color: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
        }
        p {
            line-height: 1.6;
        }
        img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px 0;
        }
        a {
            color: #007BFF;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        footer {
            text-align: center;
            padding: 10px;
            background-color: #333;
            color: white;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <header>
        <h1>歡迎來到我的簡單網頁</h1>
    </header>

    <div class="container">
        <h2>關於這個網頁</h2>
        <p>這是一個簡單的 HTML 網頁排版示例。你可以使用 HTML 和 CSS 來創建自己的網頁。</p>
        <p>HTML 是一種用於建立網頁的標記語言，而 CSS 則用於控制網頁的外觀和風格。</p>

        <h2>圖片示例</h2>
        <img src="https://via.placeholder.com/600x400" alt="示例圖片">

        <h2>連結示例</h2>
        <p>這是一個 <a href="https://www.google.com" target="_blank">連結到 Google</a> 的示例。</p>
    </div>

    <footer>
        <p>&copy; 2023 我的簡單網頁. 保留所有權利。</p>
    </footer>
</body>
</html>