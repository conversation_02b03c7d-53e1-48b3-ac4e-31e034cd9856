from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
from .hint_window import Ui_hint_window

class Ui_pop_up_Window(QDialog, Ui_hint_window):
    def __init__(self, message, timeout=2000, parent=None):
        """
        message: 要顯示的訊息
        timeout: 視窗顯示的時間 (預設 2000 毫秒 = 2 秒)
        """
        super().__init__(parent)
        self.setupUi(self)

        # 設定無邊框
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)

        # 設定訊息內容
        self.set_message(message)

    def set_message(self, message):
        """更新對話框的訊息"""
        self.label.setText(message)
    
    def update_label(self, new_message):
        """更新視窗內的訊息"""
        self.label.setText(new_message)

