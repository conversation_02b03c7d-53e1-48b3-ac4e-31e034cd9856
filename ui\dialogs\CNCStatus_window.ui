<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CNCStatus_window</class>
 <widget class="QWidget" name="CNCStatus_window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>200</width>
    <height>59</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>200</width>
    <height>20</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>200</width>
    <height>120</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#CNCStatus_window{
    background-color: #0C0C44;
    border: 2px solid #5448B6;
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>3</number>
   </property>
   <property name="topMargin">
    <number>3</number>
   </property>
   <property name="rightMargin">
    <number>3</number>
   </property>
   <property name="bottomMargin">
    <number>6</number>
   </property>
   <item>
    <widget class="QWidget" name="title_group" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>2</number>
      </property>
      <property name="leftMargin">
       <number>6</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>3</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="expand_btn">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>14</width>
          <height>14</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>14</width>
          <height>14</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial</family>
          <pointsize>12</pointsize>
          <weight>50</weight>
          <bold>false</bold>
         </font>
        </property>
        <property name="cursor">
         <cursorShape>ArrowCursor</cursorShape>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color: #0C0C44;
    border: None;
}

QPushButton:hover {
    background-color: #0C0C44;
	border: None;
    color: #7AFEC6;
}

QPushButton:pressed {
    color: gray;
	border: none;
}</string>
        </property>
        <property name="text">
         <string>+</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="flat">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="title_label">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="font">
         <font>
          <family>Arial</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">color: rgb(255, 255, 255);
</string>
        </property>
        <property name="text">
         <string>CNC：</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="margin">
         <number>4</number>
        </property>
       </widget>
      </item>
      <item alignment="Qt::AlignVCenter">
       <widget class="QPushButton" name="cancel_btn">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Consolas</family>
          <pointsize>12</pointsize>
          <stylestrategy>PreferDefault</stylestrategy>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    color: rgb(255, 0, 0);
    background-color: #0C0C44;
    border: None;
    margin-top: 4px;
    margin-left: 2px;
    margin-right: 2px; 
    padding-bottom:4px;
}

QPushButton:hover {
    background-color: red;
	border: None;
    color: #0C0C44;
}

QPushButton:pressed {
    color: gray;
	border: none;
}</string>
        </property>
        <property name="text">
         <string>x</string>
        </property>
        <property name="iconSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="status_group" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>190</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>190</width>
       <height>16777215</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Maximum</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPlainTextEdit" name="status_text">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>160</width>
          <height>24</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>160</width>
          <height>100</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">color:white;
background-color: #0C0C44;
border-top: 1px solid #5448B6;
vertical-align: text-top;
padding: 0px;
margin: 0px;</string>
        </property>
        <property name="inputMethodHints">
         <set>Qt::ImhNone</set>
        </property>
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Plain</enum>
        </property>
        <property name="lineWidth">
         <number>0</number>
        </property>
        <property name="verticalScrollBarPolicy">
         <enum>Qt::ScrollBarAlwaysOff</enum>
        </property>
        <property name="horizontalScrollBarPolicy">
         <enum>Qt::ScrollBarAlwaysOff</enum>
        </property>
        <property name="sizeAdjustPolicy">
         <enum>QAbstractScrollArea::AdjustToContents</enum>
        </property>
        <property name="readOnly">
         <bool>true</bool>
        </property>
        <property name="plainText">
         <string>[機台狀態]
主軸轉速：
刀把編號：</string>
        </property>
        <property name="textInteractionFlags">
         <set>Qt::NoTextInteraction</set>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
