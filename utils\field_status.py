from utils.enums import StressSafetyStatus
from typing import Dict
from . import logger


class FieldStatus(dict):
    def __init__(self):
        super().__init__(
            {
                "C.F.": StressSafetyStatus.NO_DATA,
                "Fz": StressSafetyStatus.NO_DATA,
                "Torque": StressSafetyStatus.NO_DATA,
                "Temp.": StressSafetyStatus.NO_DATA
            }
        )
    
    def update_status(self, field_name:str, status:StressSafetyStatus):
        self[field_name] = status
    
    def get_status(self, field_name:str) -> StressSafetyStatus:
        return self[field_name]
    
    def get_all_status(self) -> Dict[str, StressSafetyStatus]:
        return self
    
    def update_all_status(self, status:Dict[str, StressSafetyStatus]):
        self.update(status)

    def get_most_dangerous_status(self):
        return max(self.values(), key=lambda x: x.value)
    
    def check_status(self, field_name:str, value:float, warning_plus:float, alarm_plus:float, warning_minus = None, alarm_minus = None):
        try:
            if value > alarm_plus or (alarm_minus and value < alarm_minus):
                self.update_status(field_name, StressSafetyStatus.ALARM)
            elif value > warning_plus or (warning_minus and value < warning_minus):
                self.update_status(field_name, StressSafetyStatus.WARNING)
            else:
                self.update_status(field_name, StressSafetyStatus.SAFE)
        except Exception as e:
            logger.error(f"Error checking status for {field_name}: {e}")
