import sys , math
import numpy as np
from PySide2.QtWidgets import QApplication, QVBoxLayout, QWidget
from PySide2.QtCore import QTimer, Signal, Slot
import pyqtgraph as pg
from . import logger  # 從同一個包導入 logger

class Animation_Bending(QWidget):
    # sigUpdate = Signal(np.ndarray, np.ndarray, np.ndarray)  # 訊號，讓外部傳新資料

    def __init__(self):
        super().__init__()

        # 初始化變數
        self.init_variables()

        # 設置布局
        self.layout = QVBoxLayout()
        self.setLayout(self.layout)

        # 創建 PlotWidget
        self.plot_widget = pg.PlotWidget()
        self.layout.addWidget(self.plot_widget)

        # 設置繪圖範圍
        self.plot_widget.setXRange(0, self.SamplePoint1)
        self.plot_widget.setYRange(self.Y_min, self.Y_max)

        # 初始化繪圖
        self.init_plot()

        # 建立定時器，每 100ms 更新一次
        # self.timer = QTimer()
        # self.timer.timeout.connect(self.update_data)
        # self.timer.start(100)

        # 連接外部信號
        # self.sigUpdate.connect(self.set_data)

    def init_variables(self):
        """初始化變數"""
        SampleRate = 10000 # 取資料
        self.SamplePoint1=math.ceil(SampleRate/12.5)  # 每一秒取5次
        # self.SamplePoint1 = 1000
        self.Y_min = -1
        self.Y_max = 1
        # self.MS_BendingX = np.random.rand(self.SamplePoint1) * 10 - 5
        # self.MS_BendingY = np.random.rand(self.SamplePoint1) * 10 - 5
        # self.MS_BendingXY = np.random.rand(self.SamplePoint1) * 10 - 5
        self.MS_BendingX = np.zeros(self.SamplePoint1)
        self.MS_BendingY = np.zeros(self.SamplePoint1)
        self.MS_BendingXY = np.zeros(self.SamplePoint1)
        self.BendingX_Visible = True
        self.BendingY_Visible = True
        self.BendingXY_Visible = True

    def init_plot(self):
        """初始化繪圖"""
        self.plot_widget.clear()
        self.plot_widget.showGrid(y=True)
        self.plot_widget.getAxis('bottom').setStyle(showValues=False)
        self.plot_widget.getAxis('bottom').setHeight(0)
        self.plot_widget.getViewBox().setMouseEnabled(x=False, y=True)
        self.plot_widget.getViewBox().enableAutoRange(axis=pg.ViewBox.XAxis)

        # 初始化曲線
        self.curveX = self.plot_widget.plot(pen=pg.mkPen(color=(180, 225, 0), width=1))
        self.curveY = self.plot_widget.plot(pen=pg.mkPen(color=(255, 255, 255), width=1))
        self.curveXY = self.plot_widget.plot(pen=pg.mkPen(color=(0, 255, 255), width=1))

    @Slot(np.ndarray, np.ndarray, np.ndarray)
    def set_data(self, bx, by, bxy):
        """外部可以透過此函數來更新資料"""
        # logger.info(f"bx: {bx}")
        self.MS_BendingX = bx
        self.MS_BendingY = by
        self.MS_BendingXY = bxy
        self.redraw()

    # def update_data(self):
    #     """模擬新數據，並更新畫面"""
    #     bx = np.random.rand(self.SamplePoint1) * 10 - 5
    #     by = np.random.rand(self.SamplePoint1) * 10 - 5
    #     bxy = np.random.rand(self.SamplePoint1) * 10 - 5
    #     self.set_data(bx, by, bxy)

    # 修改 Animation_Bending 類中的 redraw 方法
    def redraw(self):
        """更新圖表，如果可見性為False則清空曲線數據"""
        if self.BendingX_Visible:
            self.curveX.setData(self.MS_BendingX)
            self.curveX.show()  # 確保曲線是可見的
        else:
            self.curveX.setData([])  # 設置空數據使曲線消失
            self.curveX.hide()  # 直接隱藏曲線（這是一個更直接的方法）
            
        if self.BendingY_Visible:
            self.curveY.setData(self.MS_BendingY)
            self.curveY.show()
        else:
            self.curveY.setData([])
            self.curveY.hide()
            
        if self.BendingXY_Visible:
            self.curveXY.setData(self.MS_BendingXY)
            self.curveXY.show()
        else:
            self.curveXY.setData([])
            self.curveXY.hide()
            
    def set_y_range(self, y_min, y_max):
        """設定Y軸範圍（手動模式）"""
        self.Y_min = y_min
        self.Y_max = y_max
        self.auto_range_enabled = False  # 關閉自動範圍
        self.plot_widget.setYRange(self.Y_min, self.Y_max)
        self.plot_widget.getViewBox().enableAutoRange(axis=pg.ViewBox.YAxis, enable=False)

    def set_auto_range(self, enable=True):
        """切換自動範圍模式"""
        self.auto_range_enabled = enable
        if enable:
            # 啟用自動範圍
            self.plot_widget.getViewBox().enableAutoRange(axis=pg.ViewBox.YAxis, enable=True)
        else:
            # 使用固定範圍
            self.plot_widget.getViewBox().enableAutoRange(axis=pg.ViewBox.YAxis, enable=False)
            self.plot_widget.setYRange(self.Y_min, self.Y_max)

# if __name__ == "__main__":
#     app = QApplication(sys.argv)
#     window = Animation_Bending()
#     window.show()
#     sys.exit(app.exec_())
