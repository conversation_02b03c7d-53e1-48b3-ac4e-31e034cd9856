import os
from . import logger
from PySide2.QtWidgets import QFileDialog

class TextFileManager:
    def __init__(self, directory: str):
        self.directory = directory
        os.makedirs(self.directory, exist_ok=True)
        print(f"目錄 '{self.directory}' 已確認存在。")

    def _get_filepath(self, filename: str) -> str:
        return os.path.join(self.directory, filename)

    def create_file(self, filename: str) -> None:
        filepath = self._get_filepath(filename)
        try:
            with open(filepath, "w", encoding="utf-8"):
                print(f"檔案 '{filepath}' 創建成功。")
        except Exception as e:
            print(f"創建檔案失敗：{e}")

    def write_text(self, filename: str, text: str) -> None:
        """覆蓋寫入內容。"""
        filepath = self._get_filepath(filename)
        try:
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(text)
        except Exception as e:
            print(f"寫入文字失敗：{e}")

    def add_text(self, filename: str, text: str) -> None:
        """附加文字內容。"""
        filepath = self._get_filepath(filename)
        try:
            with open(filepath, "a", encoding="utf-8") as f:
                f.write(text)
        except Exception as e:
            print(f"新增文字失敗：{e}")

    def delete_text(self, filename: str, text_to_delete: str) -> None:
        filepath = self._get_filepath(filename)
        try:
            with open(filepath, "r", encoding="utf-8") as f:
                lines = f.readlines()

            with open(filepath, "w", encoding="utf-8") as f:
                f.writelines([line for line in lines if text_to_delete not in line])

            print(f"已從 '{filepath}' 刪除指定文字。")
        except Exception as e:
            print(f"刪除文字失敗：{e}")

    def load_file(self, file_path: str) -> str:
        if not file_path or not os.path.exists(file_path):
            print("檔案路徑無效。")
            return ""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"成功讀取檔案 '{file_path}'。")
                return content
        except Exception as e:
            print(f"讀取檔案失敗：{e}")
            return ""

    def choose_file(self) -> str:
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog

        file_path, _ = QFileDialog.getOpenFileName(
            parent=None,
            caption="選擇要讀取的檔案",
            dir=self.directory,
            filter="MSRB Files (*.msrb);;Text Files (*.txt);;All Files (*)",
            options=options
        )

        if file_path:
            print(f"選擇檔案: {file_path}")
        else:
            print("未選擇檔案。")
        return file_path

    def close_file(self):
        """預留擴充，若改用 QFile 等更進階類別再實作。"""
        pass
