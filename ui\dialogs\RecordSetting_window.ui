<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RecordSetting_window</class>
 <widget class="QWidget" name="RecordSetting_window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>900</width>
    <height>635</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>900</width>
    <height>635</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>900</width>
    <height>635</height>
   </size>
  </property>
  <property name="font">
   <font>
    <family>Arial Black</family>
    <pointsize>10</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#RecordSetting_window{background-color: #0C0C44;
    border: 2px solid #5448B6;}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="10,1">
   <property name="leftMargin">
    <number>9</number>
   </property>
   <property name="topMargin">
    <number>9</number>
   </property>
   <property name="rightMargin">
    <number>9</number>
   </property>
   <property name="bottomMargin">
    <number>9</number>
   </property>
   <item>
    <widget class="QWidget" name="RecordSetting_body" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,5">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item alignment="Qt::AlignTop">
       <widget class="QWidget" name="RecordSetting_settings" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <property name="leftMargin">
          <number>12</number>
         </property>
         <property name="topMargin">
          <number>9</number>
         </property>
         <property name="rightMargin">
          <number>9</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QWidget" name="manual_title" native="true">
           <property name="enabled">
            <bool>false</bool>
           </property>
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="styleSheet">
            <string notr="true">QWidget#manual_title{
background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));
border-radius:5px;
}
</string>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <item>
             <widget class="QLabel" name="manual_label">
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>手動</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="manual_group" native="true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_6">
            <item>
             <widget class="QWidget" name="record_img" native="true">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>16</width>
                <height>16</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QWidget#record_img{
image: url(:/btn_record/btn_record_s0_1.png);
}</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="manual_description">
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>在主畫面點擊「錄製」按鈕。</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="auto_title" native="true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="styleSheet">
            <string notr="true">QWidget#auto_title{
background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));
border-radius:5px;
}
</string>
           </property>
           <layout class="QGridLayout" name="gridLayout_2">
            <property name="sizeConstraint">
             <enum>QLayout::SetDefaultConstraint</enum>
            </property>
            <item row="0" column="0" alignment="Qt::AlignVCenter">
             <widget class="QCheckBox" name="auto_record_checkBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="layoutDirection">
               <enum>Qt::LeftToRight</enum>
              </property>
              <property name="styleSheet">
               <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox{margin-top: 4px; margin-left:4px;}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
	</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="iconSize">
               <size>
                <width>16</width>
                <height>16</height>
               </size>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="QLabel" name="auto_record_label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>自動</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="auto_group" native="true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <layout class="QGridLayout" name="gridLayout" rowstretch="0,0,0,0,0,0,0,0,0,0,0" columnstretch="1,0">
            <item row="2" column="1">
             <widget class="QComboBox" name="max_auto_record_count_comboBox">
              <property name="enabled">
               <bool>true</bool>
              </property>
              <property name="currentIndex">
               <number>0</number>
              </property>
              <item>
               <property name="text">
                <string>100筆</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>500筆</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>1000筆</string>
               </property>
              </item>
             </widget>
            </item>
            <item row="5" column="0">
             <widget class="QLabel" name="pre_record_label">
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>預錄秒數</string>
              </property>
             </widget>
            </item>
            <item row="2" column="0">
             <widget class="QLabel" name="max_auto_record_count_label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>錄製筆數</string>
              </property>
             </widget>
            </item>
            <item row="6" column="0">
             <widget class="QLabel" name="record_seconds_label">
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>錄製秒數</string>
              </property>
             </widget>
            </item>
            <item row="6" column="1">
             <widget class="QLineEdit" name="record_seconds_lineEdit">
              <property name="text">
               <string>10</string>
              </property>
              <property name="placeholderText">
               <string>10</string>
              </property>
             </widget>
            </item>
            <item row="8" column="0">
             <widget class="QCheckBox" name="cf_enabled_checkBox">
              <property name="enabled">
               <bool>true</bool>
              </property>
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
QCheckBox::indicator:disabled {
image: url(:/btn_check/btn_check_s0_0.png);
}</string>
              </property>
              <property name="text">
               <string>C.F.:</string>
              </property>
              <property name="checked">
               <bool>false</bool>
              </property>
             </widget>
            </item>
            <item row="5" column="1">
             <widget class="QLineEdit" name="pre_record_seconds_lineEdit">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="text">
               <string>0</string>
              </property>
              <property name="placeholderText">
               <string>0</string>
              </property>
             </widget>
            </item>
            <item row="8" column="1">
             <widget class="QLineEdit" name="cf_threshold_lineEdit"/>
            </item>
            <item row="9" column="0">
             <widget class="QCheckBox" name="fz_enabled_checkBox">
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
QCheckBox::indicator:disabled {
image: url(:/btn_check/btn_check_s0_0.png);
}</string>
              </property>
              <property name="text">
               <string>Fz:</string>
              </property>
             </widget>
            </item>
            <item row="9" column="1">
             <widget class="QLineEdit" name="fz_threshold_lineEdit"/>
            </item>
            <item row="10" column="1">
             <widget class="QLineEdit" name="t_threshold_lineEdit"/>
            </item>
            <item row="10" column="0">
             <widget class="QCheckBox" name="t_enabled_checkbox">
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
QCheckBox::indicator:disabled {
image: url(:/btn_check/btn_check_s0_0.png);
}</string>
              </property>
              <property name="text">
               <string>T:</string>
              </property>
             </widget>
            </item>
            <item row="1" column="0" colspan="2">
             <spacer name="verticalSpacer_3">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>10</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="4" column="0" colspan="2">
             <spacer name="verticalSpacer">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>208</width>
                <height>5</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="7" column="0" colspan="2">
             <spacer name="verticalSpacer_2">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>208</width>
                <height>5</height>
               </size>
              </property>
             </spacer>
            </item>
            <item row="0" column="0" colspan="2">
             <widget class="QLabel" name="auto_description">
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>在受力到達閥值時自動觸發錄製。</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="machine_title" native="true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="styleSheet">
            <string notr="true">QWidget#machine_title{
background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));
border-radius:5px;
}
</string>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_5">
            <item>
             <widget class="QCheckBox" name="machine_checkBox">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox{margin-top: 4px; margin-left:4px;}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
	</string>
              </property>
              <property name="text">
               <string/>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="machine_label">
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>12</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>機台控制 M Code</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="machine_group" native="true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_3">
            <property name="spacing">
             <number>9</number>
            </property>
            <item>
             <widget class="QLabel" name="machine_description">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>[說明文字] 以機台暫存器值觸發。</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="verticalSpacer_4">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>20</width>
                <height>10</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QCheckBox" name="M101_enabled_checkBox">
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
QCheckBox::indicator:disabled {
image: url(:/btn_check/btn_check_s0_0.png);
}</string>
              </property>
              <property name="text">
               <string>M101</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="M102_enabled_checkBox">
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
QCheckBox::indicator:disabled {
image: url(:/btn_check/btn_check_s0_0.png);
}</string>
              </property>
              <property name="text">
               <string>M102</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QCheckBox" name="M103_enabled_checkBox">
              <property name="enabled">
               <bool>true</bool>
              </property>
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">QCheckBox{color: rgb(255, 255, 255);}

QCheckBox::indicator:unchecked{
image: url(:/btn_check/btn_check_s0_0.png);
}
QCheckBox::indicator:unchecked:hover {
image: url(:/btn_check/btn_check_s0_1.png);
}
QCheckBox::indicator:checked{
image: url(:/btn_check/btn_check_s1_0.png);
}
QCheckBox::indicator:checked:hover {
image: url(:/btn_check/btn_check_s1_1.png);
}
QCheckBox::indicator:disabled {
image: url(:/btn_check/btn_check_s0_0.png);
}</string>
              </property>
              <property name="text">
               <string>M103</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="RecordSetting_sample" native="true">
        <layout class="QVBoxLayout" name="verticalLayout_4">
         <property name="topMargin">
          <number>9</number>
         </property>
         <property name="bottomMargin">
          <number>9</number>
         </property>
         <item>
          <widget class="QWidget" name="sample_title_group" native="true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_7">
            <property name="leftMargin">
             <number>9</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QWidget" name="sample_title" native="true">
              <property name="sizePolicy">
               <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true">QWidget#sample_title{
background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:0, stop:0 rgba(0, 0, 168, 141), stop:1 rgba(0, 0, 0, 0));
border-radius:5px;
}
</string>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_4">
               <item>
                <widget class="QLabel" name="sample_label">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="font">
                  <font>
                   <family>Arial Black</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">color: rgb(255, 255, 255);</string>
                 </property>
                 <property name="text">
                  <string>錄製樣本</string>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_3">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>360</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="sample_buttons" native="true">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_8">
            <item>
             <widget class="QPushButton" name="sample_record_btn">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="icon">
               <iconset resource="../../system_image/system_image.qrc">
                <normaloff>:/btn_record/btn_record_s0_0.png</normaloff>
                <normalon>:/btn_record/btn_record_s1_0.png</normalon>
                <activeoff>:/btn_record/btn_record_s0_0.png</activeoff>
                <activeon>:/btn_record/btn_record_s1_0.png</activeon>:/btn_record/btn_record_s0_0.png</iconset>
              </property>
              <property name="iconSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="sample_record_label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>開始錄製</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>30</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QPushButton" name="sample_redo_btn">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton { border: none; color: rgb(255, 255, 255); }</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="icon">
               <iconset resource="../../system_image/system_image.qrc">
                <normaloff>:/btn_update/btn_update.png</normaloff>:/btn_update/btn_update.png</iconset>
              </property>
              <property name="iconSize">
               <size>
                <width>30</width>
                <height>30</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLabel" name="sample_redo_label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <family>Arial Black</family>
                <pointsize>10</pointsize>
               </font>
              </property>
              <property name="styleSheet">
               <string notr="true">color: rgb(255, 255, 255);</string>
              </property>
              <property name="text">
               <string>重新錄製</string>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_4">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="sample_animation_group" native="true">
           <layout class="QHBoxLayout" name="horizontalLayout_9">
            <property name="bottomMargin">
             <number>9</number>
            </property>
            <item>
             <widget class="QOpenGLWidget" name="sample_2d_animation"/>
            </item>
            <item>
             <spacer name="horizontalSpacer_5">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>5</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="RecordSetting_save_close" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="60,8,2,8">
      <property name="rightMargin">
       <number>20</number>
      </property>
      <property name="bottomMargin">
       <number>20</number>
      </property>
      <item>
       <spacer name="horizontalSpacer_6">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="RecordSetting_save_btn">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color:#5448B6;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color:#7AFEC6;
     color: rgb(255, 255, 255);
    border:none
}

QPushButton:pressed {
    background-color: gray;
	border:none
}</string>
        </property>
        <property name="text">
         <string>儲存</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="RecordSetting_close_btn">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>10</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Arial Black</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color: #0C0C44;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color: #0C0C44;
	border: 2px solid #7AFEC6;
    color: rgb(255, 255, 255);
	border-radius:5px;
	padding:2px;
}

QPushButton:pressed {
    background-color: gray;
	border: none;
}</string>
        </property>
        <property name="text">
         <string>關閉</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../../system_image/system_image.qrc"/>
 </resources>
 <connections/>
</ui>
