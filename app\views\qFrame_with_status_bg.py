from PySide2.QtCore import Slot
from PySide2.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QWidget, QLabel
from utils.enums import StressSafetyStatus
from utils.field_status import FieldStatus


class QFrameWithStatusBG(QFrame):
    def __init__(self, parent:QWidget, name:str, status:FieldStatus):
        super().__init__(parent)
        self.setObjectName(name)
        self.status = status

    @property
    def status(self) -> FieldStatus:
        return self._status
    
    @status.setter
    def status(self, status:FieldStatus):
        self._status = status
        self._set_background_by_status(self._status)
    
    @Slot(FieldStatus)
    def set_status(self, status:FieldStatus):
        self.status = status
    
    def _set_background_by_status(self, status:FieldStatus):
        style_html = ""
        space = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
        for key, value in status.items():
            r, g, b, a = value.color.getRgb()
            style_html += f'<span style="color: rgba({r}, {g}, {b}, {a / 255: .2f});">{key}</span>{space}'
        # 尋找 Label_Status - 它是 bottom_frame 的子元素
        label_status = self.findChild(QLabel, "Label_Status")
        if label_status:
            label_status.setText(style_html)

        # 設置背景顏色
        most_dangerous_status = status.get_most_dangerous_status()
        r, g, b, a = most_dangerous_status.ui_color.getRgb()
        self.setStyleSheet(f"""QFrame#bottom_frame {{
            background-color: qlineargradient(
                spread:pad, 
                x1:0, y1:1, 
                x2:0, y2:0, 
                stop:0 rgba({r}, {g}, {b}, 0.45), 
                stop:1 rgba(0, 0, 0, 0.45)
            );
        }}""")

        self.parent().findChild(QFrame, "Top_frame").setStyleSheet(f"""QFrame#Top_frame {{
            background-color: qlineargradient(
                spread:pad, 
                x1:0, y1:0, 
                x2:0, y2:1, 
                stop:0 rgba({r}, {g}, {b}, 0.45), 
                stop:1 rgba(0, 0, 0, 0.45)
            );
        }}""")

        # 設置各個2D圖表的背景顏色
        # 1. FzV_frame
        r, g, b, a = status.get_status("Fz").ui_color.getRgb()
        self.parent().findChild(QFrame, "FzV_frame").setStyleSheet(f"""QFrame#FzV_frame {{
            background-color: rgba({r}, {g}, {b}, {a / 255: .2f});
            border: 2px solid  rgb({r}, {g}, {b}); /* \u908a\u6846\u6a23\u5f0f */;
        }}""")
        # 2. TorqueV_frame
        r, g, b, a = status.get_status("Torque").ui_color.getRgb()
        self.parent().findChild(QFrame, "TorqueV_frame").setStyleSheet(f"""QFrame#TorqueV_frame {{
            background-color: rgba({r}, {g}, {b}, {a / 255: .2f});
            border: 2px solid  rgb({r}, {g}, {b}); /* \u908a\u6846\u6a23\u5f0f */;
        }}""")
        # 3. TempV_frame
        r, g, b, a = status.get_status("Temp.").ui_color.getRgb()
        self.parent().findChild(QFrame, "TempV_frame").setStyleSheet(f"""QFrame#TempV_frame {{
            background-color: rgba({r}, {g}, {b}, {a / 255: .2f});
            border: 2px solid  rgb({r}, {g}, {b}); /* \u908a\u6846\u6a23\u5f0f */;
        }}""")

        # 4. Fx_Fy_CFH_frame
        r, g, b, a = status.get_status("C.F.").ui_color.getRgb()
        self.parent().findChild(QFrame, "Fx_Fy_CFH_frame").setStyleSheet(f"""QFrame#Fx_Fy_CFH_frame {{
            background-color: rgba({r}, {g}, {b}, {a / 255: .2f});
            border: 2px solid  rgb({r}, {g}, {b}); /* \u908a\u6846\u6a23\u5f0f */;
        }}""")

