from PySide2 import Qt<PERSON><PERSON>, QtGui, QtWidgets

class DraggableDialog(QtWidgets.QDialog):
    def __init__(self, parent=None):
        super(DraggableDialog, self).__init__(parent)
        self.setWindowFlags(QtCore.Qt.FramelessWindowHint | QtCore.Qt.Dialog)
        self.pressing = False
        self.start_pos = None

    def mousePressEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.pressing = True
            self.start_pos = event.globalPos() - self.pos()
            event.accept()

    def mouseMoveEvent(self, event):
        if self.pressing:
            self.move(event.globalPos() - self.start_pos)
            event.accept()

    def mouseReleaseEvent(self, event):
        if event.button() == QtCore.Qt.LeftButton:
            self.pressing = False
            self.start_pos = None
            event.accept()