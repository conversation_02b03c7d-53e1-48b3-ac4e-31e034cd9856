#!/usr/bin/env python3
"""
簡單的 Socket Server 測試
"""

import sys
import os
import socket
import threading
import time

# 添加項目根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def simple_server_test():
    """簡單的 socket server 測試"""
    print("開始簡單的 Socket Server 測試...")
    
    # 創建 server socket
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    
    try:
        # 綁定並監聽
        server_socket.bind(('127.0.0.1', 1333))
        server_socket.listen(5)
        print("Server 已啟動，監聽 127.0.0.1:1333")
        
        # 設置超時，避免無限等待
        server_socket.settimeout(30)
        
        while True:
            try:
                print("等待客戶端連接...")
                client_socket, client_address = server_socket.accept()
                print(f"客戶端已連接: {client_address}")
                
                # 處理客戶端數據
                def handle_client(sock, addr):
                    try:
                        data_count = 0
                        while True:
                            data = sock.recv(1024)
                            if not data:
                                break
                            data_count += 1
                            if data_count % 100 == 0:
                                print(f"從 {addr} 收到 {data_count} 個數據包")
                    except Exception as e:
                        print(f"處理客戶端 {addr} 時發生錯誤: {e}")
                    finally:
                        sock.close()
                        print(f"客戶端 {addr} 已斷開")
                
                # 創建處理線程
                client_thread = threading.Thread(
                    target=handle_client, 
                    args=(client_socket, client_address),
                    daemon=True
                )
                client_thread.start()
                
            except socket.timeout:
                print("等待超時，結束測試")
                break
            except KeyboardInterrupt:
                print("收到中斷信號")
                break
                
    except Exception as e:
        print(f"Server 錯誤: {e}")
    finally:
        server_socket.close()
        print("Server 已關閉")

def simple_client_test():
    """簡單的 socket client 測試"""
    print("開始簡單的 Socket Client 測試...")
    
    try:
        # 連接到 server
        client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        client_socket.connect(('127.0.0.1', 1333))
        print("已連接到 Server")
        
        # 發送測試數據
        for i in range(500):
            test_data = f"TestData_{i:04d}_" + "X" * 50
            client_socket.send(test_data.encode())
            if (i + 1) % 100 == 0:
                print(f"已發送 {i + 1} 個數據包")
            time.sleep(0.01)  # 10ms 間隔
            
    except Exception as e:
        print(f"Client 錯誤: {e}")
    finally:
        client_socket.close()
        print("Client 已斷開")

def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='簡單 Socket 測試')
    parser.add_argument('--mode', choices=['server', 'client'], default='server',
                       help='運行模式')
    
    args = parser.parse_args()
    
    if args.mode == 'server':
        simple_server_test()
    else:
        simple_client_test()

if __name__ == '__main__':
    main()
