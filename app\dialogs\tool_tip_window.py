from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *


class Ui_Tooltip_label(QLabel):
    """獨立的 Tooltip 視窗"""
    def __init__(self, parent=None):
        super().__init__("", parent)
        self.setStyleSheet("""
            background-color: #5448B6;
            padding: 4px;
            color: #73FFD1;
            font-size: 12px;
        """)
        self.setWindowFlags(Qt.ToolTip)  # 讓 Tooltip 獨立顯示
        self.hide()

    def show_tooltip(self, message, pos):
        """顯示 Tooltip，並移動到指定位置"""
        self.setText(message)
        self.adjustSize()
        self.move(pos + QPoint(10, 5))  # 讓 Tooltip 在按鈕下方
        self.show()

class Ui_Tooltip_label_up(QLabel):
    """獨立的 Tooltip 視窗 上"""
    def __init__(self, parent=None):
        super().__init__("", parent)
        self.setStyleSheet("""
            background-color: #5448B6;
            padding: 4px;
            color: #73FFD1;
            font-size: 12px;
        """)
        self.setWindowFlags(Qt.ToolTip)  # 讓 Tooltip 獨立顯示
        self.hide()

    def show_tooltip(self, message, pos):
        """顯示 Tooltip，並移動到指定位置"""
        self.setText(message)
        self.adjustSize()
        self.move(pos + QPoint(10, -50))  # 讓 Tooltip 在按鈕上方
        self.show()