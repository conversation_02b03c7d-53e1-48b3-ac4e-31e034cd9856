# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'CNCStatus_window.ui'
##
## Created by: Qt User Interface Compiler version 5.15.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *


class Ui_CNCStatus_window(object):
    def setupUi(self, CNCStatus_window):
        if not CNCStatus_window.objectName():
            CNCStatus_window.setObjectName(u"CNCStatus_window")
        CNCStatus_window.resize(200, 59)
        CNCStatus_window.setMinimumSize(QSize(200, 20))
        CNCStatus_window.setMaximumSize(QSize(200, 120))
        CNCStatus_window.setStyleSheet(u"QWidget#CNCStatus_window{\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"}")
        self.verticalLayout = QVBoxLayout(CNCStatus_window)
        self.verticalLayout.setSpacing(0)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(3, 3, 3, 6)
        self.title_group = QWidget(CNCStatus_window)
        self.title_group.setObjectName(u"title_group")
        sizePolicy = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.title_group.sizePolicy().hasHeightForWidth())
        self.title_group.setSizePolicy(sizePolicy)
        self.horizontalLayout = QHBoxLayout(self.title_group)
        self.horizontalLayout.setSpacing(2)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(6, 0, 3, 0)
        self.expand_btn = QPushButton(self.title_group)
        self.expand_btn.setObjectName(u"expand_btn")
        sizePolicy1 = QSizePolicy(QSizePolicy.Maximum, QSizePolicy.Maximum)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.expand_btn.sizePolicy().hasHeightForWidth())
        self.expand_btn.setSizePolicy(sizePolicy1)
        self.expand_btn.setMinimumSize(QSize(14, 14))
        self.expand_btn.setMaximumSize(QSize(14, 14))
        font = QFont()
        font.setFamily(u"Arial")
        font.setPointSize(12)
        font.setBold(False)
        font.setWeight(50)
        self.expand_btn.setFont(font)
        self.expand_btn.setCursor(QCursor(Qt.ArrowCursor))
        self.expand_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 255, 255);\n"
"    background-color: #0C0C44;\n"
"    border: None;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: #0C0C44;\n"
"	border: None;\n"
"    color: #7AFEC6;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    color: gray;\n"
"	border: none;\n"
"}")
        self.expand_btn.setCheckable(True)
        self.expand_btn.setFlat(False)

        self.horizontalLayout.addWidget(self.expand_btn)

        self.title_label = QLabel(self.title_group)
        self.title_label.setObjectName(u"title_label")
        sizePolicy.setHeightForWidth(self.title_label.sizePolicy().hasHeightForWidth())
        self.title_label.setSizePolicy(sizePolicy)
        font1 = QFont()
        font1.setFamily(u"Arial")
        font1.setPointSize(10)
        self.title_label.setFont(font1)
        self.title_label.setStyleSheet(u"color: rgb(255, 255, 255);\n"
"")
        self.title_label.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)
        self.title_label.setMargin(4)

        self.horizontalLayout.addWidget(self.title_label)

        self.cancel_btn = QPushButton(self.title_group)
        self.cancel_btn.setObjectName(u"cancel_btn")
        sizePolicy2 = QSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.cancel_btn.sizePolicy().hasHeightForWidth())
        self.cancel_btn.setSizePolicy(sizePolicy2)
        self.cancel_btn.setMinimumSize(QSize(20, 20))
        self.cancel_btn.setMaximumSize(QSize(20, 20))
        font2 = QFont()
        font2.setFamily(u"Consolas")
        font2.setPointSize(12)
        font2.setStyleStrategy(QFont.PreferDefault)
        self.cancel_btn.setFont(font2)
        self.cancel_btn.setStyleSheet(u"QPushButton {\n"
"    color: rgb(255, 0, 0);\n"
"    background-color: #0C0C44;\n"
"    border: None;\n"
"    margin-top: 4px;\n"
"    margin-left: 2px;\n"
"    margin-right: 2px; \n"
"    padding-bottom:4px;\n"
"}\n"
"\n"
"QPushButton:hover {\n"
"    background-color: red;\n"
"	border: None;\n"
"    color: #0C0C44;\n"
"}\n"
"\n"
"QPushButton:pressed {\n"
"    color: gray;\n"
"	border: none;\n"
"}")
        self.cancel_btn.setIconSize(QSize(20, 20))

        self.horizontalLayout.addWidget(self.cancel_btn, 0, Qt.AlignVCenter)


        self.verticalLayout.addWidget(self.title_group)

        self.status_group = QWidget(CNCStatus_window)
        self.status_group.setObjectName(u"status_group")
        sizePolicy3 = QSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        sizePolicy3.setHorizontalStretch(0)
        sizePolicy3.setVerticalStretch(0)
        sizePolicy3.setHeightForWidth(self.status_group.sizePolicy().hasHeightForWidth())
        self.status_group.setSizePolicy(sizePolicy3)
        self.status_group.setMinimumSize(QSize(190, 0))
        self.status_group.setMaximumSize(QSize(190, 16777215))
        self.horizontalLayout_2 = QHBoxLayout(self.status_group)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalLayout_2.setContentsMargins(0, 0, 0, 0)
        self.horizontalSpacer = QSpacerItem(10, 10, QSizePolicy.Maximum, QSizePolicy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer)

        self.status_text = QPlainTextEdit(self.status_group)
        self.status_text.setObjectName(u"status_text")
        sizePolicy4 = QSizePolicy(QSizePolicy.Preferred, QSizePolicy.Preferred)
        sizePolicy4.setHorizontalStretch(0)
        sizePolicy4.setVerticalStretch(0)
        sizePolicy4.setHeightForWidth(self.status_text.sizePolicy().hasHeightForWidth())
        self.status_text.setSizePolicy(sizePolicy4)
        self.status_text.setMinimumSize(QSize(160, 24))
        self.status_text.setMaximumSize(QSize(160, 100))
        self.status_text.setFont(font1)
        self.status_text.setStyleSheet(u"color:white;\n"
"background-color: #0C0C44;\n"
"border-top: 1px solid #5448B6;\n"
"vertical-align: text-top;\n"
"padding: 0px;\n"
"margin: 0px;")
        self.status_text.setInputMethodHints(Qt.ImhNone)
        self.status_text.setFrameShape(QFrame.NoFrame)
        self.status_text.setFrameShadow(QFrame.Plain)
        self.status_text.setLineWidth(0)
        self.status_text.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.status_text.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.status_text.setSizeAdjustPolicy(QAbstractScrollArea.AdjustToContents)
        self.status_text.setReadOnly(True)
        self.status_text.setTextInteractionFlags(Qt.NoTextInteraction)

        self.horizontalLayout_2.addWidget(self.status_text)


        self.verticalLayout.addWidget(self.status_group)


        self.retranslateUi(CNCStatus_window)

        QMetaObject.connectSlotsByName(CNCStatus_window)
    # setupUi

    def retranslateUi(self, CNCStatus_window):
        CNCStatus_window.setWindowTitle(QCoreApplication.translate("CNCStatus_window", u"Form", None))
        self.expand_btn.setText(QCoreApplication.translate("CNCStatus_window", u"+", None))
        self.title_label.setText(QCoreApplication.translate("CNCStatus_window", u"CNC\uff1a", None))
        self.cancel_btn.setText(QCoreApplication.translate("CNCStatus_window", u"x", None))
        self.status_text.setPlainText(QCoreApplication.translate("CNCStatus_window", u"[\u6a5f\u53f0\u72c0\u614b]\n"
"\u4e3b\u8ef8\u8f49\u901f\uff1a\n"
"\u5200\u628a\u7de8\u865f\uff1a", None))
    # retranslateUi

