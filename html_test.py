from PySide2.QtWidgets import QApplication, QMainWindow
from PySide2.QtWebEngineWidgets import QWebEngineView
from PySide2.QtCore import QUrl
import sys
import os

app = QApplication(sys.argv)
window = QMainWindow()

# 取得 HTML 檔案的完整絕對路徑
html_path = os.path.abspath("index.html")
url = QUrl.fromLocalFile(html_path)  # 正確轉換為 QUrl 格式

browser = QWebEngineView()
browser.setUrl(url)  # 載入本地 HTML 檔案

window.setCentralWidget(browser)
window.show()
sys.exit(app.exec_())
