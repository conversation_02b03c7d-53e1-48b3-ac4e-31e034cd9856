# UI 注意事項

1.ui打包為py時QScrollArea需要重新編寫為以下方式

2.非主視窗需再view.py加入下述，使小視窗不顯示邊框

``` python
def __init__(self):
	self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint) #小視窗不顯示邊框
```

## 1.main_window.py

``` python
self.scrollArea.setStyleSheet("QScrollArea {background: black; border: none;}")

# 設定 QScrollBar 樣式 (分開設定)

self.scrollArea.verticalScrollBar().setStyleSheet("""

QScrollBar:vertical {
background: black;
width: 2px;
border: none;
margin: 0px 0px 0px 0px;
}

QScrollBar::handle:vertical {
background: #7AFEC6;
min-height: 20px;
border-radius: 3px;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
background: none;
height: 0px;
}
QScrollBar::sub-page:vertical, QScrollBar::add-page:vertical {
background: black;
}

""")

self.scrollArea.horizontalScrollBar().setStyleSheet("""

QScrollBar:horizontal {
background: black;
height: 2px;
border: none;
margin: 0px 0px 0px 0px;
}

QScrollBar::handle:horizontal {
background: #7AFEC6;
min-width: 20px;
border-radius: 3px;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
background: none;
width: 0px;
}

QScrollBar::sub-page:horizontal, QScrollBar::add-page:horizontal {
background: black;
}

""")
```

## 2.ToolList_window.py
🚨🚨 重點: 有改動態生成 UI.py 檔，如果要修改 UI編譯後需要再與先前.py檔案做對應調整
``` python


self.scrollArea.setStyleSheet("QScrollArea {background: #0C0C44;  border: 2px solid #5448B6;}")

# 設定 QScrollBar 樣式 (分開設定)

self.scrollArea.verticalScrollBar().setStyleSheet("""
                                                          
QScrollBar:vertical {
background: #0C0C44;
width: 8px;
border: none;
margin: 0px 0px 0px 0px;
}
QScrollBar::handle:vertical {
background: #7AFEC6;
min-height: 20px;
border-radius: 3px;
}
QScrollBar::add-line:vertical, 
QScrollBar::sub-line:vertical {
background: none;
height: 0px;
}
QScrollBar::sub-page:vertical, 
QScrollBar::add-page:vertical {
background: #0C0C44;
}
                                                          
""")

self.scrollArea.horizontalScrollBar().setStyleSheet("""

QScrollBar:horizontal {
background: #0C0C44;
height: 2px;
border: none;
margin: 0px 0px 0px 0px;
}
QScrollBar::handle:horizontal {
background: #7AFEC6;
min-width: 20px;
border-radius: 3px;
}
QScrollBar::add-line:horizontal, 
QScrollBar::sub-line:horizontal {
background: none;
width: 0px;
}
QScrollBar::sub-page:horizontal, 
QScrollBar::add-page:horizontal {
background: #0C0C44;
}

""")
```
## 3.message_box.py
🚨🚨 給對話框做使用，如Confirm_window.py
