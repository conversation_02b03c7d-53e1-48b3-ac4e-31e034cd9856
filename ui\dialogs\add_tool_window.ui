<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>add_tool_window</class>
 <widget class="QWidget" name="add_tool_window">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>399</width>
    <height>59</height>
   </rect>
  </property>
  <property name="maximumSize">
   <size>
    <width>399</width>
    <height>59</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QWidget#add_tool_window{
    background-color: #0C0C44;
    border: 2px solid #5448B6;
}</string>
  </property>
  <layout class="QGridLayout" name="gridLayout" columnstretch="2,5,1,1">
   <property name="leftMargin">
    <number>9</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>9</number>
   </property>
   <property name="bottomMargin">
    <number>5</number>
   </property>
   <property name="horizontalSpacing">
    <number>6</number>
   </property>
   <property name="verticalSpacing">
    <number>2</number>
   </property>
   <item row="0" column="0">
    <widget class="QLabel" name="mac_label">
     <property name="font">
      <font>
       <family>Arial Black</family>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">color: rgb(255, 255, 255);</string>
     </property>
     <property name="text">
      <string>MAC : </string>
     </property>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QLineEdit" name="mac_lineEdit">
     <property name="styleSheet">
      <string notr="true">color: rgb(0, 0, 0);</string>
     </property>
    </widget>
   </item>
   <item row="0" column="2">
    <widget class="QPushButton" name="add_cancel_Btn">
     <property name="font">
      <font>
       <family>Arial Black</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color: #0C0C44;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color: #0C0C44;
	border: 2px solid #7AFEC6;
    color: rgb(255, 255, 255);
	border-radius:5px;
	padding:2px;
}

QPushButton:pressed {
    background-color: gray;
	border: none;
}</string>
     </property>
     <property name="text">
      <string>取消</string>
     </property>
     <property name="iconSize">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="0" column="3">
    <widget class="QPushButton" name="add_save_Btn">
     <property name="font">
      <font>
       <family>Arial Black</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton {
    color: rgb(255, 255, 255);
    background-color:#5448B6;
    border: 2px solid #5448B6;
	border-radius:5px;
	padding:2px;
}

QPushButton:hover {
    background-color:#7AFEC6;
     color: rgb(255, 255, 255);
    border:none
}

QPushButton:pressed {
    background-color: gray;
	border:none
}</string>
     </property>
     <property name="text">
      <string notr="true">新增</string>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
